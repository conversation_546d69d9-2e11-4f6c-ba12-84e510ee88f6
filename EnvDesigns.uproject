{"FileVersion": 3, "EngineAssociation": "5.6", "Category": "", "Description": "", "Plugins": [{"Name": "ModelingToolsEditorMode", "Enabled": true, "TargetAllowList": ["Editor"]}, {"Name": "OnlineSubsystemGooglePlay", "Enabled": false, "SupportedTargetPlatforms": ["Android"]}, {"Name": "AndroidPermission", "Enabled": false}, {"Name": "AndroidFileServer", "Enabled": false}, {"Name": "GooglePAD", "Enabled": false}, {"Name": "MotionWarping", "Enabled": true}, {"Name": "JsonBlueprintUtilities", "Enabled": true}, {"Name": "AndroidDeviceProfileSelector", "Enabled": false}, {"Name": "IOSDeviceProfileSelector", "Enabled": false}, {"Name": "PCG", "Enabled": true}, {"Name": "AppleImageUtils", "Enabled": false}, {"Name": "AbilitySystemGameFeatureActions", "Enabled": true}, {"Name": "GameplayAbilities", "Enabled": true}, {"Name": "GameplayInteractions", "Enabled": true}, {"Name": "GameplayStateTree", "Enabled": true}, {"Name": "DatasmithContent", "Enabled": false}, {"Name": "Water", "Enabled": true}, {"Name": "GitSourceControl", "Enabled": false}, {"Name": "PlasticSourceControl", "Enabled": false}, {"Name": "SubversionSourceControl", "Enabled": false}, {"Name": "HDRIBackdrop", "Enabled": true}, {"Name": "XCodeSourceCodeAccess", "Enabled": false, "SupportedTargetPlatforms": ["<PERSON>"]}, {"Name": "KDevelopSourceCodeAccess", "Enabled": false}, {"Name": "CodeLiteSourceCodeAccess", "Enabled": false}, {"Name": "CLionSourceCodeAccess", "Enabled": false}, {"Name": "Iris", "Enabled": false}, {"Name": "LocationServicesBPLibrary", "Enabled": false}, {"Name": "MobilePatchingUtils", "Enabled": false}, {"Name": "MacGraphicsSwitching", "Enabled": false}, {"Name": "MobileLauncherProfileWizard", "Enabled": false}, {"Name": "HDRIBackdrop", "Enabled": true}, {"Name": "MovieRenderPipeline", "Enabled": true}, {"Name": "MoviePipelineMaskRenderPass", "Enabled": true}]}