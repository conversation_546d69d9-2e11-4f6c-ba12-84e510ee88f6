﻿Log file open, 09/15/25 13:58:21
LogWindows: Failed to load 'aqProf.dll' (GetLastError=126)
LogWindows: File 'aqProf.dll' does not exist
LogProfilingDebugging: Loading WinPixEventRuntime.dll for PIX profiling (from ../../../Engine/Binaries/ThirdParty/Windows/WinPixEventRuntime/x64).
LogWindows: Failed to load 'VtuneApi.dll' (GetLastError=126)
LogWindows: File 'VtuneApi.dll' does not exist
LogWindows: Failed to load 'VtuneApi32e.dll' (GetLastError=126)
LogWindows: File 'VtuneApi32e.dll' does not exist
LogWindows: Started CrashReportClient (pid=30080)
LogWindows: Enabling Tpause support
LogWindows: Custom abort handler registered for crash reporting.
LogInit: Display: Running engine for game: EnvDesigns
LogCore: UTS: Unreal Trace Server launched successfully
LogTrace: Initializing trace...
LogCore: Display: Requested channels: 'cpu,gpu,frame,log,bookmark,screenshot,region'
LogTrace: Display: Control listening on port 35403
LogTrace: Finished trace initialization.
LogCsvProfiler: Display: Metadata set : platform="Windows"
LogCsvProfiler: Display: Metadata set : config="Development"
LogCsvProfiler: Display: Metadata set : buildversion="++UE5+Release-5.6-***********"
LogCsvProfiler: Display: Metadata set : engineversion="5.6.1-44394996+++UE5+Release-5.6"
LogCsvProfiler: Display: Metadata set : os="Windows 11 (24H2) [10.0.26100.4061] "
LogCsvProfiler: Display: Metadata set : cpu="GenuineIntel|13th Gen Intel(R) Core(TM) i5-1345U"
LogCsvProfiler: Display: Metadata set : pgoenabled="0"
LogCsvProfiler: Display: Metadata set : pgoprofilingenabled="0"
LogCsvProfiler: Display: Metadata set : ltoenabled="0"
LogCsvProfiler: Display: Metadata set : asan="0"
LogCsvProfiler: Display: Metadata set : commandline="" C:/Auracron/EnvDesigns.uproject""
LogCsvProfiler: Display: Metadata set : loginid="b4f508ed4bd797d4247ed9a748e6dcab"
LogCsvProfiler: Display: Metadata set : llm="0"
LogStats: Stats thread started at 0.269597
LogModuleManager: Skipping out-of-date modules in manifest '../../../../../../Auracron/Binaries/Win64/UnrealEditor.modules' (BuildId 23058290 != 43139311):
LogModuleManager:     Skipping module '../../../../../../Auracron/Binaries/Win64/UnrealEditor-EnvDesigns.dll'.
LogCsvProfiler: Display: Metadata set : systemresolution.resx="1280"
LogCsvProfiler: Display: Metadata set : systemresolution.resy="720"
LogICUInternationalization: ICU TimeZone Detection - Raw Offset: -3:00, Platform Override: ''
LogInit: Session CrashGUID >====================================================
         Session CrashGUID >   UECC-Windows-5496AEDE415EC473D1028F8D44ABACA6
         Session CrashGUID >====================================================
LogConfig: No local boot hotfix file found at: [../../../../../../Auracron/Saved/PersistentDownloadDir/HotfixForNextBoot.txt]
LogAudio: Display: Pre-Initializing Audio Device Manager...
LogAudio: Display: AudioInfo: 'OPUS' Registered
LogAudioDebug: Display: Lib vorbis DLL was dynamically loaded.
LogAudio: Display: AudioInfo: 'OGG' Registered
LogAudio: Display: AudioInfo: 'ADPCM' Registered
LogAudio: Display: AudioInfo: 'PCM' Registered
LogAudio: Display: AudioInfo: 'BINKA' Registered
LogAudio: Display: AudioInfo: 'RADA' Registered
LogAudio: Display: Audio Device Manager Pre-Initialized
LogPluginManager: Looking for build plugins target receipt
LogAssetRegistry: Display: PlatformFileJournal journal has wrapped for volume 'C:'. AssetRegistry discovery of files on this volume will be uncached. Notes on wrapping:
NTFS Journal has wrapped for volume 'C:'; the number of files written to this volume exceeded the buffer size of the journal and some entries were dropped.
If this happens frequently increase the buffer size of the journal:
Launch cmd.exe as admin and run command `fsutil usn createJournal C: m=<SizeInBytes>`
<SizeInBytes> defaults to 33554432 (32MB), Recommended <SizeInBytes> is 0x40000000 (1GB). You can see the current value via `fsutil usn queryJournal C:`, 'Maximum Size' field.
LogConfig: Display: Loading VulkanPC ini files took 0.05 seconds
LogPluginManager: Found matching target receipt: ../../../../../../Auracron/Binaries/Win64/EnvDesignsEditor.target
LogPluginManager: Looking for enabled plugins target receipt
LogConfig: Display: Loading Mac ini files took 0.06 seconds
LogConfig: Display: Loading Android ini files took 0.06 seconds
LogConfig: Display: Loading IOS ini files took 0.06 seconds
LogPluginManager: Found matching target receipt: ../../../../../../Auracron/Binaries/Win64/EnvDesignsEditor.target
LogConfig: Display: Loading Unix ini files took 0.08 seconds
LogConfig: Display: Loading TVOS ini files took 0.08 seconds
LogConfig: Display: Loading Windows ini files took 0.08 seconds
LogConfig: Display: Loading Linux ini files took 0.09 seconds
LogPluginManager: Mounting Engine plugin Bridge
LogPluginManager: Mounting Engine plugin ChaosCloth
LogPluginManager: Mounting Engine plugin ChaosClothAsset
LogPluginManager: Mounting Engine plugin ChaosInsights
LogPluginManager: Mounting Engine plugin ChaosVD
LogPluginManager: Mounting Engine plugin CmdLinkServer
LogPluginManager: Mounting Engine plugin EnhancedInput
LogPluginManager: Mounting Engine plugin FastBuildController
LogPluginManager: Mounting Engine plugin Fab
LogPluginManager: Mounting Engine plugin IoStoreInsights
LogPluginManager: Mounting Engine plugin JsonBlueprintUtilities
LogPluginManager: Mounting Engine plugin MassInsights
LogConfig: Display: Loading VisionOS ini files took 0.04 seconds
LogPluginManager: Mounting Engine plugin MeshPainting
LogPluginManager: Mounting Engine plugin PCG
LogPluginManager: Mounting Engine plugin RenderGraphInsights
LogPluginManager: Mounting Engine plugin TraceUtilities
LogPluginManager: Mounting Engine plugin UbaController
LogPluginManager: Mounting Engine plugin WorldMetrics
LogPluginManager: Mounting Engine plugin XGEController
LogPluginManager: Mounting Engine plugin AndroidMedia
LogPluginManager: Mounting Engine plugin AvfMedia
LogPluginManager: Mounting Engine plugin ImgMedia
LogPluginManager: Mounting Engine plugin MediaCompositing
LogPluginManager: Mounting Engine plugin MediaPlate
LogPluginManager: Mounting Engine plugin MediaPlayerEditor
LogPluginManager: Mounting Engine plugin WmfMedia
LogPluginManager: Mounting Engine plugin WebMMedia
LogPluginManager: Mounting Engine plugin MetaHumanSDK
LogPluginManager: Mounting Engine plugin EOSShared
LogPluginManager: Mounting Engine plugin OnlineServices
LogPluginManager: Mounting Engine plugin OnlineBase
LogPluginManager: Mounting Engine plugin OnlineSubsystem
LogPluginManager: Mounting Engine plugin OnlineSubsystemNull
LogPluginManager: Mounting Engine plugin OnlineSubsystemUtils
LogPluginManager: Mounting Engine plugin ActorLayerUtilities
LogPluginManager: Mounting Engine plugin AndroidMoviePlayer
LogPluginManager: Mounting Engine plugin AppleMoviePlayer
LogPluginManager: Mounting Engine plugin AudioCapture
LogPluginManager: Mounting Engine plugin ArchVisCharacter
LogPluginManager: Mounting Engine plugin AssetTags
LogPluginManager: Mounting Engine plugin CableComponent
LogPluginManager: Mounting Engine plugin AudioSynesthesia
LogPluginManager: Mounting Engine plugin AudioWidgets
LogPluginManager: Mounting Engine plugin ChunkDownloader
LogPluginManager: Mounting Engine plugin CustomMeshComponent
LogPluginManager: Mounting Engine plugin ComputeFramework
LogPluginManager: Mounting Engine plugin DataRegistry
LogPluginManager: Mounting Engine plugin GameFeatures
LogPluginManager: Mounting Engine plugin ExampleDeviceProfileSelector
LogPluginManager: Mounting Engine plugin GameplayAbilities
LogPluginManager: Mounting Engine plugin GameplayStateTree
LogPluginManager: Mounting Engine plugin GameplayInteractions
LogPluginManager: Mounting Engine plugin GeometryProcessing
LogPluginManager: Mounting Engine plugin GeometryCache
LogPluginManager: Mounting Engine plugin GoogleCloudMessaging
LogPluginManager: Mounting Engine plugin HairStrands
LogPluginManager: Mounting Engine plugin HDRIBackdrop
LogPluginManager: Mounting Engine plugin InputDebugging
LogPluginManager: Mounting Engine plugin LinuxDeviceProfileSelector
LogPluginManager: Mounting Engine plugin MeshModelingToolset
LogPluginManager: Mounting Engine plugin Metasound
LogPluginManager: Mounting Engine plugin ModularGameplay
LogPluginManager: Mounting Engine plugin MsQuic
LogPluginManager: Mounting Engine plugin NavCorridor
LogPluginManager: Mounting Engine plugin PropertyAccessEditor
LogAssetRegistry: Display: Asset registry cache read as 94.2 MiB from ../../../../../../Auracron/Intermediate/CachedAssetRegistry_0.bin.
LogPluginManager: Mounting Engine plugin PropertyBindingUtils
LogPluginManager: Mounting Engine plugin ProceduralMeshComponent
LogPluginManager: Mounting Engine plugin SignificanceManager
LogPluginManager: Mounting Engine plugin ResonanceAudio
LogPluginManager: Mounting Engine plugin RigVM
LogPluginManager: Mounting Engine plugin SmartObjects
LogPluginManager: Mounting Engine plugin SoundFields
LogPluginManager: Mounting Engine plugin StateTree
LogPluginManager: Mounting Engine plugin Synthesis
LogPluginManager: Mounting Engine plugin WaveTable
LogPluginManager: Mounting Engine plugin WindowsMoviePlayer
LogPluginManager: Mounting Engine plugin WorldConditions
LogPluginManager: Mounting Engine plugin WindowsDeviceProfileSelector
LogPluginManager: Mounting Engine plugin WebMMoviePlayer
LogPluginManager: Mounting Engine plugin Paper2D
LogPluginManager: Mounting Engine plugin AISupport
LogPluginManager: Mounting Engine plugin EnvironmentQueryEditor
LogPluginManager: Mounting Engine plugin AnimationData
LogPluginManager: Mounting Engine plugin ACLPlugin
LogPluginManager: Mounting Engine plugin AnimationModifierLibrary
LogPluginManager: Mounting Engine plugin BlendSpaceMotionAnalysis
LogPluginManager: Mounting Engine plugin ControlRigModules
LogPluginManager: Mounting Engine plugin ControlRig
LogPluginManager: Mounting Engine plugin IKRig
LogPluginManager: Mounting Engine plugin ControlRigSpline
LogPluginManager: Mounting Engine plugin DeformerGraph
LogPluginManager: Mounting Engine plugin GameplayInsights
LogPluginManager: Mounting Engine plugin MotionWarping
LogPluginManager: Mounting Engine plugin SkeletalMeshModelingTools
LogPluginManager: Mounting Engine plugin TweeningUtils
LogPluginManager: Mounting Engine plugin RigLogic
LogPluginManager: Mounting Engine plugin EngineCameras
LogPluginManager: Mounting Engine plugin CameraShakePreviewer
LogPluginManager: Mounting Engine plugin GameplayCameras
LogPluginManager: Mounting Engine plugin OpenColorIO
LogPluginManager: Mounting Engine plugin OodleNetwork
LogPluginManager: Mounting Engine plugin AnimationSharing
LogPluginManager: Mounting Engine plugin NullSourceCodeAccess
LogPluginManager: Mounting Engine plugin DumpGPUServices
LogPluginManager: Mounting Engine plugin PerforceSourceControl
LogPluginManager: Mounting Engine plugin PixWinPlugin
LogPluginManager: Mounting Engine plugin N10XSourceCodeAccess
LogPluginManager: Mounting Engine plugin ProjectLauncher
LogPluginManager: Mounting Engine plugin NamingTokens
LogPluginManager: Mounting Engine plugin PropertyAccessNode
LogPluginManager: Mounting Engine plugin PluginUtils
LogPluginManager: Mounting Engine plugin RenderDocPlugin
LogPluginManager: Mounting Engine plugin RiderSourceCodeAccess
LogPluginManager: Mounting Engine plugin VisualStudioCodeSourceCodeAccess
LogPluginManager: Mounting Engine plugin VisualStudioSourceCodeAccess
LogPluginManager: Mounting Engine plugin TextureFormatOodle
LogPluginManager: Mounting Engine plugin AssetManagerEditor
LogPluginManager: Mounting Engine plugin AssetReferenceRestrictions
LogPluginManager: Mounting Engine plugin UObjectPlugin
LogPluginManager: Mounting Engine plugin BlueprintMaterialTextureNodes
LogPluginManager: Mounting Engine plugin BlueprintHeaderView
LogPluginManager: Mounting Engine plugin CryptoKeys
LogPluginManager: Mounting Engine plugin ChangelistReview
LogPluginManager: Mounting Engine plugin ColorGrading
LogPluginManager: Mounting Engine plugin ConsoleVariables
LogPluginManager: Mounting Engine plugin EditorDebugTools
LogPluginManager: Mounting Engine plugin DataValidation
LogPluginManager: Mounting Engine plugin CurveEditorTools
LogPluginManager: Mounting Engine plugin FacialAnimation
LogPluginManager: Mounting Engine plugin GameplayTagsEditor
LogPluginManager: Mounting Engine plugin EditorScriptingUtilities
LogPluginManager: Mounting Engine plugin EngineAssetDefinitions
LogPluginManager: Mounting Engine plugin GeometryMode
LogPluginManager: Mounting Engine plugin MeshLODToolset
LogPluginManager: Mounting Engine plugin MaterialAnalyzer
LogPluginManager: Mounting Engine plugin ModelingToolsEditorMode
LogPluginManager: Mounting Engine plugin PluginBrowser
LogPluginManager: Mounting Engine plugin ProxyLODPlugin
LogPluginManager: Mounting Engine plugin SequencerAnimTools
LogPluginManager: Mounting Engine plugin SpeedTreeImporter
LogPluginManager: Mounting Engine plugin UMGWidgetPreview
LogPluginManager: Mounting Engine plugin StylusInput
LogPluginManager: Mounting Engine plugin UVEditor
LogPluginManager: Mounting Engine plugin WorldPartitionHLODUtilities
LogPluginManager: Mounting Engine plugin GLTFExporter
LogPluginManager: Mounting Engine plugin VariantManagerContent
LogPluginManager: Mounting Engine plugin VariantManager
LogPluginManager: Mounting Engine plugin AbilitySystemGameFeatureActions
LogPluginManager: Mounting Engine plugin AdvancedRenamer
LogPluginManager: Mounting Engine plugin AutomationUtils
LogPluginManager: Mounting Engine plugin BackChannel
LogPluginManager: Mounting Engine plugin ChaosCaching
LogPluginManager: Mounting Engine plugin ChaosEditor
LogPluginManager: Mounting Engine plugin ChaosSolverPlugin
LogPluginManager: Mounting Engine plugin ChaosNiagara
LogPluginManager: Mounting Engine plugin CharacterAI
LogPluginManager: Mounting Engine plugin ChaosUserDataPT
LogPluginManager: Mounting Engine plugin Dataflow
LogPluginManager: Mounting Engine plugin EditorDataStorage
LogPluginManager: Mounting Engine plugin EditorDataStorageFeatures
LogPluginManager: Mounting Engine plugin EditorPerformance
LogPluginManager: Mounting Engine plugin EditorTelemetry
LogPluginManager: Mounting Engine plugin TargetingSystem
LogPluginManager: Mounting Engine plugin Fracture
LogPluginManager: Mounting Engine plugin FullBodyIK
LogPluginManager: Mounting Engine plugin GeometryCollectionPlugin
LogPluginManager: Mounting Engine plugin GeometryDataflow
LogPluginManager: Mounting Engine plugin GeometryFlow
LogPluginManager: Mounting Engine plugin Landmass
LogPluginManager: Mounting Engine plugin LevelSequenceNavigatorBridge
LogPluginManager: Mounting Engine plugin LowLevelNetTrace
LogPluginManager: Mounting Engine plugin LocalizableMessage
LogPluginManager: Mounting Engine plugin MeshModelingToolsetExp
LogPluginManager: Mounting Engine plugin NFORDenoise
LogPluginManager: Mounting Engine plugin PlanarCut
LogPluginManager: Mounting Engine plugin PlatformCrypto
LogPluginManager: Mounting Engine plugin PythonScriptPlugin
LogPluginManager: Mounting Engine plugin RuntimeTelemetry
LogPluginManager: Mounting Engine plugin SkeletalReduction
LogPluginManager: Mounting Engine plugin SequenceNavigator
LogPluginManager: Mounting Engine plugin ToolPresets
LogPluginManager: Mounting Engine plugin Water
LogPluginManager: Mounting Engine plugin Cascade
LogPluginManager: Mounting Engine plugin Niagara
LogPluginManager: Mounting Engine plugin NiagaraSimCaching
LogPluginManager: Mounting Engine plugin AlembicImporter
LogPluginManager: Mounting Engine plugin InterchangeEditor
LogPluginManager: Mounting Engine plugin InterchangeAssets
LogPluginManager: Mounting Engine plugin Interchange
LogPluginManager: Mounting Engine plugin UdpMessaging
LogPluginManager: Mounting Engine plugin TcpMessaging
LogPluginManager: Mounting Engine plugin LevelSequenceEditor
LogPluginManager: Mounting Engine plugin ActorSequence
LogPluginManager: Mounting Engine plugin MovieRenderPipeline
LogPluginManager: Mounting Engine plugin MoviePipelineMaskRenderPass
LogPluginManager: Mounting Engine plugin SequencerScripting
LogPluginManager: Mounting Engine plugin TemplateSequence
LogPluginManager: Mounting Engine plugin NNERuntimeORT
LogPluginManager: Mounting Engine plugin LauncherChunkInstaller
LogPluginManager: Mounting Engine plugin NNEDenoiser
LogPluginManager: Mounting Engine plugin InterchangeTests
LogPluginManager: Mounting Engine plugin CameraCalibrationCore
LogPluginManager: Mounting Engine plugin Takes
LogPluginManager: Mounting Engine plugin ObjectMixer
LogPluginManager: Mounting Engine plugin LightMixer
LogPluginManager: Mounting Engine plugin XInputDevice
LogPluginManager: Mounting Engine plugin BaseCharacterFXEditor
LogPluginManager: Mounting Engine plugin ConcertMain
LogPluginManager: Mounting Engine plugin PortableObjectFileDataSource
LogPluginManager: Mounting Engine plugin ContentBrowserClassDataSource
LogPluginManager: Mounting Engine plugin ContentBrowserFileDataSource
LogPluginManager: Mounting Engine plugin ContentBrowserAssetDataSource
LogPluginManager: Mounting Engine plugin ContextualAnimation
LogPluginManager: Mounting Engine plugin CompositeCore
LogPluginManager: Mounting Engine plugin OnlineSubsystemIOS
LogPluginManager: Mounting Engine plugin SQLiteCore
LogPluginManager: Mounting Engine plugin ConcertSyncCore
LogPluginManager: Mounting Engine plugin ConcertSyncClient
LogPluginManager: Mounting Engine plugin ConcertSharedSlate
SourceControl: Revision control is disabled
SourceControl: Revision control is disabled
LogEOSShared: Loaded "C:/Program Files/Epic Games/UE_5.6/Engine/Binaries/Win64/EOSSDK-Win64-Shipping.dll"
LogEOSShared: FEOSSDKManager::Initialize Initializing EOSSDK Version:1.17.0-41373641
LogInit: Using libcurl 8.12.1
LogInit:  - built for Windows
LogInit:  - supports SSL with OpenSSL/1.1.1t
LogInit:  - supports HTTP deflate (compression) using libz 1.3
LogInit:  - other features:
LogInit:      CURL_VERSION_SSL
LogInit:      CURL_VERSION_LIBZ
LogInit:      CURL_VERSION_IPV6
LogInit:      CURL_VERSION_ASYNCHDNS
LogInit:      CURL_VERSION_LARGEFILE
LogInit:      CURL_VERSION_TLSAUTH_SRP
LogInit:      CURL_VERSION_HTTP2
LogInit:  CurlRequestOptions (configurable via config and command line):
LogInit:  - bVerifyPeer = true  - Libcurl will verify peer certificate
LogInit:  - bUseHttpProxy = false  - Libcurl will NOT use HTTP proxy
LogInit:  - bDontReuseConnections = false  - Libcurl will reuse connections
LogInit:  - MaxHostConnections = 16  - Libcurl will limit the number of connections to a host
LogInit:  - LocalHostAddr = Default
LogInit:  - BufferSize = 65536
LogInit: CreateHttpThread using FCurlMultiPollEventLoopHttpThread
LogInit: Creating http thread with maximum 256 concurrent requests
LogInit: WinSock: version 1.1 (2.2), MaxSocks=32767, MaxUdp=65467
LogOnline: OSS: Created online subsystem instance for: NULL
LogOnline: OSS: TryLoadSubsystemAndSetDefault: Loaded subsystem for type [NULL]
LogWindows: Failed to load 'WinPixGpuCapturer.dll' (GetLastError=126)
LogWindows: File 'WinPixGpuCapturer.dll' does not exist
PixWinPlugin: PIX capture plugin failed to initialize! Check that the process is launched from PIX.
LogConfig: Applying CVar settings from Section [/Script/RenderDocPlugin.RenderDocPluginSettings] File [Engine]
RenderDocPlugin: Display: RenderDoc plugin will not be loaded. Use '-AttachRenderDoc' on the cmd line or enable 'renderdoc.AutoAttach' in the plugin settings.
LogStudioTelemetry: Started StudioTelemetry Session
LogNFORDenoise: NFORDenoise function starting up
LogConfig: Applying CVar settings from Section [/Script/CompositeCore.CompositeCorePluginSettings] File [Engine]
LogInit: ExecutableName: UnrealEditor.exe
LogInit: Build: ++UE5+Release-5.6-***********
LogInit: Platform=WindowsEditor
LogInit: MachineId=b4f508ed4bd797d4247ed9a748e6dcab
LogInit: DeviceId=
LogInit: Engine Version: 5.6.1-44394996+++UE5+Release-5.6
LogInit: Compatible Engine Version: 5.6.0-43139311+++UE5+Release-5.6
LogInit: Net CL: 43139311
LogInit: OS: Windows 11 (24H2) [10.0.26100.4061] (), CPU: 13th Gen Intel(R) Core(TM) i5-1345U, GPU: Intel(R) Iris(R) Xe Graphics
LogInit: Compiled (64-bit): Jul 28 2025 20:53:34
LogInit: Architecture: x64
LogInit: Compiled with Visual C++: 19.38.33130.00
LogInit: Build Configuration: Development
LogInit: Branch Name: ++UE5+Release-5.6
LogInit: Command Line: 
LogInit: Base Directory: C:/Program Files/Epic Games/UE_5.6/Engine/Binaries/Win64/
LogInit: Allocator: Mimalloc
LogInit: Installed Engine Build: 1
LogInit: This binary is optimized with LTO: no, PGO: no, instrumented for PGO data collection: no
LogDevObjectVersion: Number of dev versions registered: 37
LogDevObjectVersion:   Dev-Blueprints (B0D832E4-1F89-4F0D-ACCF-7EB736FD4AA2): 10
LogDevObjectVersion:   Dev-Build (E1C64328-A22C-4D53-A36C-8E866417BD8C): 0
LogDevObjectVersion:   Dev-Core (375EC13C-06E4-48FB-B500-84F0262A717E): 4
LogDevObjectVersion:   Dev-Editor (E4B068ED-F494-42E9-A231-DA0B2E46BB41): 40
LogDevObjectVersion:   Dev-Framework (CFFC743F-43B0-4480-9391-14DF171D2073): 37
LogDevObjectVersion:   Dev-Mobile (B02B49B5-BB20-44E9-A304-32B752E40360): 3
LogDevObjectVersion:   Dev-Networking (A4E4105C-59A1-49B5-A7C5-40C4547EDFEE): 0
LogDevObjectVersion:   Dev-Online (39C831C9-5AE6-47DC-9A44-9C173E1C8E7C): 0
LogDevObjectVersion:   Dev-Physics (78F01B33-EBEA-4F98-B9B4-84EACCB95AA2): 20
LogDevObjectVersion:   Dev-Platform (6631380F-2D4D-43E0-8009-CF276956A95A): 0
LogDevObjectVersion:   Dev-Rendering (12F88B9F-8875-4AFC-A67C-D90C383ABD29): 49
LogDevObjectVersion:   Dev-Sequencer (7B5AE74C-D270-4C10-A958-57980B212A5A): 13
LogDevObjectVersion:   Dev-VR (D7296918-1DD6-4BDD-9DE2-64A83CC13884): 3
LogDevObjectVersion:   Dev-LoadTimes (C2A15278-BFE7-4AFE-6C17-90FF531DF755): 1
LogDevObjectVersion:   Private-Geometry (6EACA3D4-40EC-4CC1-B786-8BED09428FC5): 3
LogDevObjectVersion:   Dev-AnimPhys (29E575DD-E0A3-4627-9D10-D276232CDCEA): 17
LogDevObjectVersion:   Dev-Anim (AF43A65D-7FD3-4947-9873-3E8ED9C1BB05): 15
LogDevObjectVersion:   Dev-ReflectionCapture (6B266CEC-1EC7-4B8F-A30B-E4D90942FC07): 1
LogDevObjectVersion:   Dev-Automation (0DF73D61-A23F-47EA-B727-89E90C41499A): 1
LogDevObjectVersion:   FortniteMain (601D1886-AC64-4F84-AA16-D3DE0DEAC7D6): 207
LogDevObjectVersion:   FortniteValkyrie (8DBC2C5B-54A7-43E0-A768-FCBB7DA29060): 8
LogDevObjectVersion:   FortniteSeason (5B4C06B7-2463-4AF8-805B-BF70CDF5D0DD): 13
LogDevObjectVersion:   FortniteRelease (E7086368-6B23-4C58-8439-1B7016265E91): 17
LogDevObjectVersion:   Dev-Enterprise (9DFFBCD6-494F-0158-E221-12823C92A888): 11
LogDevObjectVersion:   Dev-Niagara (F2AED0AC-9AFE-416F-8664-AA7FFA26D6FC): 1
LogDevObjectVersion:   Dev-Destruction (174F1F0B-B4C6-45A5-B13F-2EE8D0FB917D): 10
LogDevObjectVersion:   Dev-Physics-Ext (35F94A83-E258-406C-A318-09F59610247C): 41
LogDevObjectVersion:   Dev-PhysicsMaterial-Chaos (B68FC16E-8B1B-42E2-B453-215C058844FE): 1
LogDevObjectVersion:   Dev-CineCamera (B2E18506-4273-CFC2-A54E-F4BB758BBA07): 1
LogDevObjectVersion:   Dev-VirtualProduction (64F58936-FD1B-42BA-BA96-7289D5D0FA4E): 1
LogDevObjectVersion:   UE5-Main (697DD581-E64F-41AB-AA4A-51ECBEB7B628): 121
LogDevObjectVersion:   UE5-Release (D89B5E42-24BD-4D46-8412-ACA8DF641779): 56
LogDevObjectVersion:   UE5-SpecialProject (59DA5D52-1232-4948-B878-597870B8E98B): 9
LogDevObjectVersion:   Dev-MediaFramework (6F0ED827-A609-4895-9C91-998D90180EA4): 2
LogDevObjectVersion:   Dev-NaniteResearch (30D58BE3-95EA-4282-A6E3-B159D8EBB06A): 1
LogDevObjectVersion:   Dev-ComputeFramework (6304A3E7-0059-4F59-8CFC-21BD7721FD4E): 0
LogDevObjectVersion:   LensFileVersion (8652A554-966A-466C-9FD7-1C6DD61B1ADB): 1
LogInit: Presizing for max 25165824 objects, including 0 objects not considered by GC.
LogInit: Object subsystem initialized
LogConfig: Set CVar [[con.DebugEarlyDefault:1]]
LogConfig: CVar [[con.DebugLateDefault:1]] deferred - dummy variable created
LogConfig: CVar [[con.DebugLateCheat:1]] deferred - dummy variable created
LogConfig: CVar [[LogNamedEventFilters:Frame *]] deferred - dummy variable created
LogConfig: Set CVar [[r.setres:1280x720]]
LogConfig: CVar [[framepro.ScopeMinTimeMicroseconds:10]] deferred - dummy variable created
LogConfig: Set CVar [[fx.NiagaraAllowRuntimeScalabilityChanges:1]]
LogConfig: CVar [[QualityLevelMapping:high]] deferred - dummy variable created
LogConfig: CVar [[r.Occlusion.SingleRHIThreadStall:1]] deferred - dummy variable created
LogConfig: Set CVar [[r.Nanite.Streaming.ReservedResources:1]]
LogConfig: CVar [[D3D12.Bindless.ResourceDescriptorHeapSize:32768]] deferred - dummy variable created
LogConfig: CVar [[D3D12.Bindless.SamplerDescriptorHeapSize:2048]] deferred - dummy variable created
LogConfig: Set CVar [[r.PSOPrecache.GlobalShaders:1]]
LogConfig: Set CVar [[r.VRS.EnableSoftware:1]]
LogConfig: Set CVar [[r.VRS.ContrastAdaptiveShading:1]]
[2025.09.15-16.58.22:298][  0]LogConfig: Set CVar [[r.VSync:0]]
[2025.09.15-16.58.22:298][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.RendererSettings] File [Engine]
[2025.09.15-16.58.22:298][  0]LogConfig: CVar [[VisualizeCalibrationColorMaterialPath:/Engine/EngineMaterials/PPM_DefaultCalibrationColor.PPM_DefaultCalibrationColor]] deferred - dummy variable created
[2025.09.15-16.58.22:298][  0]LogConfig: CVar [[VisualizeCalibrationGrayscaleMaterialPath:/Engine/EngineMaterials/PPM_DefaultCalibrationGrayscale.PPM_DefaultCalibrationGrayscale]] deferred - dummy variable created
[2025.09.15-16.58.22:298][  0]LogConfig: Set CVar [[r.GPUCrashDebugging:0]]
[2025.09.15-16.58.22:298][  0]LogConfig: CVar [[MaxSkinBones:(Default=65536,PerPlatform=(("Mobile", 256)))]] deferred - dummy variable created
[2025.09.15-16.58.22:298][  0]LogConfig: Set CVar [[r.Shaders.RemoveUnusedInterpolators:1]]
[2025.09.15-16.58.22:298][  0]LogConfig: Set CVar [[r.Shadow.DetectVertexShaderLayerAtRuntime:1]]
[2025.09.15-16.58.22:298][  0]LogConfig: Set CVar [[r.GenerateMeshDistanceFields:1]]
[2025.09.15-16.58.22:298][  0]LogConfig: CVar [[r.GenerateLandscapeGIData:0]] deferred - dummy variable created
[2025.09.15-16.58.22:298][  0]LogConfig: Set CVar [[r.EarlyZPassOnlyMaterialMasking:1]]
[2025.09.15-16.58.22:298][  0]LogConfig: Set CVar [[r.EarlyZPass:2]]
[2025.09.15-16.58.22:298][  0]LogConfig: CVar [[bDefaultParticleCutouts:1]] deferred - dummy variable created
[2025.09.15-16.58.22:298][  0]LogConfig: Set CVar [[r.StencilForLODDither:1]]
[2025.09.15-16.58.22:298][  0]LogConfig: Set CVar [[r.SkinCache.CompileShaders:1]]
[2025.09.15-16.58.22:298][  0]LogConfig: Set CVar [[r.RayTracing:0]]
[2025.09.15-16.58.22:298][  0]LogConfig: CVar [[r.RayTracing.GlobalIllumination.ScreenPercentage:50]] deferred - dummy variable created
[2025.09.15-16.58.22:298][  0]LogConfig: Set CVar [[r.AllowGlobalClipPlane:1]]
[2025.09.15-16.58.22:298][  0]LogConfig: Set CVar [[r.GBufferFormat:3]]
[2025.09.15-16.58.22:298][  0]LogConfig: Set CVar [[r.Tonemapper.Sharpen:1.8]]
[2025.09.15-16.58.22:298][  0]LogConfig: Set CVar [[r.LightShaftRenderToSeparateTranslucency:1]]
[2025.09.15-16.58.22:298][  0]LogConfig: Set CVar [[r.ReflectionCaptureResolution:1024]]
[2025.09.15-16.58.22:298][  0]LogConfig: Set CVar [[r.ReflectionMethod:1]]
[2025.09.15-16.58.22:298][  0]LogConfig: Set CVar [[r.DynamicGlobalIlluminationMethod:1]]
[2025.09.15-16.58.22:298][  0]LogConfig: Set CVar [[r.Shadow.Virtual.Enable:1]]
[2025.09.15-16.58.22:298][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.RendererOverrideSettings] File [Engine]
[2025.09.15-16.58.22:298][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.StreamingSettings] File [Engine]
[2025.09.15-16.58.22:298][  0]LogConfig: Set CVar [[s.MinBulkDataSizeForAsyncLoading:131072]]
[2025.09.15-16.58.22:298][  0]LogConfig: Set CVar [[s.AsyncLoadingThreadEnabled:1]]
[2025.09.15-16.58.22:298][  0]LogConfig: Set CVar [[s.EventDrivenLoaderEnabled:1]]
[2025.09.15-16.58.22:298][  0]LogConfig: Set CVar [[s.WarnIfTimeLimitExceeded:0]]
[2025.09.15-16.58.22:298][  0]LogConfig: Set CVar [[s.TimeLimitExceededMultiplier:1.5]]
[2025.09.15-16.58.22:298][  0]LogConfig: Set CVar [[s.TimeLimitExceededMinTime:0.005]]
[2025.09.15-16.58.22:298][  0]LogConfig: Set CVar [[s.UseBackgroundLevelStreaming:1]]
[2025.09.15-16.58.22:298][  0]LogConfig: Set CVar [[s.PriorityAsyncLoadingExtraTime:15.0]]
[2025.09.15-16.58.22:298][  0]LogConfig: Set CVar [[s.LevelStreamingActorsUpdateTimeLimit:5.0]]
[2025.09.15-16.58.22:298][  0]LogConfig: Set CVar [[s.PriorityLevelStreamingActorsUpdateExtraTime:5.0]]
[2025.09.15-16.58.22:298][  0]LogConfig: Set CVar [[s.LevelStreamingComponentsRegistrationGranularity:10]]
[2025.09.15-16.58.22:298][  0]LogConfig: Set CVar [[s.UnregisterComponentsTimeLimit:1.0]]
[2025.09.15-16.58.22:298][  0]LogConfig: Set CVar [[s.LevelStreamingComponentsUnregistrationGranularity:5]]
[2025.09.15-16.58.22:298][  0]LogConfig: CVar [[s.MaxPackageSummarySize:16384]] deferred - dummy variable created
[2025.09.15-16.58.22:298][  0]LogConfig: Set CVar [[s.FlushStreamingOnExit:1]]
[2025.09.15-16.58.22:298][  0]LogConfig: CVar [[FixedBootOrder:/Script/Engine/Default__SoundBase]] deferred - dummy variable created
[2025.09.15-16.58.22:298][  0]LogConfig: CVar [[FixedBootOrder:/Script/Engine/Default__MaterialInterface]] deferred - dummy variable created
[2025.09.15-16.58.22:298][  0]LogConfig: CVar [[FixedBootOrder:/Script/Engine/Default__DeviceProfileManager]] deferred - dummy variable created
[2025.09.15-16.58.22:298][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.GarbageCollectionSettings] File [Engine]
[2025.09.15-16.58.22:298][  0]LogConfig: Set CVar [[gc.MaxObjectsNotConsideredByGC:1]]
[2025.09.15-16.58.22:298][  0]LogConfig: Set CVar [[gc.FlushStreamingOnGC:0]]
[2025.09.15-16.58.22:298][  0]LogConfig: Set CVar [[gc.NumRetriesBeforeForcingGC:10]]
[2025.09.15-16.58.22:298][  0]LogConfig: Set CVar [[gc.AllowParallelGC:1]]
[2025.09.15-16.58.22:298][  0]LogConfig: Set CVar [[gc.TimeBetweenPurgingPendingKillObjects:61.1]]
[2025.09.15-16.58.22:298][  0]LogConfig: Set CVar [[gc.MaxObjectsInEditor:25165824]]
[2025.09.15-16.58.22:298][  0]LogConfig: Set CVar [[gc.IncrementalBeginDestroyEnabled:1]]
[2025.09.15-16.58.22:298][  0]LogConfig: Set CVar [[gc.CreateGCClusters:1]]
[2025.09.15-16.58.22:298][  0]LogConfig: Set CVar [[gc.MinGCClusterSize:5]]
[2025.09.15-16.58.22:298][  0]LogConfig: Set CVar [[gc.AssetClustreringEnabled:0]]
[2025.09.15-16.58.22:298][  0]LogConfig: Set CVar [[gc.ActorClusteringEnabled:0]]
[2025.09.15-16.58.22:298][  0]LogConfig: Set CVar [[gc.VerifyUObjectsAreNotFGCObjects:0]]
[2025.09.15-16.58.22:298][  0]LogConfig: Set CVar [[gc.GarbageEliminationEnabled:1]]
[2025.09.15-16.58.22:298][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.NetworkSettings] File [Engine]
[2025.09.15-16.58.22:298][  0]LogConfig: CVar [[NetworkEmulationProfiles:(ProfileName="Average",ToolTip="Simulates average internet conditions")]] deferred - dummy variable created
[2025.09.15-16.58.22:298][  0]LogConfig: CVar [[NetworkEmulationProfiles:(ProfileName="Bad",ToolTip="Simulates laggy internet conditions")]] deferred - dummy variable created
[2025.09.15-16.58.22:298][  0]LogConfig: Applying CVar settings from Section [/Script/UnrealEd.CookerSettings] File [Engine]
[2025.09.15-16.58.22:298][  0]LogConfig: CVar [[DefaultASTCQualityBySpeed:2]] deferred - dummy variable created
[2025.09.15-16.58.22:298][  0]LogConfig: CVar [[DefaultASTCQualityBySize:3]] deferred - dummy variable created
[2025.09.15-16.58.22:298][  0]LogConfig: CVar [[DefaultASTCQualityBySizeHQ:4]] deferred - dummy variable created
[2025.09.15-16.58.22:298][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:WidgetBlueprint]] deferred - dummy variable created
[2025.09.15-16.58.22:298][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:GroupActor]] deferred - dummy variable created
[2025.09.15-16.58.22:298][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:MetaData]] deferred - dummy variable created
[2025.09.15-16.58.22:298][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ObjectRedirector]] deferred - dummy variable created
[2025.09.15-16.58.22:298][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:NavMeshRenderingComponent]] deferred - dummy variable created
[2025.09.15-16.58.22:298][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ReflectionCaptureComponent]] deferred - dummy variable created
[2025.09.15-16.58.22:298][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:TextRenderComponent]] deferred - dummy variable created
[2025.09.15-16.58.22:298][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:Font]] deferred - dummy variable created
[2025.09.15-16.58.22:298][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:InterpCurveEdSetup]] deferred - dummy variable created
[2025.09.15-16.58.22:298][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:MaterialExpression]] deferred - dummy variable created
[2025.09.15-16.58.22:298][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:NiagaraEmitter]] deferred - dummy variable created
[2025.09.15-16.58.22:298][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:NiagaraScript]] deferred - dummy variable created
[2025.09.15-16.58.22:298][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ParticleEmitter]] deferred - dummy variable created
[2025.09.15-16.58.22:298][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ParticleLODLevel]] deferred - dummy variable created
[2025.09.15-16.58.22:298][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ParticleModule]] deferred - dummy variable created
[2025.09.15-16.58.22:298][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:SubUVAnimation]] deferred - dummy variable created
[2025.09.15-16.58.22:298][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:SoundNode]] deferred - dummy variable created
[2025.09.15-16.58.22:298][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:GameplayEffectUIData]] deferred - dummy variable created
[2025.09.15-16.58.22:298][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:AmbientSound]] deferred - dummy variable created
[2025.09.15-16.58.22:298][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:WidgetBlueprint]] deferred - dummy variable created
[2025.09.15-16.58.22:298][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:GroupActor]] deferred - dummy variable created
[2025.09.15-16.58.22:298][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:MetaData]] deferred - dummy variable created
[2025.09.15-16.58.22:298][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:ObjectRedirector]] deferred - dummy variable created
[2025.09.15-16.58.22:298][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:InterpCurveEdSetup]] deferred - dummy variable created
[2025.09.15-16.58.22:298][  0]LogConfig: CVar [[VersionedIntRValues:r.AllowStaticLighting]] deferred - dummy variable created
[2025.09.15-16.58.22:298][  0]LogConfig: CVar [[VersionedIntRValues:r.MaterialEditor.LWCTruncateMode]] deferred - dummy variable created
[2025.09.15-16.58.22:298][  0]LogConfig: CVar [[VersionedIntRValues:r.GBuffer]] deferred - dummy variable created
[2025.09.15-16.58.22:298][  0]LogConfig: CVar [[VersionedIntRValues:r.VelocityOutputPass]] deferred - dummy variable created
[2025.09.15-16.58.22:298][  0]LogConfig: CVar [[VersionedIntRValues:r.SelectiveBasePassOutputs]] deferred - dummy variable created
[2025.09.15-16.58.22:298][  0]LogConfig: CVar [[VersionedIntRValues:r.DBuffer]] deferred - dummy variable created
[2025.09.15-16.58.22:298][  0]LogConfig: CVar [[VersionedIntRValues:r.Mobile.DBuffer]] deferred - dummy variable created
[2025.09.15-16.58.22:298][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.Symbols]] deferred - dummy variable created
[2025.09.15-16.58.22:298][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.GenerateSymbols]] deferred - dummy variable created
[2025.09.15-16.58.22:298][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.WriteSymbols]] deferred - dummy variable created
[2025.09.15-16.58.22:298][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.AllowUniqueSymbols]] deferred - dummy variable created
[2025.09.15-16.58.22:298][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.ExtraData]] deferred - dummy variable created
[2025.09.15-16.58.22:298][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.Optimize]] deferred - dummy variable created
[2025.09.15-16.58.22:298][  0]LogConfig: CVar [[VersionedIntRValues:r.CompileShadersForDevelopment]] deferred - dummy variable created
[2025.09.15-16.58.22:298][  0]LogConfig: CVar [[VersionedIntRValues:r.MobileHDR]] deferred - dummy variable created
[2025.09.15-16.58.22:298][  0]LogConfig: CVar [[VersionedIntRValues:r.UsePreExposure]] deferred - dummy variable created
[2025.09.15-16.58.22:299][  0]LogCsvProfiler: Display: Metadata set : systemresolution.resx="1920"
[2025.09.15-16.58.22:299][  0]LogCsvProfiler: Display: Metadata set : systemresolution.resy="1080"
[2025.09.15-16.58.22:299][  0]LogConfig: Applying CVar settings from Section [ViewDistanceQuality@3] File [Scalability]
[2025.09.15-16.58.22:299][  0]LogConfig: Set CVar [[r.SkeletalMeshLODBias:0]]
[2025.09.15-16.58.22:299][  0]LogConfig: Set CVar [[r.ViewDistanceScale:1.0]]
[2025.09.15-16.58.22:299][  0]LogConfig: Applying CVar settings from Section [AntiAliasingQuality@3] File [Scalability]
[2025.09.15-16.58.22:299][  0]LogConfig: Set CVar [[r.FXAA.Quality:4]]
[2025.09.15-16.58.22:299][  0]LogConfig: Set CVar [[r.TemporalAA.Quality:2]]
[2025.09.15-16.58.22:299][  0]LogConfig: Set CVar [[r.TSR.History.R11G11B10:1]]
[2025.09.15-16.58.22:299][  0]LogConfig: Set CVar [[r.TSR.History.ScreenPercentage:200]]
[2025.09.15-16.58.22:299][  0]LogConfig: Set CVar [[r.TSR.History.UpdateQuality:3]]
[2025.09.15-16.58.22:299][  0]LogConfig: Set CVar [[r.TSR.ShadingRejection.Flickering:1]]
[2025.09.15-16.58.22:299][  0]LogConfig: Set CVar [[r.TSR.RejectionAntiAliasingQuality:2]]
[2025.09.15-16.58.22:299][  0]LogConfig: Set CVar [[r.TSR.ReprojectionField:1]]
[2025.09.15-16.58.22:299][  0]LogConfig: Set CVar [[r.TSR.Resurrection:1]]
[2025.09.15-16.58.22:299][  0]LogConfig: Applying CVar settings from Section [ShadowQuality@3] File [Scalability]
[2025.09.15-16.58.22:299][  0]LogConfig: Set CVar [[r.LightFunctionQuality:1]]
[2025.09.15-16.58.22:299][  0]LogConfig: Set CVar [[r.ShadowQuality:5]]
[2025.09.15-16.58.22:299][  0]LogConfig: Set CVar [[r.Shadow.CSM.MaxCascades:10]]
[2025.09.15-16.58.22:299][  0]LogConfig: Set CVar [[r.Shadow.MaxResolution:2048]]
[2025.09.15-16.58.22:299][  0]LogConfig: Set CVar [[r.Shadow.MaxCSMResolution:2048]]
[2025.09.15-16.58.22:299][  0]LogConfig: Set CVar [[r.Shadow.RadiusThreshold:0.01]]
[2025.09.15-16.58.22:299][  0]LogConfig: Set CVar [[r.Shadow.DistanceScale:1.0]]
[2025.09.15-16.58.22:299][  0]LogConfig: Set CVar [[r.Shadow.CSM.TransitionScale:1.0]]
[2025.09.15-16.58.22:299][  0]LogConfig: Set CVar [[r.Shadow.PreShadowResolutionFactor:1.0]]
[2025.09.15-16.58.22:299][  0]LogConfig: Set CVar [[r.DistanceFieldShadowing:1]]
[2025.09.15-16.58.22:299][  0]LogConfig: Set CVar [[r.VolumetricFog:1]]
[2025.09.15-16.58.22:299][  0]LogConfig: Set CVar [[r.VolumetricFog.GridPixelSize:8]]
[2025.09.15-16.58.22:299][  0]LogConfig: Set CVar [[r.VolumetricFog.GridSizeZ:128]]
[2025.09.15-16.58.22:299][  0]LogConfig: Set CVar [[r.VolumetricFog.HistoryMissSupersampleCount:4]]
[2025.09.15-16.58.22:299][  0]LogConfig: Set CVar [[r.LightMaxDrawDistanceScale:1]]
[2025.09.15-16.58.22:299][  0]LogConfig: Set CVar [[r.CapsuleShadows:1]]
[2025.09.15-16.58.22:301][  0]LogConfig: Set CVar [[r.Shadow.Virtual.MaxPhysicalPages:4096]]
[2025.09.15-16.58.22:301][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasDirectional:-1.5]]
[2025.09.15-16.58.22:301][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasDirectionalMoving:-1.5]]
[2025.09.15-16.58.22:301][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasLocal:0.0]]
[2025.09.15-16.58.22:301][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasLocalMoving:1.0]]
[2025.09.15-16.58.22:301][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.RayCountDirectional:8]]
[2025.09.15-16.58.22:301][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.SamplesPerRayDirectional:4]]
[2025.09.15-16.58.22:301][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.RayCountLocal:8]]
[2025.09.15-16.58.22:301][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.SamplesPerRayLocal:4]]
[2025.09.15-16.58.22:301][  0]LogConfig: Applying CVar settings from Section [GlobalIlluminationQuality@3] File [Scalability]
[2025.09.15-16.58.22:301][  0]LogConfig: Set CVar [[r.DistanceFieldAO:1]]
[2025.09.15-16.58.22:301][  0]LogConfig: Set CVar [[r.SkylightIntensityMultiplier:1.0]]
[2025.09.15-16.58.22:301][  0]LogConfig: Set CVar [[r.AOQuality:2]]
[2025.09.15-16.58.22:301][  0]LogConfig: Set CVar [[r.Lumen.DiffuseIndirect.Allow:1]]
[2025.09.15-16.58.22:301][  0]LogConfig: Set CVar [[r.LumenScene.DirectLighting.MaxLightsPerTile:8]]
[2025.09.15-16.58.22:301][  0]LogConfig: Set CVar [[r.LumenScene.DirectLighting.UpdateFactor:32]]
[2025.09.15-16.58.22:301][  0]LogConfig: Set CVar [[r.LumenScene.Radiosity.UpdateFactor:64]]
[2025.09.15-16.58.22:301][  0]LogConfig: Set CVar [[r.LumenScene.Radiosity.ProbeSpacing:4]]
[2025.09.15-16.58.22:301][  0]LogConfig: Set CVar [[r.LumenScene.Radiosity.HemisphereProbeResolution:4]]
[2025.09.15-16.58.22:301][  0]LogConfig: Set CVar [[r.Lumen.TraceMeshSDFs.Allow:1]]
[2025.09.15-16.58.22:301][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.RadianceCache.ProbeResolution:32]]
[2025.09.15-16.58.22:301][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.RadianceCache.NumProbesToTraceBudget:100]]
[2025.09.15-16.58.22:301][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.DownsampleFactor:16]]
[2025.09.15-16.58.22:301][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.NumAdaptiveProbes:8]]
[2025.09.15-16.58.22:301][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.TracingOctahedronResolution:8]]
[2025.09.15-16.58.22:301][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.IrradianceFormat:0]]
[2025.09.15-16.58.22:301][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.StochasticInterpolation:0]]
[2025.09.15-16.58.22:301][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.FullResolutionJitterWidth:1]]
[2025.09.15-16.58.22:301][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.TwoSidedFoliageBackfaceDiffuse:1]]
[2025.09.15-16.58.22:301][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.ScreenTraces.HZBTraversal.FullResDepth:1]]
[2025.09.15-16.58.22:301][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.ShortRangeAO.HardwareRayTracing:0]]
[2025.09.15-16.58.22:301][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.ShortRangeAO.BentNormal:1]]
[2025.09.15-16.58.22:301][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.GridPixelSize:32]]
[2025.09.15-16.58.22:301][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.TraceFromVolume:1]]
[2025.09.15-16.58.22:301][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.TracingOctahedronResolution:3]]
[2025.09.15-16.58.22:301][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.RadianceCache.ProbeResolution:8]]
[2025.09.15-16.58.22:301][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.RadianceCache.NumProbesToTraceBudget:70]]
[2025.09.15-16.58.22:301][  0]LogConfig: Set CVar [[r.RayTracing.Scene.BuildMode:1]]
[2025.09.15-16.58.22:301][  0]LogConfig: Applying CVar settings from Section [ReflectionQuality@3] File [Scalability]
[2025.09.15-16.58.22:301][  0]LogConfig: Set CVar [[r.SSR.Quality:3]]
[2025.09.15-16.58.22:301][  0]LogConfig: Set CVar [[r.SSR.HalfResSceneColor:0]]
[2025.09.15-16.58.22:301][  0]LogConfig: Set CVar [[r.Lumen.Reflections.Allow:1]]
[2025.09.15-16.58.22:301][  0]LogConfig: Set CVar [[r.Lumen.Reflections.DownsampleFactor:1]]
[2025.09.15-16.58.22:301][  0]LogConfig: Set CVar [[r.Lumen.Reflections.MaxRoughnessToTraceForFoliage:0.4]]
[2025.09.15-16.58.22:301][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.MaxRoughnessToEvaluateRoughSpecularForFoliage:0.8]]
[2025.09.15-16.58.22:301][  0]LogConfig: Set CVar [[r.Lumen.Reflections.ScreenSpaceReconstruction.NumSamples:5]]
[2025.09.15-16.58.22:301][  0]LogConfig: Set CVar [[r.Lumen.Reflections.ScreenSpaceReconstruction.MinWeight:0]]
[2025.09.15-16.58.22:301][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyReflections.FrontLayer.Allow:1]]
[2025.09.15-16.58.22:301][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyReflections.FrontLayer.Enable:0]]
[2025.09.15-16.58.22:301][  0]LogConfig: Applying CVar settings from Section [PostProcessQuality@3] File [Scalability]
[2025.09.15-16.58.22:301][  0]LogConfig: Set CVar [[r.MotionBlurQuality:4]]
[2025.09.15-16.58.22:301][  0]LogConfig: Set CVar [[r.MotionBlur.HalfResGather:0]]
[2025.09.15-16.58.22:301][  0]LogConfig: Set CVar [[r.AmbientOcclusionMipLevelFactor:0.4]]
[2025.09.15-16.58.22:301][  0]LogConfig: Set CVar [[r.AmbientOcclusionMaxQuality:100]]
[2025.09.15-16.58.22:301][  0]LogConfig: Set CVar [[r.AmbientOcclusionLevels:-1]]
[2025.09.15-16.58.22:301][  0]LogConfig: Set CVar [[r.AmbientOcclusionRadiusScale:1.0]]
[2025.09.15-16.58.22:301][  0]LogConfig: Set CVar [[r.DepthOfFieldQuality:2]]
[2025.09.15-16.58.22:301][  0]LogConfig: Set CVar [[r.RenderTargetPoolMin:400]]
[2025.09.15-16.58.22:301][  0]LogConfig: Set CVar [[r.LensFlareQuality:2]]
[2025.09.15-16.58.22:301][  0]LogConfig: Set CVar [[r.SceneColorFringeQuality:1]]
[2025.09.15-16.58.22:301][  0]LogConfig: Set CVar [[r.EyeAdaptationQuality:2]]
[2025.09.15-16.58.22:301][  0]LogConfig: Set CVar [[r.BloomQuality:5]]
[2025.09.15-16.58.22:301][  0]LogConfig: Set CVar [[r.Bloom.ScreenPercentage:50.000]]
[2025.09.15-16.58.22:301][  0]LogConfig: Set CVar [[r.FastBlurThreshold:100]]
[2025.09.15-16.58.22:301][  0]LogConfig: Set CVar [[r.Upscale.Quality:3]]
[2025.09.15-16.58.22:301][  0]LogConfig: Set CVar [[r.LightShaftQuality:1]]
[2025.09.15-16.58.22:301][  0]LogConfig: Set CVar [[r.Filter.SizeScale:1]]
[2025.09.15-16.58.22:301][  0]LogConfig: Set CVar [[r.Tonemapper.Quality:5]]
[2025.09.15-16.58.22:301][  0]LogConfig: Set CVar [[r.DOF.Gather.ResolutionDivisor:2         ; lower gathering resolution]]
[2025.09.15-16.58.22:301][  0]LogConfig: Set CVar [[r.DOF.Gather.AccumulatorQuality:1        ; higher gathering accumulator quality]]
[2025.09.15-16.58.22:301][  0]LogConfig: Set CVar [[r.DOF.Gather.PostfilterMethod:1          ; Median3x3 postfilering method]]
[2025.09.15-16.58.22:301][  0]LogConfig: Set CVar [[r.DOF.Gather.EnableBokehSettings:0       ; no bokeh simulation when gathering]]
[2025.09.15-16.58.22:301][  0]LogConfig: Set CVar [[r.DOF.Gather.RingCount:4                 ; medium number of samples when gathering]]
[2025.09.15-16.58.22:301][  0]LogConfig: Set CVar [[r.DOF.Scatter.ForegroundCompositing:1    ; additive foreground scattering]]
[2025.09.15-16.58.22:301][  0]LogConfig: Set CVar [[r.DOF.Scatter.BackgroundCompositing:2    ; additive background scattering]]
[2025.09.15-16.58.22:301][  0]LogConfig: Set CVar [[r.DOF.Scatter.EnableBokehSettings:1      ; bokeh simulation when scattering]]
[2025.09.15-16.58.22:301][  0]LogConfig: Set CVar [[r.DOF.Scatter.MaxSpriteRatio:0.1         ; only a maximum of 10% of scattered bokeh]]
[2025.09.15-16.58.22:301][  0]LogConfig: Set CVar [[r.DOF.Recombine.Quality:1                ; cheap slight out of focus]]
[2025.09.15-16.58.22:301][  0]LogConfig: Set CVar [[r.DOF.Recombine.EnableBokehSettings:0    ; no bokeh simulation on slight out of focus]]
[2025.09.15-16.58.22:301][  0]LogConfig: Set CVar [[r.DOF.TemporalAAQuality:1                ; more stable temporal accumulation]]
[2025.09.15-16.58.22:301][  0]LogConfig: Set CVar [[r.DOF.Kernel.MaxForegroundRadius:0.025]]
[2025.09.15-16.58.22:301][  0]LogConfig: Set CVar [[r.DOF.Kernel.MaxBackgroundRadius:0.025]]
[2025.09.15-16.58.22:301][  0]LogConfig: Applying CVar settings from Section [TextureQuality@3] File [Scalability]
[2025.09.15-16.58.22:301][  0]LogConfig: Set CVar [[r.Streaming.MipBias:0]]
[2025.09.15-16.58.22:301][  0]LogConfig: Set CVar [[r.Streaming.AmortizeCPUToGPUCopy:0]]
[2025.09.15-16.58.22:301][  0]LogConfig: Set CVar [[r.Streaming.MaxNumTexturesToStreamPerFrame:0]]
[2025.09.15-16.58.22:301][  0]LogConfig: Set CVar [[r.Streaming.Boost:1]]
[2025.09.15-16.58.22:301][  0]LogConfig: Set CVar [[r.MaxAnisotropy:8]]
[2025.09.15-16.58.22:301][  0]LogConfig: Set CVar [[r.VT.MaxAnisotropy:8]]
[2025.09.15-16.58.22:301][  0]LogConfig: Set CVar [[r.Streaming.LimitPoolSizeToVRAM:0]]
[2025.09.15-16.58.22:301][  0]LogConfig: Set CVar [[r.Streaming.PoolSize:1000]]
[2025.09.15-16.58.22:301][  0]LogConfig: Set CVar [[r.Streaming.MaxEffectiveScreenSize:0]]
[2025.09.15-16.58.22:301][  0]LogConfig: Applying CVar settings from Section [EffectsQuality@3] File [Scalability]
[2025.09.15-16.58.22:301][  0]LogConfig: Set CVar [[r.TranslucencyLightingVolumeDim:64]]
[2025.09.15-16.58.22:301][  0]LogConfig: Set CVar [[r.RefractionQuality:2]]
[2025.09.15-16.58.22:301][  0]LogConfig: Set CVar [[r.SceneColorFormat:4]]
[2025.09.15-16.58.22:301][  0]LogConfig: Set CVar [[r.DetailMode:3]]
[2025.09.15-16.58.22:301][  0]LogConfig: Set CVar [[r.TranslucencyVolumeBlur:1]]
[2025.09.15-16.58.22:301][  0]LogConfig: Set CVar [[r.MaterialQualityLevel:1 ; High quality]]
[2025.09.15-16.58.22:301][  0]LogConfig: Set CVar [[r.SSS.Scale:1]]
[2025.09.15-16.58.22:301][  0]LogConfig: Set CVar [[r.SSS.SampleSet:2]]
[2025.09.15-16.58.22:301][  0]LogConfig: Set CVar [[r.SSS.Quality:1]]
[2025.09.15-16.58.22:301][  0]LogConfig: Set CVar [[r.SSS.HalfRes:0]]
[2025.09.15-16.58.22:301][  0]LogConfig: Set CVar [[r.SSGI.Quality:3]]
[2025.09.15-16.58.22:301][  0]LogConfig: Set CVar [[r.EmitterSpawnRateScale:1.0]]
[2025.09.15-16.58.22:301][  0]LogConfig: Set CVar [[r.ParticleLightQuality:2]]
[2025.09.15-16.58.22:301][  0]LogConfig: Set CVar [[r.SkyAtmosphere.AerialPerspectiveLUT.FastApplyOnOpaque:1 ; Always have FastSkyLUT 1 in this case to avoid wrong sky]]
[2025.09.15-16.58.22:301][  0]LogConfig: Set CVar [[r.SkyAtmosphere.AerialPerspectiveLUT.SampleCountMaxPerSlice:4]]
[2025.09.15-16.58.22:301][  0]LogConfig: Set CVar [[r.SkyAtmosphere.AerialPerspectiveLUT.DepthResolution:16.0]]
[2025.09.15-16.58.22:301][  0]LogConfig: Set CVar [[r.SkyAtmosphere.FastSkyLUT:1]]
[2025.09.15-16.58.22:301][  0]LogConfig: Set CVar [[r.SkyAtmosphere.FastSkyLUT.SampleCountMin:4.0]]
[2025.09.15-16.58.22:301][  0]LogConfig: Set CVar [[r.SkyAtmosphere.FastSkyLUT.SampleCountMax:128.0]]
[2025.09.15-16.58.22:301][  0]LogConfig: Set CVar [[r.SkyAtmosphere.SampleCountMin:4.0]]
[2025.09.15-16.58.22:301][  0]LogConfig: Set CVar [[r.SkyAtmosphere.SampleCountMax:128.0]]
[2025.09.15-16.58.22:301][  0]LogConfig: Set CVar [[r.SkyAtmosphere.TransmittanceLUT.UseSmallFormat:0]]
[2025.09.15-16.58.22:301][  0]LogConfig: Set CVar [[r.SkyAtmosphere.TransmittanceLUT.SampleCount:10.0]]
[2025.09.15-16.58.22:301][  0]LogConfig: Set CVar [[r.SkyAtmosphere.MultiScatteringLUT.SampleCount:15.0]]
[2025.09.15-16.58.22:301][  0]LogConfig: Set CVar [[fx.Niagara.QualityLevel:3]]
[2025.09.15-16.58.22:301][  0]LogConfig: Set CVar [[r.Refraction.OffsetQuality:1]]
[2025.09.15-16.58.22:301][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.DownsampleFactor:2]]
[2025.09.15-16.58.22:301][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.MaxStepCount:256]]
[2025.09.15-16.58.22:301][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.Shadows.Resolution:256]]
[2025.09.15-16.58.22:301][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.Shadows.MaxSampleCount:8]]
[2025.09.15-16.58.22:301][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.UseExistenceMask:0]]
[2025.09.15-16.58.22:301][  0]LogConfig: Applying CVar settings from Section [FoliageQuality@3] File [Scalability]
[2025.09.15-16.58.22:301][  0]LogConfig: Set CVar [[foliage.DensityScale:1.0]]
[2025.09.15-16.58.22:301][  0]LogConfig: Set CVar [[grass.DensityScale:1.0]]
[2025.09.15-16.58.22:301][  0]LogConfig: Applying CVar settings from Section [ShadingQuality@3] File [Scalability]
[2025.09.15-16.58.22:301][  0]LogConfig: Set CVar [[r.HairStrands.SkyLighting.IntegrationType:2]]
[2025.09.15-16.58.22:301][  0]LogConfig: Set CVar [[r.HairStrands.SkyAO.SampleCount:4]]
[2025.09.15-16.58.22:301][  0]LogConfig: Set CVar [[r.HairStrands.Visibility.MSAA.SamplePerPixel:4]]
[2025.09.15-16.58.22:301][  0]LogConfig: Set CVar [[r.AnisotropicMaterials:1]]
[2025.09.15-16.58.22:301][  0]LogConfig: Applying CVar settings from Section [LandscapeQuality@3] File [Scalability]
[2025.09.15-16.58.22:303][  0]LogRHI: Using Default RHI: D3D12
[2025.09.15-16.58.22:303][  0]LogRHI: Using Highest Feature Level of D3D12: SM6
[2025.09.15-16.58.22:303][  0]LogRHI: Loading RHI module D3D12RHI
[2025.09.15-16.58.22:305][  0]LogD3D12RHI: Loading WinPixEventRuntime.dll for PIX profiling (from ../../../Engine/Binaries/ThirdParty/Windows/WinPixEventRuntime/x64).
[2025.09.15-16.58.22:305][  0]LogRHI: Checking if RHI D3D12 with Feature Level SM6 is supported by your system.
[2025.09.15-16.58.22:413][  0]LogD3D12RHI: Intel Extensions Framework not supported by driver. Please check if a driver update is available.
[2025.09.15-16.58.22:428][  0]LogD3D12RHI: Found D3D12 adapter 0: Intel(R) Iris(R) Xe Graphics (VendorId: 8086, DeviceId: a7a1, SubSysId: c001028, Revision: 0004
[2025.09.15-16.58.22:428][  0]LogD3D12RHI:   Max supported Feature Level 12_1, shader model 6.7, binding tier 3, wave ops supported, atomic64 unsupported
[2025.09.15-16.58.22:428][  0]LogD3D12RHI:   Adapter has 128MB of dedicated video memory, 0MB of dedicated system memory, and 16226MB of shared system memory, 1 output[s], UMA:true
[2025.09.15-16.58.22:428][  0]LogD3D12RHI:   Driver Version: 32.0.101.6881 (internal:32.0.101.6881, unified:101.6881)
[2025.09.15-16.58.22:428][  0]LogD3D12RHI:      Driver Date: 6-4-2025
[2025.09.15-16.58.22:433][  0]LogD3D12RHI: Found D3D12 adapter 1: Microsoft Basic Render Driver (VendorId: 1414, DeviceId: 008c, SubSysId: 0000, Revision: 0000
[2025.09.15-16.58.22:433][  0]LogD3D12RHI:   Max supported Feature Level 12_1, shader model 6.7, binding tier 3, wave ops supported, atomic64 supported
[2025.09.15-16.58.22:433][  0]LogD3D12RHI:   Adapter has 0MB of dedicated video memory, 0MB of dedicated system memory, and 16226MB of shared system memory, 0 output[s], UMA:true
[2025.09.15-16.58.22:433][  0]LogD3D12RHI: DirectX Agility SDK runtime found.
[2025.09.15-16.58.22:433][  0]LogD3D12RHI: Chosen D3D12 Adapter Id = 0
[2025.09.15-16.58.22:433][  0]LogD3D12RHI: Adapter only supports up to Feature Level 'SM5', requested Feature Level was 'SM6'
[2025.09.15-16.58.22:433][  0]LogRHI: RHI D3D12 with Feature Level SM6 is not supported on your system, attempting to fall back to RHI D3D12 with Feature Level SM5
[2025.09.15-16.58.22:433][  0]LogRHI: Loading RHI module D3D12RHI
[2025.09.15-16.58.22:433][  0]LogRHI: Checking if RHI D3D12 with Feature Level SM5 is supported by your system.
[2025.09.15-16.58.22:433][  0]LogRHI: RHI D3D12 with Feature Level SM5 is supported and will be used.
[2025.09.15-16.58.22:433][  0]LogInit: Selected Device Profile: [WindowsEditor]
[2025.09.15-16.58.22:433][  0]LogHAL: Display: Platform has ~ 32 GB [34029125632 / 34359738368 / 32], which maps to Largest [LargestMinGB=32, LargerMinGB=12, DefaultMinGB=8, SmallerMinGB=6, SmallestMinGB=0)
[2025.09.15-16.58.22:434][  0]LogDeviceProfileManager: Going up to parent DeviceProfile [Windows]
[2025.09.15-16.58.22:434][  0]LogDeviceProfileManager: Going up to parent DeviceProfile []
[2025.09.15-16.58.22:434][  0]LogDeviceProfileManager: Pushing Device Profile CVar: [[UI.SlateSDFText.RasterizationMode:Bitmap -> Msdf]]
[2025.09.15-16.58.22:434][  0]LogDeviceProfileManager: Pushing Device Profile CVar: [[UI.SlateSDFText.ResolutionLevel:2 -> 2]]
[2025.09.15-16.58.22:434][  0]LogConfig: Applying CVar settings from Section [Startup] File [../../../Engine/Config/ConsoleVariables.ini]
[2025.09.15-16.58.22:434][  0]LogConfig: Set CVar [[r.DumpShaderDebugInfo:2]]
[2025.09.15-16.58.22:434][  0]LogConfig: Set CVar [[p.chaos.AllowCreatePhysxBodies:1]]
[2025.09.15-16.58.22:434][  0]LogConfig: Set CVar [[fx.SkipVectorVMBackendOptimizations:1]]
[2025.09.15-16.58.22:434][  0]LogConfig: CVar [[ds.CADTranslator.Meshing.ActivateThinZoneMeshing:0]] deferred - dummy variable created
[2025.09.15-16.58.22:434][  0]LogConfig: CVar [[ds.CADTranslator.Stitching.RemoveThinFaces:0]] deferred - dummy variable created
[2025.09.15-16.58.22:434][  0]LogConfig: Applying CVar settings from Section [Startup_Windows] File [../../../Engine/Config/ConsoleVariables.ini]
[2025.09.15-16.58.22:434][  0]LogConfig: Applying CVar settings from Section [ConsoleVariables] File [Engine]
[2025.09.15-16.58.22:434][  0]LogConfig: Applying CVar settings from Section [ConsoleVariables] File [C:/Auracron/Saved/Config/WindowsEditor/Editor.ini]
[2025.09.15-16.58.22:434][  0]LogInit: Computer: TKT
[2025.09.15-16.58.22:434][  0]LogInit: User: tktca
[2025.09.15-16.58.22:434][  0]LogInit: CPU Page size=4096, Cores=10
[2025.09.15-16.58.22:434][  0]LogInit: High frequency timer resolution =10.000000 MHz
[2025.09.15-16.58.22:434][  0]LogMemory: Process is running as part of a Windows Job with separate resource limits
[2025.09.15-16.58.22:434][  0]LogMemory: Memory total: Physical=31.7GB (32GB approx) Virtual=36.7GB
[2025.09.15-16.58.22:434][  0]LogMemory: Platform Memory Stats for WindowsEditor
[2025.09.15-16.58.22:434][  0]LogMemory: Process Physical Memory: 662.77 MB used, 700.37 MB peak
[2025.09.15-16.58.22:434][  0]LogMemory: Process Virtual Memory: 652.17 MB used, 666.94 MB peak
[2025.09.15-16.58.22:434][  0]LogMemory: Physical Memory: 17904.02 MB used,  14548.69 MB free, 32452.70 MB total
[2025.09.15-16.58.22:434][  0]LogMemory: Virtual Memory: 20460.23 MB used,  17112.47 MB free, 37572.70 MB total
[2025.09.15-16.58.22:434][  0]LogCsvProfiler: Display: Metadata set : extradevelopmentmemorymb="0"
[2025.09.15-16.58.22:436][  0]LogWindows: WindowsPlatformFeatures enabled
[2025.09.15-16.58.22:438][  0]LogChaosDD: Chaos Debug Draw Startup
[2025.09.15-16.58.22:438][  0]LogInit: Physics initialised using underlying interface: Chaos
[2025.09.15-16.58.22:438][  0]LogInit: Using OS detected language (pt-BR).
[2025.09.15-16.58.22:438][  0]LogInit: Using OS detected locale (pt-BR).
[2025.09.15-16.58.22:443][  0]LogInit: Setting process to per monitor DPI aware
[2025.09.15-16.58.22:735][  0]LogTextLocalizationResource: LocRes '../../../Engine/Content/Localization/Editor/pt/Editor.locres' could not be opened for reading!
[2025.09.15-16.58.22:735][  0]LogTextLocalizationResource: LocRes '../../../Engine/Content/Localization/EditorTutorials/pt/EditorTutorials.locres' could not be opened for reading!
[2025.09.15-16.58.22:735][  0]LogTextLocalizationResource: LocRes '../../../Engine/Content/Localization/Keywords/pt/Keywords.locres' could not be opened for reading!
[2025.09.15-16.58.22:735][  0]LogTextLocalizationResource: LocRes '../../../Engine/Content/Localization/Category/pt/Category.locres' could not be opened for reading!
[2025.09.15-16.58.22:735][  0]LogTextLocalizationResource: LocRes '../../../Engine/Content/Localization/ToolTips/pt/ToolTips.locres' could not be opened for reading!
[2025.09.15-16.58.22:735][  0]LogTextLocalizationResource: LocRes '../../../Engine/Content/Localization/PropertyNames/pt/PropertyNames.locres' could not be opened for reading!
[2025.09.15-16.58.22:735][  0]LogTextLocalizationResource: LocRes '../../../Engine/Content/Localization/Engine/pt/Engine.locres' could not be opened for reading!
[2025.09.15-16.58.22:735][  0]LogTextLocalizationResource: LocRes '../../../Engine/Plugins/MetaHuman/MetaHumanSDK/Content/Localization/MetaHumanSDK/pt/MetaHumanSDK.locres' could not be opened for reading!
[2025.09.15-16.58.22:735][  0]LogTextLocalizationResource: LocRes '../../../Engine/Plugins/Online/OnlineSubsystem/Content/Localization/OnlineSubsystem/pt/OnlineSubsystem.locres' could not be opened for reading!
[2025.09.15-16.58.22:735][  0]LogTextLocalizationResource: LocRes '../../../Engine/Plugins/Online/OnlineSubsystemUtils/Content/Localization/OnlineSubsystemUtils/pt/OnlineSubsystemUtils.locres' could not be opened for reading!
[2025.09.15-16.58.22:735][  0]LogTextLocalizationResource: LocRes '../../../Engine/Plugins/Online/IOS/OnlineSubsystemIOS/Content/Localization/OnlineSubsystemIOS/pt/OnlineSubsystemIOS.locres' could not be opened for reading!
[2025.09.15-16.58.22:845][  0]LogWindowsTextInputMethodSystem: Available input methods:
[2025.09.15-16.58.22:845][  0]LogWindowsTextInputMethodSystem:   - Português (Brasil) - (Keyboard).
[2025.09.15-16.58.22:845][  0]LogWindowsTextInputMethodSystem:   - Português (Brasil) - (Keyboard).
[2025.09.15-16.58.22:845][  0]LogWindowsTextInputMethodSystem:   - Português (Portugal) - (Keyboard).
[2025.09.15-16.58.22:845][  0]LogWindowsTextInputMethodSystem:   - Português (Brasil) - Correção de Entrada por Toque (TSF IME).
[2025.09.15-16.58.22:845][  0]LogWindowsTextInputMethodSystem: Activated input method: Português (Brasil) - (Keyboard).
[2025.09.15-16.58.22:849][  0]LogWindowsTouchpad: Display: CacheForceMaxTouchpadSensitivityMode SetMaxTouchpadSensitivity
[2025.09.15-16.58.22:852][  0]LogObj: Display: Attempting to load config data for Default__SlateThemeManager before the Class has been constructed/registered/linked (likely during module loading or early startup). This will result in the load silently failing and should be fixed.
[2025.09.15-16.58.22:855][  0]LogSlate: New Slate User Created. Platform User Id 0, User Index 0, Is Virtual User: 0
[2025.09.15-16.58.22:855][  0]LogSlate: Slate User Registered.  User Index 0, Is Virtual User: 0
[2025.09.15-16.58.22:952][  0]LogRHI: Using Default RHI: D3D12
[2025.09.15-16.58.22:952][  0]LogRHI: Using Highest Feature Level of D3D12: SM6
[2025.09.15-16.58.22:952][  0]LogRHI: Loading RHI module D3D12RHI
[2025.09.15-16.58.22:952][  0]LogRHI: Checking if RHI D3D12 with Feature Level SM6 is supported by your system.
[2025.09.15-16.58.22:952][  0]LogD3D12RHI: Adapter only supports up to Feature Level 'SM5', requested Feature Level was 'SM6'
[2025.09.15-16.58.22:952][  0]LogRHI: RHI D3D12 with Feature Level SM6 is not supported on your system, attempting to fall back to RHI D3D12 with Feature Level SM5
[2025.09.15-16.58.22:952][  0]LogRHI: Loading RHI module D3D12RHI
[2025.09.15-16.58.22:952][  0]LogRHI: Checking if RHI D3D12 with Feature Level SM5 is supported by your system.
[2025.09.15-16.58.22:952][  0]LogRHI: RHI D3D12 with Feature Level SM5 is supported and will be used.
[2025.09.15-16.58.22:952][  0]LogD3D12RHI: Integrated GPU (iGPU): true
[2025.09.15-16.58.22:952][  0]LogD3D12RHI: Display: Creating D3D12 RHI with Max Feature Level SM5
[2025.09.15-16.58.22:953][  0]LogWindows: Attached monitors:
[2025.09.15-16.58.22:953][  0]LogWindows:     resolution: 1920x1080, work area: (0, 0) -> (1920, 1032), device: '\\.\DISPLAY1' [PRIMARY]
[2025.09.15-16.58.22:953][  0]LogWindows: Found 1 attached monitors.
[2025.09.15-16.58.22:953][  0]LogWindows: Gathering driver information using Windows Setup API
[2025.09.15-16.58.22:953][  0]LogRHI: RHI Adapter Info:
[2025.09.15-16.58.22:953][  0]LogRHI:             Name: Intel(R) Iris(R) Xe Graphics
[2025.09.15-16.58.22:953][  0]LogRHI:   Driver Version: 32.0.101.6881 (internal:32.0.101.6881, unified:101.6881)
[2025.09.15-16.58.22:953][  0]LogRHI:      Driver Date: 6-4-2025
[2025.09.15-16.58.22:953][  0]LogD3D12RHI:     GPU DeviceId: 0xa7a1 (for the marketing name, search the web for "GPU Device Id")
[2025.09.15-16.58.22:953][  0]LogD3D12RHI: InitD3DDevice: -D3DDebug = off -D3D12GPUValidation = off
[2025.09.15-16.58.22:953][  0]LogD3D12RHI: Emitting draw events for PIX profiling.
[2025.09.15-16.58.23:063][  0]LogD3D12RHI: Intel Extensions Framework not supported by driver. Please check if a driver update is available.
[2025.09.15-16.58.23:071][  0]LogIntelBreadcrumbs: Intel Breadcrumbs is explicitly disabled. Intel Breadcrumbs initialization skipped...
[2025.09.15-16.58.23:157][  0]LogD3D12RHI: ID3D12Device1 is supported.
[2025.09.15-16.58.23:157][  0]LogD3D12RHI: ID3D12Device2 is supported.
[2025.09.15-16.58.23:157][  0]LogD3D12RHI: ID3D12Device3 is supported.
[2025.09.15-16.58.23:157][  0]LogD3D12RHI: ID3D12Device4 is supported.
[2025.09.15-16.58.23:157][  0]LogD3D12RHI: ID3D12Device5 is supported.
[2025.09.15-16.58.23:157][  0]LogD3D12RHI: ID3D12Device6 is supported.
[2025.09.15-16.58.23:157][  0]LogD3D12RHI: ID3D12Device7 is supported.
[2025.09.15-16.58.23:157][  0]LogD3D12RHI: ID3D12Device8 is supported.
[2025.09.15-16.58.23:157][  0]LogD3D12RHI: ID3D12Device9 is supported.
[2025.09.15-16.58.23:157][  0]LogD3D12RHI: ID3D12Device10 is supported.
[2025.09.15-16.58.23:157][  0]LogD3D12RHI: ID3D12Device11 is supported.
[2025.09.15-16.58.23:157][  0]LogD3D12RHI: ID3D12Device12 is supported.
[2025.09.15-16.58.23:157][  0]LogD3D12RHI: Bindless resources are supported
[2025.09.15-16.58.23:157][  0]LogD3D12RHI: Stencil ref from pixel shader is supported
[2025.09.15-16.58.23:157][  0]LogD3D12RHI: Raster order views are supported
[2025.09.15-16.58.23:157][  0]LogD3D12RHI: Shader Model 6.6 atomic64 is not supported
[2025.09.15-16.58.23:189][  0]LogD3D12RHI: [GPUBreadCrumb] Successfully setup breadcrumb resource for DiagnosticBuffer (Queue: 0x000001A3CBDD9AC0)
[2025.09.15-16.58.23:189][  0]LogD3D12RHI: [GPUBreadCrumb] Successfully setup breadcrumb resource for DiagnosticBuffer (Queue: 0x000001A3CBDD9D80)
[2025.09.15-16.58.23:190][  0]LogD3D12RHI: [GPUBreadCrumb] Successfully setup breadcrumb resource for DiagnosticBuffer (Queue: 0x000001A3CBDDA040)
[2025.09.15-16.58.23:190][  0]LogD3D12RHI: Display: Not using pipeline state disk cache per r.D3D12.PSO.DiskCache=0
[2025.09.15-16.58.23:190][  0]LogD3D12RHI: Display: Not using driver-optimized pipeline state disk cache per r.D3D12.PSO.DriverOptimizedDiskCache=0
[2025.09.15-16.58.23:215][  0]LogD3D12RHI: Intel Extensions Framework not supported by driver. Please check if a driver update is available.
[2025.09.15-16.58.23:215][  0]LogRHI: Texture pool is 9738 MB (70% of 13912 MB)
[2025.09.15-16.58.23:215][  0]LogD3D12RHI: Async texture creation enabled
[2025.09.15-16.58.23:215][  0]LogD3D12RHI: RHI does not have support for 64 bit atomics
[2025.09.15-16.58.23:228][  0]LogVRS: Current RHI supports per-draw Variable Rate Shading
[2025.09.15-16.58.23:232][  0]LogCsvProfiler: Display: Metadata set : verbatimrhiname="D3D12"
[2025.09.15-16.58.23:232][  0]LogCsvProfiler: Display: Metadata set : rhiname="D3D12"
[2025.09.15-16.58.23:232][  0]LogCsvProfiler: Display: Metadata set : rhifeaturelevel="SM5"
[2025.09.15-16.58.23:232][  0]LogCsvProfiler: Display: Metadata set : shaderplatform="PCD3D_SM5"
[2025.09.15-16.58.23:232][  0]LogInit: Initializing FReadOnlyCVARCache
[2025.09.15-16.58.23:234][  0]LogTurnkeySupport: Running Turnkey SDK detection: ' -ScriptsForProject="C:/Auracron/EnvDesigns.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="C:/Auracron/Intermediate/TurnkeyReport_0.log" -log="C:/Auracron/Intermediate/TurnkeyLog_0.log" -project="C:/Auracron/EnvDesigns.uproject"  -platform=all'
[2025.09.15-16.58.23:234][  0]LogMonitoredProcess: Running Serialized UAT: [ cmd.exe /c ""C:/Program Files/Epic Games/UE_5.6/Engine/Build/BatchFiles/RunUAT.bat"  -ScriptsForProject="C:/Auracron/EnvDesigns.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="C:/Auracron/Intermediate/TurnkeyReport_0.log" -log="C:/Auracron/Intermediate/TurnkeyLog_0.log" -project="C:/Auracron/EnvDesigns.uproject"  -platform=all" ]
[2025.09.15-16.58.23:244][  0]LogTextureFormatASTC: Display: ASTCEnc version 5.0.1 library loaded
[2025.09.15-16.58.23:244][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatASTC
[2025.09.15-16.58.23:244][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatDXT
[2025.09.15-16.58.23:244][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatETC2
[2025.09.15-16.58.23:244][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatIntelISPCTexComp
[2025.09.15-16.58.23:244][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatUncompressed
[2025.09.15-16.58.23:244][  0]LogTextureFormatOodle: Display: Oodle Texture TFO init; latest sdk version = 2.9.13
[2025.09.15-16.58.23:244][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.13.dll
[2025.09.15-16.58.23:245][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.5.dll
[2025.09.15-16.58.23:245][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatOodle
[2025.09.15-16.58.23:272][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android'
[2025.09.15-16.58.23:272][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ASTC'
[2025.09.15-16.58.23:272][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_DXT'
[2025.09.15-16.58.23:272][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ETC2'
[2025.09.15-16.58.23:272][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_OpenXR'
[2025.09.15-16.58.23:272][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'AndroidClient'
[2025.09.15-16.58.23:272][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ASTCClient'
[2025.09.15-16.58.23:272][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_DXTClient'
[2025.09.15-16.58.23:272][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ETC2Client'
[2025.09.15-16.58.23:272][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_OpenXRClient'
[2025.09.15-16.58.23:272][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_Multi'
[2025.09.15-16.58.23:272][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_MultiClient'
[2025.09.15-16.58.23:284][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'IOS'
[2025.09.15-16.58.23:284][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'IOSClient'
[2025.09.15-16.58.23:297][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Linux'
[2025.09.15-16.58.23:297][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxEditor'
[2025.09.15-16.58.23:297][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxServer'
[2025.09.15-16.58.23:297][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxClient'
[2025.09.15-16.58.23:315][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Mac'
[2025.09.15-16.58.23:317][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'MacEditor'
[2025.09.15-16.58.23:317][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'MacServer'
[2025.09.15-16.58.23:317][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'MacClient'
[2025.09.15-16.58.23:330][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'TVOS'
[2025.09.15-16.58.23:330][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'TVOSClient'
[2025.09.15-16.58.23:343][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Windows'
[2025.09.15-16.58.23:343][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'WindowsEditor'
[2025.09.15-16.58.23:343][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'WindowsServer'
[2025.09.15-16.58.23:343][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'WindowsClient'
[2025.09.15-16.58.23:343][  0]LogTargetPlatformManager: Display: Building Assets For WindowsEditor
[2025.09.15-16.58.23:373][  0]LogTargetPlatformManager: Unable to find shader format SF_METAL_ES3_1_IOS from hinted modules, loading all potential format modules to find it
[2025.09.15-16.58.23:378][  0]LogTargetPlatformManager: Loaded format module MetalShaderFormat
[2025.09.15-16.58.23:378][  0]LogTargetPlatformManager:   SF_METAL_ES3_1_IOS
[2025.09.15-16.58.23:378][  0]LogTargetPlatformManager:   SF_METAL_SM5_IOS
[2025.09.15-16.58.23:378][  0]LogTargetPlatformManager:   SF_METAL_ES3_1_TVOS
[2025.09.15-16.58.23:378][  0]LogTargetPlatformManager:   SF_METAL_SM5_TVOS
[2025.09.15-16.58.23:378][  0]LogTargetPlatformManager:   SF_METAL_SM5
[2025.09.15-16.58.23:378][  0]LogTargetPlatformManager:   SF_METAL_SM6
[2025.09.15-16.58.23:378][  0]LogTargetPlatformManager:   SF_METAL_SIM
[2025.09.15-16.58.23:378][  0]LogTargetPlatformManager:   SF_METAL_ES3_1
[2025.09.15-16.58.23:378][  0]LogTargetPlatformManager: Loaded format module ShaderFormatD3D
[2025.09.15-16.58.23:378][  0]LogTargetPlatformManager:   PCD3D_SM6
[2025.09.15-16.58.23:378][  0]LogTargetPlatformManager:   PCD3D_SM5
[2025.09.15-16.58.23:378][  0]LogTargetPlatformManager:   PCD3D_ES31
[2025.09.15-16.58.23:378][  0]LogTargetPlatformManager: Loaded format module ShaderFormatOpenGL
[2025.09.15-16.58.23:378][  0]LogTargetPlatformManager:   GLSL_150_ES31
[2025.09.15-16.58.23:378][  0]LogTargetPlatformManager:   GLSL_ES3_1_ANDROID
[2025.09.15-16.58.23:378][  0]LogTargetPlatformManager: Loaded format module ShaderFormatVectorVM
[2025.09.15-16.58.23:378][  0]LogTargetPlatformManager:   VVM_1_0
[2025.09.15-16.58.23:378][  0]LogTargetPlatformManager: Loaded format module VulkanShaderFormat
[2025.09.15-16.58.23:378][  0]LogTargetPlatformManager:   SF_VULKAN_SM5
[2025.09.15-16.58.23:378][  0]LogTargetPlatformManager:   SF_VULKAN_ES31_ANDROID
[2025.09.15-16.58.23:378][  0]LogTargetPlatformManager:   SF_VULKAN_ES31
[2025.09.15-16.58.23:378][  0]LogTargetPlatformManager:   SF_VULKAN_SM5_ANDROID
[2025.09.15-16.58.23:378][  0]LogTargetPlatformManager:   SF_VULKAN_SM6
[2025.09.15-16.58.23:378][  0]LogRendererCore: Ray tracing is disabled. Reason: disabled through project setting (r.RayTracing=0).
[2025.09.15-16.58.23:381][  0]LogDerivedDataCache: Display: Memory: Max Cache Size: -1 MB
[2025.09.15-16.58.23:381][  0]LogDerivedDataCache: FDerivedDataBackendGraph: Pak pak cache file ../../../../../../Auracron/DerivedDataCache/DDC.ddp not found, will not use a pak cache.
[2025.09.15-16.58.23:381][  0]LogDerivedDataCache: Unable to find inner node Pak for hierarchy Hierarchy.
[2025.09.15-16.58.23:381][  0]LogDerivedDataCache: FDerivedDataBackendGraph: CompressedPak pak cache file ../../../../../../Auracron/DerivedDataCache/Compressed.ddp not found, will not use a pak cache.
[2025.09.15-16.58.23:381][  0]LogDerivedDataCache: Unable to find inner node CompressedPak for hierarchy Hierarchy.
[2025.09.15-16.58.23:591][  0]LogDerivedDataCache: Display: ../../../Engine/DerivedDataCache/Compressed.ddp: Opened pak cache for reading. (1350 MiB)
[2025.09.15-16.58.23:591][  0]LogDerivedDataCache: FDerivedDataBackendGraph: EnterprisePak pak cache file ../../../Enterprise/DerivedDataCache/Compressed.ddp not found, will not use a pak cache.
[2025.09.15-16.58.23:591][  0]LogDerivedDataCache: Unable to find inner node EnterprisePak for hierarchy Hierarchy.
[2025.09.15-16.58.23:591][  0]LogZenServiceInstance: Found Zen config default=C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Data
[2025.09.15-16.58.23:592][  0]LogZenServiceInstance: InTree version at 'C:/Program Files/Epic Games/UE_5.6/Engine/Binaries/Win64/zenserver.exe' is '5.6.6-202504241958-windows-x64-release-f090a03'
[2025.09.15-16.58.23:592][  0]LogZenServiceInstance: Installed version at 'C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Install/zenserver.exe' is '5.6.6-202504241958-windows-x64-release-f090a03'
[2025.09.15-16.58.23:592][  0]LogZenServiceInstance: Found existing instance running on port 8558 matching our settings, no actions needed
[2025.09.15-16.58.24:127][  0]LogTurnkeySupport: Completed SDK detection: ExitCode = 0
[2025.09.15-16.58.24:197][  0]LogZenServiceInstance: Display: Unreal Zen Storage Server HTTP service at [::1]:8558 status: OK!.
[2025.09.15-16.58.24:197][  0]LogZenServiceInstance: Local ZenServer AutoLaunch initialization completed in 0.606 seconds
[2025.09.15-16.58.24:199][  0]LogDerivedDataCache: Display: ZenLocal: Using ZenServer HTTP service at http://[::1]:8558/ with namespace ue.ddc status: OK!.
[2025.09.15-16.58.24:205][  0]LogDerivedDataCache: C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: Speed tests took 0.01 seconds.
[2025.09.15-16.58.24:205][  0]LogDerivedDataCache: Display: C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: Performance: Latency=0.02ms. RandomReadSpeed=1316.09MBs, RandomWriteSpeed=165.55MBs. Assigned SpeedClass 'Local'
[2025.09.15-16.58.24:208][  0]LogDerivedDataCache: Local: Using data cache path C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: DeleteOnly
[2025.09.15-16.58.24:208][  0]LogDerivedDataCache: ZenShared: Disabled because Host is set to 'None'
[2025.09.15-16.58.24:208][  0]LogDerivedDataCache: Unable to find inner node ZenShared for hierarchy Hierarchy.
[2025.09.15-16.58.24:208][  0]LogDerivedDataCache: Shared: Disabled because no path is configured.
[2025.09.15-16.58.24:208][  0]LogDerivedDataCache: Unable to find inner node Shared for hierarchy Hierarchy.
[2025.09.15-16.58.24:208][  0]LogDerivedDataCache: Cloud: Disabled because Host is set to 'None'
[2025.09.15-16.58.24:208][  0]LogDerivedDataCache: Unable to find inner node Cloud for hierarchy Hierarchy.
[2025.09.15-16.58.24:208][  0]LogShaderCompilers: Guid format shader working directory is 19 characters bigger than the processId version (../../../../../../Auracron/Intermediate/Shaders/WorkingDirectory/14324/).
[2025.09.15-16.58.24:208][  0]LogShaderCompilers: Cleaned the shader compiler working directory 'C:/Users/<USER>/AppData/Local/Temp/UnrealShaderWorkingDir/EF84E3AB4DCC739D08A3918B15405AB0/'.
[2025.09.15-16.58.24:208][  0]LogXGEController: Cannot use XGE Controller as Incredibuild is not installed on this machine.
[2025.09.15-16.58.24:209][  0]LogUbaHorde: Display: UBA/Horde Configuration [Uba.Provider.Horde]: Not Enabled
[2025.09.15-16.58.24:209][  0]LogShaderCompilers: Display: Using 9 local workers for shader compilation
[2025.09.15-16.58.24:211][  0]LogShaderCompilers: Display: Compiling shader autogen file: ../../../../../../Auracron/Intermediate/ShaderAutogen/PCD3D_SM5/AutogenShaderHeaders.ush
[2025.09.15-16.58.24:211][  0]LogShaderCompilers: Display: Autogen file is unchanged, skipping write.
[2025.09.15-16.58.25:312][  0]LogSlate: Using FreeType 2.10.0
[2025.09.15-16.58.25:313][  0]LogSlate: SlateFontServices - WITH_FREETYPE: 1, WITH_HARFBUZZ: 1
[2025.09.15-16.58.25:316][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png' error.
[2025.09.15-16.58.25:316][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png' error.
[2025.09.15-16.58.25:316][  0]LogSlate: Could not load file for Slate resource: [../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png] file: [../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png]
[2025.09.15-16.58.25:316][  0]LogSlate: Could not load file for Slate resource: [../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png] file: [../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png]
[2025.09.15-16.58.25:344][  0]LogAssetRegistry: FAssetRegistry took 0.0032 seconds to start up
[2025.09.15-16.58.25:346][  0]LogEditorDomain: Display: EditorDomain is Disabled
[2025.09.15-16.58.25:418][  0]LogAssetRegistry: Display: AssetDataGatherer spent 0.001s loading caches ../../../../../../Auracron/Intermediate/CachedAssetRegistry_*.bin.
[2025.09.15-16.58.25:464][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Wall_Magic_Edges_BaseColor.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:464][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Wall_Magic_Edges_Emissive.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:464][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Wall_Magic_Edges_BaseColor.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:464][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Wall_Magic_Edges_Emissive.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:464][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Wall_Magic_Blocks_Crystals_BaseColor.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:464][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Wall_Magic_Blocks_Crystals_BaseColor.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:464][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Wall_Magic_Blocks_MAT.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:464][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Wall_Magic_Blocks_MAT.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:464][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Wall_Magic_Blocks_BaseColor.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:464][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Wall_Magic_Blocks_BaseColor.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:464][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Wall_Base_Front_04_Normal.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:464][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Wall_Base_Front_04_Normal.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:464][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Wall_Magic_Blocks.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:464][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Wall_Magic_Blocks.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:464][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Wall_Magic_Blocks_Crystal_Opacity.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:464][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Wall_Magic_Blocks_Crystal_Opacity.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:464][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Wall_Base_Front_04_MAT.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:464][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Wall_Base_Front_04_MAT.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:464][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Wall_Base_Front_04_MRA.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:464][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Wall_Base_Front_04.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:464][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Wall_Base_Front_04_MRA.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:464][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Wall_Base_Front_04.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:464][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Wall_Magic_Blocks_Edges_MAT.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:464][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Wall_Magic_Blocks_Edges_MAT.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:464][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Wall_Base_Front_03_MRA.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:464][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Wall_Base_Front_03_MRA.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:464][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Wall_Base_Front_03_MAT.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:464][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Wall_Base_Front_03_MAT.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:464][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Wall_Base_Front_03_Normal.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:464][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Wall_Base_Front_03_Normal.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:464][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Wall_Base_Front_03.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:464][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Wall_Base_Front_03.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:464][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Wall_Base_Front_03_BaseColor.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:464][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Wall_Base_Front_02_Normal.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:464][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Wall_Base_Front_03_BaseColor.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:464][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Wall_Base_Front_02_Normal.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:464][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Wall_Base_Front_02_MRA.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:464][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Wall_Base_Front_02_MRA.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:464][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Wall_Magic_Blocks_Normal.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:464][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Wall_Magic_Blocks_Normal.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:464][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Wall_Base_Front_04_BaseColor.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:464][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Wall_Base_Front_04_BaseColor.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:464][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Wall_Base_Front_02_MAT.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:464][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Wall_Base_Front_02_MAT.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:464][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Wall_Magic_Blocks_MRA.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:464][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Wall_Magic_Blocks_MRA.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:464][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Wall_Base_Front_01_MRA.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:464][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Wall_Base_Front_01_MRA.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:464][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Wall_Base_Front_02_BaseColor.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:464][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Wall_Base_Front_01_MAT.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:464][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Wall_Base_Front_02_BaseColor.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:464][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Wall_Base_Front_01_MAT.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:464][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Wall_Base_Front_01_Normal.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:464][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Wall_Base_Front_01_Normal.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:464][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Wall_Base_Front_01.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:464][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Wall_Base_Front_01.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:464][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Wall_Base_Front_01_BaseColor.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:464][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Wall_Base_Front_01_BaseColor.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:464][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Wall_Base_02_Normal.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:464][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Wall_Base_02_Normal.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:464][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Wall_Base_02_MRA.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:464][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Wall_Base_02_MRA.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:464][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Wall_Base_02.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:465][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Wall_Base_02_BaseColor.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:465][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Wall_Base_02_BaseColor.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:465][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Wall_Base_02.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:465][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Wall_Base_01_MRA.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:465][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Wall_Base_01_MRA.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:465][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Wall_Base_01_Normal.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:465][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Wall_Base_01_MAT.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:465][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Wall_Base_01_Normal.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:465][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Wall_Base_01_MAT.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:465][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Wall_Alcove_02_03_Normal.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:465][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Wall_Alcove_02_03_Normal.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:465][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Wall_Alcove_02_03_MRA.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:465][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Wall_Base_01.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:465][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Wall_Alcove_02_03_MAT.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:465][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Wall_Alcove_02_03_MRA.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:465][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Wall_Base_01.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:465][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Wall_Alcove_02_03_MAT.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:465][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Wall_Base_01_BaseColor.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:465][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Wall_Base_01_BaseColor.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:465][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Wall_Alcove_02_03.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:465][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Wall_Alcove_02_03.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:465][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Wall_Alcove_02_03_BaseColor.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:465][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Wall_Alcove_02_03_BaseColor.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:465][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Wall_Base_02_MAT.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:465][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Wall_Base_02_MAT.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:465][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Wall_Alcove_02_02_MRA.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:465][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Wall_Alcove_02_02_Normal.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:465][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Wall_Alcove_02_02_MRA.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:465][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Wall_Alcove_02_02_Normal.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:465][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Wall_Alcove_02_02_MAT.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:465][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Wall_Alcove_02_02_MAT.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:465][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Wall_Alcove_02_01_MRA.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:465][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Wall_Alcove_02_01_Normal.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:465][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Wall_Alcove_02_01_MRA.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:465][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Wall_Alcove_02_01_MAT.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:465][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Wall_Alcove_02_01_Normal.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:465][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Wall_Alcove_02_01_MAT.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:465][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Wall_Alcove_02_02.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:465][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Wall_Alcove_02_02.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:465][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Wall_Alcove_02_02_BaseColor.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:465][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Wall_Alcove_02_02_BaseColor.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:465][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Wall_Base_Front_02.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:465][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Wall_Base_Front_02.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:465][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Wall_Alcove_01_03_Normal.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:465][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Wall_Alcove_01_03_Normal.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:465][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Wall_Alcove_01_03_BaseColor.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:465][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Wall_Alcove_01_03_BaseColor.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:465][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Wall_Alcove_02_01_BaseColor.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:465][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Wall_Alcove_02_01_BaseColor.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:465][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Wall_Alcove_02_01.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:465][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Wall_Alcove_02_01.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:465][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Wall_Alcove_01_02_MRA.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:465][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Wall_Alcove_01_02_MRA.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:465][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Wall_Alcove_01_02_MAT.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:465][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Wall_Alcove_01_02_MAT.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:465][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Wall_Alcove_01_02_Normal.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:465][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Wall_Alcove_01_03.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:465][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Wall_Alcove_01_02_Normal.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:465][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Wall_Alcove_01_03.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:465][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Wall_Alcove_01_01_Normal.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:465][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Wall_Alcove_01_01_Normal.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:465][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Wall_Alcove_01_02_BaseColor.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:465][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Wall_Alcove_01_02_BaseColor.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:465][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Wall_Alcove_01_01_MAT.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:465][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Wall_Alcove_01_01_MAT.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:465][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Wall_Alcove_01_02.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:465][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Wall_Alcove_01_02.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:465][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Wall_07_Normal.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:465][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Wall_07_Normal.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:465][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Wall_Alcove_01_01_MRA.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:465][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Wall_Alcove_01_01_MRA.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:465][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Wall_Alcove_01_01_BaseColor.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:465][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Wall_Alcove_01_01_BaseColor.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:465][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Wall_Alcove_01_01.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:465][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Wall_Alcove_01_01.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:465][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Wall_07_MAT.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:465][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Wall_07_MAT.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:465][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Wall_Alcove_01_03_MRA.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:465][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Wall_Alcove_01_03_MRA.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:465][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Wall_07_BaseColor.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:465][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Wall_07_BaseColor.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:465][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Wall_07.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:465][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Wall_07.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:465][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Wall_06_Normal.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:465][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Wall_06_Normal.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:465][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Wall_06_MRA.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:465][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Wall_06_BaseColor.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:465][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Wall_06_MRA.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:465][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Wall_06_BaseColor.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:465][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Wall_05_MRA.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:465][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Wall_06_MAT.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:465][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Wall_05_MRA.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:465][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Wall_06_MAT.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:465][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Wall_05_Normal.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:465][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Wall_05_Normal.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:465][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Wall_05_MAT.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:465][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Wall_05_MAT.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:465][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Wall_Alcove_01_03_MAT.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:465][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Wall_Alcove_01_03_MAT.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:465][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Wall_07_MRA.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:465][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Wall_07_MRA.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:465][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Wall_06.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:465][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Wall_06.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:465][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Wall_05.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:465][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Wall_05.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:465][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Wall_04_MAT.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:465][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Wall_04_MAT.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:465][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Wall_05_BaseColor.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:465][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Wall_05_BaseColor.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:465][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Wall_04.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:465][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Wall_04.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:465][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Wall_03_Normal.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:465][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Wall_03_Normal.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:465][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Wall_04_MRA.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:465][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Wall_04_MRA.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:465][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Wall_03_MRA.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:465][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Wall_03_MRA.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:465][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Wall_04_BaseColor.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:465][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Wall_04_BaseColor.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:465][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Wall_03_BaseColor.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:465][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Wall_03_BaseColor.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:465][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Wall_03.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:465][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Wall_03.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:466][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Wall_03_MAT.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:466][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Wall_03_MAT.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:466][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Wall_02_MRA.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:466][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Wall_02_MRA.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:466][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Wall_02_BaseColor.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:466][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Wall_02_BaseColor.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:466][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Wall_02_Normal.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:466][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Wall_02_Normal.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:466][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Wall_02.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:466][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Wall_02.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:466][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Wall_01_MAT.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:466][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Wall_01_MAT.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:466][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Wall_01_Normal.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:466][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Wall_01_Normal.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:466][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Wall_01_BaseColor.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:466][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Wall_01_BaseColor.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:466][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Wall_01.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:466][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Wall_01.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:466][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Wall_02_MAT.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:466][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Turret_Red_Normal.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:466][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Turret_Red_Normal.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:466][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Wall_02_MAT.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:466][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Turret_Red_MRA.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:466][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Turret_Red_MRA.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:466][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Turret_Red_MAT.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:466][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Wall_01_MRA.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:466][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Turret_Red_MAT.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:466][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Wall_01_MRA.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:466][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Turret_Red_Flags_PhysicsAsset.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:466][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Turret_Red_Flags_PhysicsAsset.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:466][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Turret_Red_Flags_Normal.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:466][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Turret_Red_Flags_Normal.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:466][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Turret_Red_Flags_MAT.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:466][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Turret_Red_Flags_MAT.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:466][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Turret_Red_Flags_Skeleton.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:466][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Turret_Red_Flags_Skeleton.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:466][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Turret_Red_Flags_MRA.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:466][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Turret_Red_Flags_MRA.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:466][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Wall_04_Normal.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:466][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Wall_04_Normal.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:466][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Turret_Red_Flags_BaseColor.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:466][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Turret_Red_Flags_BaseColor.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:466][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Turret_Red_Flags.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:466][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Turret_Red_Flags.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:466][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Turret_Plating_02_Normal.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:466][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Turret_Plating_02_Normal.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:466][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Turret_Red_Emissive.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:466][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Turret_Red_Emissive.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:466][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Turret_Red_Crystal_BaseColor.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:466][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Turret_Red_Crystal_BaseColor.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:466][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Turret_Plating_02_MAT.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:466][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Turret_Plating_02_MAT.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:466][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Turret_Red.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:466][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Turret_Red.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:466][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Turret_Plating_02_Crystal_Opacity.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:466][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Turret_Plating_02_Crystal_Opacity.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:466][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Turret_Plating_02_MRA.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:466][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Turret_Plating_02_MRA.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:466][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Turret_Red_Crystal_Opacity.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:466][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Turret_Red_Crystal_Opacity.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:466][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Turret_Plating_02.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:466][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Turret_Plating_02.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:466][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Turret_Plating_01_Normal.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:466][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Turret_Plating_02_Crystal_BaseColor.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:466][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Turret_Plating_01_Normal.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:466][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Turret_Plating_02_Crystal_BaseColor.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:466][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Turret_Plating_02_BaseColor.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:466][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Turret_Plating_02_BaseColor.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:466][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Turret_Plating_01_Crystal_Opacity.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:466][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Turret_Plating_01_Crystal_Opacity.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:466][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Turret_Plating_01_MAT.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:466][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Turret_Plating_01_MAT.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:466][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Turret_Plating_01_Crystal_BaseColor.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:466][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Turret_Plating_01_Crystal_BaseColor.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:466][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Turret_Plating_01_BaseColor.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:466][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Turret_Plating_01_MRA.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:466][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Turret_Plating_01_BaseColor.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:466][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Turret_Plating_01_MRA.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:466][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Turret_Blue_MRA.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:466][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Turret_Blue_MRA.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:466][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Turret_Plating_01.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:466][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Turret_Blue_MAT.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:466][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Turret_Blue_Normal.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:466][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Turret_Blue_MAT.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:466][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Turret_Plating_01.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:466][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Turret_Blue_Normal.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:466][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Turret_Blue_Crystal_BaseColor.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:466][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Turret_Blue_Crystal_BaseColor.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:466][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Turret_Blue.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:466][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Turret_Blue.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:466][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Spawn_Statue_Red_Normal.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:466][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Spawn_Statue_Red_Normal.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:466][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Turret_Blue_Crystal_Opacity.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:466][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Turret_Blue_Crystal_Opacity.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:466][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Turret_Blue_BaseColor.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:466][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Turret_Blue_BaseColor.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:466][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Spawn_Statue_Red_MRA.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:466][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Spawn_Statue_Red_MRA.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:466][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Turret_Red_BaseColor.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:466][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Turret_Red_BaseColor.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:466][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Spawn_Statue_Red_Crystal_Opacity.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:466][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Spawn_Statue_Red_Crystal_Opacity.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:466][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Spawn_Statue_Red_MAT.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:467][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Spawn_Statue_Red_MAT.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:467][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Spawn_Statue_Cloth_Flags_MAT.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:467][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Spawn_Statue_Cloth_Flags_MAT.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:467][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Spawn_Statue_Blue_Normal.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:467][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Spawn_Statue_Red_Cloth_BaseColor.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:467][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Spawn_Statue_Blue_Normal.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:467][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Spawn_Statue_Red_Cloth_BaseColor.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:467][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Spawn_Statue_Red_Crystal_BaseColor.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:467][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Spawn_Statue_Red_Crystal_BaseColor.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:467][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Spawn_Statue_Red_BaseColor.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:467][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Spawn_Statue_Red_Cloth_MRA.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:467][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Spawn_Statue_Red_BaseColor.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:467][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Spawn_Statue_Red_Cloth_MRA.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:467][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Spawn_Statue_Blue_Crystal_Opacity.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:467][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Spawn_Statue_Blue_MAT.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:467][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Spawn_Statue_Blue_Crystal_Opacity.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:467][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Spawn_Statue_Blue_MAT.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:467][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Spawn_Statue_Blue_Crystal_BaseColor.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:467][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Spawn_Statue_Blue_Crystal_BaseColor.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:467][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Spawn_Statue_Blue_MRA.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:467][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Spawn_Statue_Blue_MRA.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:467][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Spawn_Red_Stones_BaseColor.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:467][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Spawn_Red_Stones_BaseColor.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:467][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Spawn_Red_Stones_MAT.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:467][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Spawn_Red_Stones_MAT.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:467][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Spawn_Statue_Blue_BaseColor.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:467][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Spawn_Statue_Blue_BaseColor.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:467][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Spawn_Red_Stones_Normal.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:467][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Spawn_Red_Stones_Normal.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:467][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Spawn_Red_Stones_MRA.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:467][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Spawn_Red_Stones.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:467][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Spawn_Red_Stones_MRA.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:467][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Spawn_Red_Stones.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:467][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Spawn_Red_Statue_PhysicsAsset.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:467][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Spawn_Red_Statue_PhysicsAsset.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:467][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Spawn_Red_Statue.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:467][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Spawn_Red_Statue.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:467][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Spawn_Red_Statue_Skeleton.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:467][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Spawn_Red_Statue_Skeleton.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:467][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Spawn_Red_Normal.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:467][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Spawn_Blue_Stones_Normal.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:467][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Spawn_Red_Normal.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:467][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Spawn_Blue_Stones_Normal.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:467][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Spawn_Red_MRA.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:467][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Spawn_Red_MAT.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:467][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Spawn_Blue_Stones_MRA.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:467][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Spawn_Red_MRA.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:467][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Spawn_Red_MAT.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:467][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Spawn_Blue_Stones_MRA.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:467][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Spawn_Red_BaseColor.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:467][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Spawn_Red_BaseColor.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:467][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Spawn_Blue_Stones_BaseColor.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:467][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Spawn_Blue_Stones_BaseColor.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:467][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Spawn_Blue_Stones_MAT.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:467][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Spawn_Blue_Stones_MAT.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:467][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Spawn_Statue_Red_Cloth_Normal.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:467][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Spawn_Statue_Red_Cloth_Normal.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:467][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Spawn_Blue_Stones.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:467][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Spawn_Blue_Stones.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:467][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Spawn_Blue_MRA.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:467][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Spawn_Blue_MRA.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:467][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Spawn_Blue_Statue.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:467][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Spawn_Blue_BaseColor.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:467][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Spawn_Blue_Normal.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:467][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Spawn_Blue_BaseColor.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:467][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Spawn_Blue_Statue.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:467][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Spawn_Blue_Normal.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:467][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Spawn.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:467][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Spawn.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:467][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Nexus_Red_MAT.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:467][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Nexus_Red_MAT.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:467][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Nexus_Red_Normal.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:467][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Nexus_Red_Normal.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:467][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Nexus_Red_Mask.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:467][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Nexus_Red_Mask.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:467][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Nexus_Red_Emissive.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:467][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Nexus_Red_Crystal_BaseColor.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:467][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Nexus_Red_Emissive.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:467][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Nexus_Red_Crystal_BaseColor.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:467][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Nexus_Red_Crystal_Opacity.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:467][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Nexus_Red_Crystal_Opacity.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:467][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Nexus_Red_BaseColor.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:467][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Nexus_Red_BaseColor.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:467][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Nexus_Blue_Normal.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:467][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Nexus_Blue_Normal.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:467][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Nexus_Red.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:467][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Nexus_Red.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:467][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Nexus_Blue_MRA.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:467][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Nexus_Blue_MRA.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:467][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Nexus_Blue_MAT.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:467][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Nexus_Blue_MAT.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:467][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Nexus_Blue_Crystal_Opacity.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:467][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Nexus_Blue_Crystal_Opacity.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:467][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Nexus_Blue_Emissive.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:467][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Nexus_Blue_Emissive.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:467][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Nexus_Blue_Crystal_BaseColor.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:467][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Nexus_Blue_Crystal_BaseColor.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:467][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Inhib_Red.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:467][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Inhib_Red.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:467][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Nexus_Blue_BaseColor.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:467][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Nexus_Blue.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:467][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Nexus_Blue_BaseColor.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:467][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Nexus_Blue.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:467][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Inhib_Blue.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:467][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Inhib_Blue.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:467][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Inhibitor_Red_MRA.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:467][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Inhibitor_Red_MRA.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:468][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Inhibitor_Red_Normal.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:468][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/MidStairs.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:468][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Inhibitor_Red_Normal.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:468][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/MidStairs.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:468][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Nexus_Red_MRA.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:468][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Nexus_Red_MRA.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:468][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Spawn_Blue_MAT.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:468][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Spawn_Blue_MAT.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:468][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Nexus_Blue_Mask.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:468][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Nexus_Blue_Mask.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:468][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Inhibitor_Red_MAT.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:468][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Inhibitor_Red_Crystal_Opacity.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:468][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Inhibitor_Red_Crystal_Opacity.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:468][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Inhibitor_Red_MAT.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:468][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Inhibitor_Blue_Normal.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:468][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Inhibitor_Blue_Normal.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:468][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Inhibitor_Red_BaseColor.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:468][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Inhibitor_Red_BaseColor.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:468][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Inhibitor_Blue_MRA.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:468][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Inhibitor_Blue_MRA.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:468][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Inhibitor_Blue_Mask.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:468][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Inhibitor_Blue_Mask.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:468][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Inhibitor_Blue_Crystal_BaseColor.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:468][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Inhibitor_Blue_Crystal_BaseColor.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:468][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Inhibitor_Blue_Crystal_Opacity.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:468][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Inhibitor_Blue_Emissive.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:468][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Inhibitor_Blue_Crystal_Opacity.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:468][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Inhibitor_Blue_Emissive.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:468][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Inhibitor_Blue_BaseColor.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:468][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Inhibitor_Blue_BaseColor.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:468][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Floor_Red_Stones_02_MAT.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:468][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Inhibitor_Red_Crystal_BaseColor.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:468][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Floor_Red_Stones_02_MAT.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:468][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Inhibitor_Red_Crystal_BaseColor.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:468][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Floor_Red_Stones_02_MRA.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:468][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Floor_Red_Stones_02_MRA.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:468][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Floor_Red_Stones_02.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:468][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Floor_Red_Stones_02.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:468][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Floor_Red_Stones_02_BaseColor.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:468][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Floor_Red_Stones_02_BaseColor.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:468][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Floor_Red_Stones.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:468][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Floor_Red_Stones.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:468][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Floor_Red_Normal.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:468][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Floor_Red_Normal.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:468][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Inhibitor_Red_Emissive.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:468][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Floor_Red_Stones_02_Normal.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:468][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Inhibitor_Red_Emissive.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:468][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Floor_Red_Stones_02_Normal.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:468][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Floor_Red_MAT.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:468][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Floor_Red_MAT.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:468][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Floor_Blue_Stones_02_MAT.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:468][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Floor_Red_Height.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:468][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Floor_Blue_Stones_02_MAT.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:468][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Floor_Blue_Stones_02_Normal.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:468][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Floor_Red_Height.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:468][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Floor_Blue_Stones_02_Normal.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:468][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Floor_Red_BaseColor.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:468][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Floor_Red_MRA.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:468][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Floor_Red_BaseColor.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:468][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Floor_Red_MRA.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:468][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Floor_Red.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:468][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Floor_Red.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:468][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Floor_Blue_Stones_02_MRA.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:468][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Floor_Blue_Stones_02_MRA.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:468][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Floor_Blue_Stones_02_BaseColor.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:468][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Floor_Blue_Stones_02_BaseColor.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:468][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Inhibitor_Blue_MAT.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:468][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Floor_Blue_Stones_02.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:468][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Inhibitor_Blue_MAT.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:468][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Floor_Blue_Stones_02.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:468][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Floor_Blue_Normal.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:468][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Floor_Blue_Normal.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:468][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Floor_Blue_Stones.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:468][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Floor_Blue_Stones.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:468][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Floor_Blue_Height.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:468][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Floor_Blue_Height.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:468][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Floor_Blue_MAT.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:468][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Floor_Blue_MRA.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:468][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Crystal_MAT_RedStatue.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:468][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Floor_Blue_MAT.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:468][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Floor_Blue_MRA.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:468][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Crystal_MAT_RedStatue.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:468][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Floor_Blue_BaseColor.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:468][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Floor_Blue_BaseColor.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:468][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Crystal_MAT_RedNexus.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:468][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Crystal_MAT_RedTurret.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:468][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Crystal_MAT_RedNexus.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:468][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Crystal_MAT_RedTurret.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:468][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Crystal_MAT_WallMagicBlocks.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:468][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Crystal_MAT_WallMagicBlocks.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:468][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Crystal_MAT_BlueNexus.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:468][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Crystal_MAT_BlueNexus.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:468][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Crystal_MAT_BlueInhibitor.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:468][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Crystal_MAT_RedInhibitor.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:468][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Crystal_MAT_BlueInhibitor.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:468][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Crystal_MAT_RedInhibitor.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:468][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Crystal_MAT_BlueTurretPlating.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:468][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Crystal_MAT_BlueTurretPlating.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:468][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Crystal_MAT_BlueStatue.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:468][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Crystal_MAT_BlueStatue.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:468][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Crystal_MAT_BlueTurret.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:468][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Crystal_MAT_BlueTurret.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:468][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Brazier_06_MRA.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:468][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Brazier_06_MRA.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:468][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Crystal_MAT.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:468][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Crystal_MAT.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:468][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Brazier_06_Normal.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:468][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Brazier_06_Normal.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:468][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Brazier_06_MAT.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:468][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Brazier_06_MAT.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:468][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Brazier_06_BaseColor.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:468][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Brazier_06.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:468][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Brazier_06_BaseColor.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:468][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Brazier_06.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:468][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Brazier_05_MRA.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:468][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Brazier_05_Normal.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:468][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Brazier_05_MRA.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:468][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Brazier_05_Normal.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:468][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Brazier_05_BaseColor.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:468][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Brazier_05_BaseColor.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:468][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Brazier_05_MAT.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:468][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Brazier_05_MAT.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:468][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Brazier_05.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:468][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Brazier_05.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:468][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Brazier_04_Normal.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:470][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Brazier_04_Normal.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:470][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Floor_Blue.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:470][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Floor_Blue.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:470][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Crystal_MAT_RedTurretPlating.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:470][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Crystal_MAT_RedTurretPlating.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:470][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Brazier_04_MRA.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:470][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Brazier_04_MRA.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:470][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Brazier_04_MAT.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:470][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Brazier_04_BaseColor.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:470][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Brazier_04_BaseColor.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:470][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Brazier_03_MAT.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:470][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Brazier_04_MAT.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:470][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Brazier_03_MAT.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:470][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Brazier_03_Normal.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:470][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Brazier_03_Normal.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:470][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Brazier_03.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:470][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Brazier_03_MRA.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:470][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Brazier_03.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:470][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Brazier_03_MRA.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:470][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Brazier_03_BaseColor.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:470][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Brazier_03_BaseColor.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:470][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Brazier_02_MRA.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:470][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Brazier_02_MRA.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:470][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Brazier_02_BaseColor.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:470][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Brazier_02_BaseColor.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:470][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Brazier_02.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:470][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Brazier_02.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:470][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Brazier_02_Normal.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:470][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Brazier_02_MAT.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:470][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Brazier_02_Normal.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:470][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Brazier_02_MAT.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:470][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Brazier_01_Normal.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:470][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Brazier_01_Normal.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:470][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Brazier_01_MAT.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:470][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Brazier_01_MAT.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:470][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Brazier_01_BaseColor.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:470][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Brazier_01_BaseColor.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:470][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Brazier_01.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:470][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Brazier_01_MRA.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:470][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Brazier_01.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:470][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Brazier_01_MRA.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:470][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Alcove_Stone_BaseColor.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:470][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Alcove_Stone_BaseColor.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:470][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Alcove_Stone_Normal.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:470][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Alcove_Stone.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:470][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Alcove_Stone_Normal.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:470][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Alcove_Stone_MRA.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:470][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Alcove_Stone.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:470][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Alcove_Stone_MRA.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:470][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Alcove_STone_MAT.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:470][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Alcove_STone_MAT.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:470][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Alcove_Skull_Normal.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:470][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Alcove_Skull_Normal.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:470][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Alcove_Skull_MRA.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:470][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Alcove_Skull_MRA.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:470][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Alcove_Skull_BaseColor.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:470][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Alcove_Skull_BaseColor.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:470][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Alcove_Skull.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:470][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Alcove_Skull.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:470][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Brazier_04.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:470][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Brazier_04.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:470][  0]LogLinker: Warning: Failed to read package file summary, the file "C:/Auracron/Content/Env/RealLeague/Structures/Alcove_Skull_MAT.uasset" is unversioned and we cannot safely load unversioned files in the editor.
[2025.09.15-16.58.25:470][  0]LogAssetRegistry: Warning: Package is unloadable: C:/Auracron/Content/Env/RealLeague/Structures/Alcove_Skull_MAT.uasset. Reason: Package was saved unversioned and the current process does not support loading unversioned packages.
[2025.09.15-16.58.25:692][  0]LogDeviceProfileManager: Display: Deviceprofile LinuxArm64Editor not found.
[2025.09.15-16.58.25:692][  0]LogDeviceProfileManager: Display: Deviceprofile LinuxArm64 not found.
[2025.09.15-16.58.25:736][  0]LogDeviceProfileManager: Active device profile: [000001A3F37C6680][000001A3CCC3C800 66] WindowsEditor
[2025.09.15-16.58.25:736][  0]LogCsvProfiler: Display: Metadata set : deviceprofile="WindowsEditor"
[2025.09.15-16.58.25:739][  0]LogStreaming: Display: FlushAsyncLoading(1): 1 QueuedPackages, 0 AsyncPackages
[2025.09.15-16.58.25:742][  0]LogTextureEncodingSettings: Display: Texture Encode Speed: FinalIfAvailable (editor).
[2025.09.15-16.58.25:742][  0]LogTextureEncodingSettings: Display: Oodle Texture Encode Speed settings: Fast: RDO Off Lambda=0, Effort=Normal Final: RDO Off Lambda=0, Effort=Normal
[2025.09.15-16.58.25:742][  0]LogTextureEncodingSettings: Display: Shared linear texture encoding: Disabled
[2025.09.15-16.58.25:752][  0]LogTurnkeySupport: Turnkey Platform: Win64: (Status=Valid, MinAllowed_Sdk=10.0.19041.0, MaxAllowed_Sdk=10.9.99999.0, Current_Sdk=10.0.26100.0, Allowed_AutoSdk=10.0.22621.0, Current_AutoSdk=, Flags="InstalledSdk_ValidVersionExists")
[2025.09.15-16.58.25:753][  0]LogTurnkeySupport: Running Turnkey device detection: ' -ScriptsForProject="C:/Auracron/EnvDesigns.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="C:/Auracron/Intermediate/TurnkeyReport_1.log" -log="C:/Auracron/Intermediate/TurnkeyLog_1.log" -project="C:/Auracron/EnvDesigns.uproject"  -Device=Win64@TKT'
[2025.09.15-16.58.25:753][  0]LogMonitoredProcess: Running Serialized UAT: [ cmd.exe /c ""C:/Program Files/Epic Games/UE_5.6/Engine/Build/BatchFiles/RunUAT.bat"  -ScriptsForProject="C:/Auracron/EnvDesigns.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="C:/Auracron/Intermediate/TurnkeyReport_1.log" -log="C:/Auracron/Intermediate/TurnkeyLog_1.log" -project="C:/Auracron/EnvDesigns.uproject"  -Device=Win64@TKT" -nocompile -nocompileuat ]
[2025.09.15-16.58.25:783][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/DefaultTextMaterialOpaque (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.09.15-16.58.25:783][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultTextMaterialOpaque is adding a dynamic import to package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness because of a recursive sync load
[2025.09.15-16.58.25:783][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: DeferredPostLoad) recursively from another package /Engine/EngineMaterials/DefaultTextMaterialOpaque (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.09.15-16.58.25:783][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/DefaultTextMaterialOpaque (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.09.15-16.58.25:784][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultTextMaterialOpaque is adding a dynamic import to package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec because of a recursive sync load
[2025.09.15-16.58.25:784][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: DeferredPostLoad) recursively from another package /Engine/EngineMaterials/DefaultTextMaterialOpaque (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.09.15-16.58.25:784][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/WorldGridMaterial (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/DefaultTextMaterialOpaque (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.09.15-16.58.25:785][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultTextMaterialOpaque is adding a dynamic import to package /Engine/EngineMaterials/T_Default_Material_Grid_M because of a recursive sync load
[2025.09.15-16.58.25:786][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultTextMaterialOpaque is adding a dynamic import to package /Engine/EngineMaterials/T_Default_Material_Grid_N because of a recursive sync load
[2025.09.15-16.58.25:789][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultTextMaterialOpaque is adding a dynamic import to package /Engine/Functions/Engine_MaterialFunctions01/Opacity/CameraDepthFade because of a recursive sync load
[2025.09.15-16.58.25:789][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.09.15-16.58.25:790][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.09.15-16.58.25:790][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultPostProcessMaterial (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.09.15-16.58.25:791][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultLightFunctionMaterial is adding a dynamic import to package /Engine/EngineMaterials/DefaultDiffuse because of a recursive sync load
[2025.09.15-16.58.25:791][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultLightFunctionMaterial is adding a dynamic import to package /Engine/Functions/Engine_MaterialFunctions02/Utility/BreakOutFloat2Components because of a recursive sync load
[2025.09.15-16.58.25:794][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultLightFunctionMaterial is adding a dynamic import to package /Engine/EngineMaterials/DefaultPostProcessMaterial because of a recursive sync load
[2025.09.15-16.58.25:794][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultPostProcessMaterial (state: DeferredPostLoad) recursively from another package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.09.15-16.58.25:839][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultDeferredDecalMaterial is adding a dynamic import to package /Engine/EngineMaterials/DefaultLightFunctionMaterial because of a recursive sync load
[2025.09.15-16.58.25:839][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: DeferredPostLoad) recursively from another package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.09.15-16.58.25:860][  0]LogStreaming: Display: Package /Engine/EngineMaterials/WorldGridMaterial is adding a dynamic import to package /Engine/EngineMaterials/DefaultDeferredDecalMaterial because of a recursive sync load
[2025.09.15-16.58.25:860][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: DeferredPostLoad) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.09.15-16.58.25:877][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultTextMaterialOpaque is adding a dynamic import to package /Engine/EngineMaterials/WorldGridMaterial because of a recursive sync load
[2025.09.15-16.58.25:877][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/WorldGridMaterial (state: DeferredPostLoad) recursively from another package /Engine/EngineMaterials/DefaultTextMaterialOpaque (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.09.15-16.58.26:014][  0]LogMeshReduction: Display: Using QuadricMeshReduction for automatic static mesh reduction
[2025.09.15-16.58.26:014][  0]LogMeshReduction: Display: Using SkeletalMeshReduction for automatic skeletal mesh reduction
[2025.09.15-16.58.26:014][  0]LogMeshReduction: Display: Using ProxyLODMeshReduction for automatic mesh merging
[2025.09.15-16.58.26:014][  0]LogMeshReduction: Display: No distributed automatic mesh merging module available
[2025.09.15-16.58.26:014][  0]LogMeshMerging: No distributed automatic mesh merging module available
[2025.09.15-16.58.26:534][  0]LogConfig: Applying CVar settings from Section [/Script/CQTest.CQTestSettings] File [Engine]
[2025.09.15-16.58.26:590][  0]LogVirtualization: Display: VirtualizationSystem name found in ini file: None
[2025.09.15-16.58.26:590][  0]LogVirtualization: Display: FNullVirtualizationSystem mounted, virtualization will be disabled
[2025.09.15-16.58.26:590][  0]LogIris: FNetObjectFactoryRegistry::UnregisterFactory is unregistering factory: NetActorFactory name: NetActorFactory id: 0
[2025.09.15-16.58.26:590][  0]LogIris: FNetObjectFactoryRegistry::UnregisterFactory is unregistering factory: NetSubObjectFactory name: NetSubObjectFactory id: 1
[2025.09.15-16.58.26:593][  0]LogLiveCoding: Display: Starting LiveCoding
[2025.09.15-16.58.26:593][  0]LogLiveCoding: Display: LiveCodingConsole Arguments: UnrealEditor Win64 Development
[2025.09.15-16.58.26:595][  0]LogLiveCoding: Display: First instance in process group "UE_EnvDesigns_0xaedfb9b9", spawning console
[2025.09.15-16.58.26:598][  0]LogLiveCoding: Display: Waiting for server
[2025.09.15-16.58.26:619][  0]LogSlate: Border
[2025.09.15-16.58.26:619][  0]LogSlate: BreadcrumbButton
[2025.09.15-16.58.26:619][  0]LogSlate: Brushes.Title
[2025.09.15-16.58.26:619][  0]LogSlate: ColorPicker.ColorThemes
[2025.09.15-16.58.26:619][  0]LogSlate: Default
[2025.09.15-16.58.26:619][  0]LogSlate: Icons.Save
[2025.09.15-16.58.26:619][  0]LogSlate: Icons.Toolbar.Settings
[2025.09.15-16.58.26:619][  0]LogSlate: ListView
[2025.09.15-16.58.26:619][  0]LogSlate: SoftwareCursor_CardinalCross
[2025.09.15-16.58.26:619][  0]LogSlate: SoftwareCursor_Grab
[2025.09.15-16.58.26:619][  0]LogSlate: TableView.DarkRow
[2025.09.15-16.58.26:619][  0]LogSlate: TableView.Row
[2025.09.15-16.58.26:619][  0]LogSlate: TreeView
[2025.09.15-16.58.26:698][  0]LogWorldPartition: Display: FWorldPartitionClassDescRegistry::Initialize started...
[2025.09.15-16.58.26:702][  0]LogWorldPartition: Display: FWorldPartitionClassDescRegistry::Initialize took 2.845 ms
[2025.09.15-16.58.26:723][  0]LogInit: XR: Instanced Stereo Rendering is Disabled
[2025.09.15-16.58.26:723][  0]LogInit: XR: MultiViewport is Disabled
[2025.09.15-16.58.26:723][  0]LogInit: XR: Mobile Multiview is Disabled
[2025.09.15-16.58.26:753][  0]LogStats: UGameplayTagsManager::InitializeManager -  0.000 s
[2025.09.15-16.58.26:921][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.10.dll
[2025.09.15-16.58.27:047][  0]LogMetaSound: Display: MetaSound Page Target Initialized to 'Default'
[2025.09.15-16.58.27:048][  0]LogAudio: Display: Registering Engine Module Parameter Interfaces...
[2025.09.15-16.58.27:086][  0]LogMetaSound: MetaSound Engine Initialized
[2025.09.15-16.58.27:237][  0]LogTurnkeySupport: Completed device detection: Code = 0
[2025.09.15-16.58.27:405][  0]LogLiveCoding: Display: Successfully initialized, removing startup thread
[2025.09.15-16.58.27:447][  0]LogNiagaraDebuggerClient: Niagara Debugger Client Initialized | Session: 7DB5D90851214FFC8000000000007F00 | Instance: F359F2F3402261B13CC142A8F0F5F22A (TKT-14324).
[2025.09.15-16.58.27:510][  0]LogUdpMessaging: Display: Work queue size set to 1024.
[2025.09.15-16.58.27:510][  0]LogUdpMessaging: Initializing bridge on interface 0.0.0.0:0 to multicast group *********:6666.
[2025.09.15-16.58.27:510][  0]LogUdpMessaging: Display: Unicast socket bound to '0.0.0.0:61600'.
[2025.09.15-16.58.27:515][  0]LogUdpMessaging: Display: Added local interface '192.168.43.191' to multicast group '*********:6666'
[2025.09.15-16.58.27:519][  0]LogUdpMessaging: Display: Using asynchronous task graph for message deserialization.
[2025.09.15-16.58.27:523][  0]LogTcpMessaging: Initializing TcpMessaging bridge
[2025.09.15-16.58.27:542][  0]LogNNERuntimeORT: Available graphics and compute adapters:
[2025.09.15-16.58.27:542][  0]LogNNERuntimeORT: Selecting NPU adapter: Intel(R) Iris(R) Xe Graphics
[2025.09.15-16.58.27:542][  0]LogNNERuntimeORT: MakeRuntimeORTDml:
[2025.09.15-16.58.27:542][  0]LogNNERuntimeORT:   DirectML:  yes
[2025.09.15-16.58.27:542][  0]LogNNERuntimeORT:   RHI D3D12: yes
[2025.09.15-16.58.27:542][  0]LogNNERuntimeORT:   D3D12:     yes
[2025.09.15-16.58.27:542][  0]LogNNERuntimeORT:   NPU:       yes
[2025.09.15-16.58.27:542][  0]LogNNERuntimeORT: Interface availability:
[2025.09.15-16.58.27:542][  0]LogNNERuntimeORT:   GPU: yes
[2025.09.15-16.58.27:542][  0]LogNNERuntimeORT:   RDG: yes
[2025.09.15-16.58.27:542][  0]LogNNERuntimeORT:   NPU: yes
[2025.09.15-16.58.27:542][  0]LogNNERuntimeORT: Available graphics and compute adapters:
[2025.09.15-16.58.27:542][  0]LogNNERuntimeORT: Selecting NPU adapter: Intel(R) Iris(R) Xe Graphics
[2025.09.15-16.58.27:600][  0]LogChaosVDEditor: [FChaosVDExtensionsManager::RegisterExtension] Registering CVD Extension [FChaosVDGenericDebugDrawExtension] ...
[2025.09.15-16.58.27:600][  0]LogChaosVDEditor: [FChaosVDExtensionsManager::RegisterExtension] Registering CVD Extension [FChaosVDAccelerationStructuresExtension] ...
[2025.09.15-16.58.27:607][  0]LogTimingProfiler: Initialize
[2025.09.15-16.58.27:608][  0]LogTimingProfiler: OnSessionChanged
[2025.09.15-16.58.27:608][  0]LoadingProfiler: Initialize
[2025.09.15-16.58.27:608][  0]LoadingProfiler: OnSessionChanged
[2025.09.15-16.58.27:608][  0]LogNetworkingProfiler: Initialize
[2025.09.15-16.58.27:608][  0]LogNetworkingProfiler: OnSessionChanged
[2025.09.15-16.58.27:608][  0]LogMemoryProfiler: Initialize
[2025.09.15-16.58.27:608][  0]LogMemoryProfiler: OnSessionChanged
[2025.09.15-16.58.27:655][  0]LogConfig: Applying CVar settings from Section [/Script/PCG.PCGEngineSettings] File [Engine]
[2025.09.15-16.58.27:741][  0]LogAudioCaptureCore: Display: No Audio Capture implementations found. Audio input will be silent.
[2025.09.15-16.58.27:741][  0]LogAudioCaptureCore: Display: No Audio Capture implementations found. Audio input will be silent.
[2025.09.15-16.58.27:920][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Content/Slate/Common/ButtonHoverHint.png' error.
[2025.09.15-16.58.27:920][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Content/Slate/Icons/doc_16x.png' error.
[2025.09.15-16.58.27:920][  0]LogSlate: Could not load file for Slate resource: [../../../Engine/Content/Slate/Icons/doc_16x.png] file: [../../../Engine/Content/Slate/Icons/doc_16x.png]
[2025.09.15-16.58.27:920][  0]LogSlate: Could not load file for Slate resource: [../../../Engine/Content/Slate/Common/ButtonHoverHint.png] file: [../../../Engine/Content/Slate/Common/ButtonHoverHint.png]
[2025.09.15-16.58.28:174][  0]LogConfig: Applying CVar settings from Section [/Script/NNEDenoiser.NNEDenoiserSettings] File [Engine]
[2025.09.15-16.58.28:215][  0]LogCollectionManager: Loaded 0 collections in 0.001736 seconds
[2025.09.15-16.58.28:218][  0]LogFileCache: Scanning file cache for directory 'C:/Auracron/Saved/Collections/' took 0.00s
[2025.09.15-16.58.28:221][  0]LogFileCache: Scanning file cache for directory 'C:/Auracron/Content/Developers/tktca/Collections/' took 0.00s
[2025.09.15-16.58.28:226][  0]LogFileCache: Scanning file cache for directory 'C:/Auracron/Content/Collections/' took 0.00s
[2025.09.15-16.58.28:277][  0]LogTurnkeySupport: Turnkey Device: Win64@tkt: (Name=tkt, Type=Computer, Status=Valid, MinAllowed=10.0.19041.0, MaxAllowed=, Current=10.0.26100.0, Flags="Device_InstallSoftwareValid")
[2025.09.15-16.58.28:285][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png' error.
[2025.09.15-16.58.28:285][  0]LogSlate: Could not load file for Slate resource: [../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png] file: [../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png]
[2025.09.15-16.58.28:285][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png' error.
[2025.09.15-16.58.28:285][  0]LogSlate: Could not load file for Slate resource: [../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png] file: [../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png]
[2025.09.15-16.58.28:306][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Content/Slate/Icons/doc_16x.png' error.
[2025.09.15-16.58.28:306][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Content/Slate/Common/ButtonHoverHint.png' error.
[2025.09.15-16.58.28:306][  0]LogSlate: Could not load file for Slate resource: [../../../Engine/Content/Slate/Common/ButtonHoverHint.png] file: [../../../Engine/Content/Slate/Common/ButtonHoverHint.png]
[2025.09.15-16.58.28:306][  0]LogSlate: Could not load file for Slate resource: [../../../Engine/Content/Slate/Icons/doc_16x.png] file: [../../../Engine/Content/Slate/Icons/doc_16x.png]
[2025.09.15-16.58.28:326][  0]LogEOSSDK: LogEOS: [Boot] EOSSDK Version 1.17.0-41373641 booting at 2025-09-15T16:58:28.326Z using C
[2025.09.15-16.58.28:327][  0]LogEOSSDK: LogEOS: [Boot] EOSSDK Platform Properties [OS=Windows/10.0.26100.3912.64bit, ClientId=xyza7891REBVsEqSJRRNXmlS7EQHM459, ProductId=86f32f1151354e7cb39c12f8ab2c22a3, SandboxId=********************************, DeploymentId=a652a72ea1664dcab3a467891eea5f30, ProductName=EnvDesigns, ProductVersion=++UE5+Release-5.6-***********, IsServer=false, Flags=DisableOverlay]
[2025.09.15-16.58.28:327][  0]LogEOSSDK: LogEOSAnalytics: Start Session (User: ...)
[2025.09.15-16.58.28:327][  0]LogEOSSDK: LogEOSOverlay: Overlay will not load, because it was explicitly disabled when creating the platform
[2025.09.15-16.58.28:334][  0]LogEOSSDK: LogEOSAntiCheat: [AntiCheatClient] Anti-cheat client not available. Verify that the game was started using the anti-cheat bootstrapper if you intend to use it.
[2025.09.15-16.58.28:334][  0]LogEOSSDK: LogEOS: SetApplicationStatus - OldStatus: EOS_AS_Foreground, NewStatus: EOS_AS_Foreground, Current Time: 0001.01.01-00.00.00
[2025.09.15-16.58.28:334][  0]LogEOSSDK: LogEOS: SetNetworkStatus - OldStatus: EOS_NS_Online, NewStatus: EOS_NS_Online
[2025.09.15-16.58.28:334][  0]LogEOSSDK: LogEOS: Updating Platform SDK Config, Time: 0.000096
[2025.09.15-16.58.28:391][  0]LogUObjectArray: 47630 objects as part of root set at end of initial load.
[2025.09.15-16.58.28:391][  0]LogUObjectArray: CloseDisregardForGC: 0/0 objects in disregard for GC pool
[2025.09.15-16.58.28:504][  0]LogAutomationTest: Error: Condition failed
[2025.09.15-16.58.28:504][  0]LogAutomationTest: Error: Condition failed
[2025.09.15-16.58.28:504][  0]LogAutomationTest: Error: Condition failed
[2025.09.15-16.58.28:504][  0]LogEngine: Initializing Engine...
[2025.09.15-16.58.28:601][  0]LogGameFeatures: Initializing game features subsystem
[2025.09.15-16.58.28:627][  0]LogStylusInput: Initializing StylusInput subsystem.
[2025.09.15-16.58.28:628][  0]LogTedsSettings: UTedsSettingsEditorSubsystem::Initialize
[2025.09.15-16.58.28:859][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.11.dll
[2025.09.15-16.58.28:878][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.12.dll
[2025.09.15-16.58.28:900][  0]LogTexture: Display: Aguardando texturas estar pronto29/42(/Engine/EngineMaterials/BaseFlattenGrayscaleMap_VT) ...
[2025.09.15-16.58.28:924][  0]LogTexture: Display: Aguardando texturas estar pronto31/42(/Engine/EngineMaterials/BaseFlattenNormalMap_VT) ...
[2025.09.15-16.58.28:949][  0]LogGameFeatures: Scanning for built-in game feature plugins
[2025.09.15-16.58.28:949][  0]LogGameFeatures: Loading 219 builtins. Not Amortized
[2025.09.15-16.58.28:950][  0]LogGameFeatures: Display: Total built in plugin load time 0.0009s
[2025.09.15-16.58.28:950][  0]LogStats: BuiltInGameFeaturePlugins loaded. -  0.001 s
[2025.09.15-16.58.28:950][  0]LogChaosDD: Creating Chaos Debug Draw Scene for world Untitled
[2025.09.15-16.58.28:960][  0]LogNetVersion: Set ProjectVersion to 1.0.0.0. Version Checksum will be recalculated on next use.
[2025.09.15-16.58.28:960][  0]LogInit: Texture streaming: Enabled
[2025.09.15-16.58.28:966][  0]LogAnalytics: Display: [UEEditor.Rocket.Release] AnalyticsET::StartSession ( APIServer = https://datarouter.ol.epicgames.com/datarouter/api/v1/public/data. AppVersion = 5.6.1-44394996+++UE5+Release-5.6 )
[2025.09.15-16.58.28:968][  0]LogAudio: Display: Initializing Audio Device Manager...
[2025.09.15-16.58.28:976][  0]LogAudio: Display: Loading Default Audio Settings Objects...
[2025.09.15-16.58.28:976][  0]LogAudio: Display: No default SoundConcurrencyObject specified (or failed to load).
[2025.09.15-16.58.28:977][  0]LogAudio: Display: Audio Device Manager Initialized
[2025.09.15-16.58.28:977][  0]LogAudio: Display: Creating Audio Device:                 Id: 1, Scope: Shared, Realtime: True
[2025.09.15-16.58.28:977][  0]LogAudioMixer: Display: Audio Mixer Platform Settings:
[2025.09.15-16.58.28:977][  0]LogAudioMixer: Display: 	Sample Rate:						  48000
[2025.09.15-16.58.28:977][  0]LogAudioMixer: Display: 	Callback Buffer Frame Size Requested: 1024
[2025.09.15-16.58.28:977][  0]LogAudioMixer: Display: 	Callback Buffer Frame Size To Use:	  1024
[2025.09.15-16.58.28:977][  0]LogAudioMixer: Display: 	Number of buffers to queue:			  1
[2025.09.15-16.58.28:977][  0]LogAudioMixer: Display: 	Max Channels (voices):				  32
[2025.09.15-16.58.28:977][  0]LogAudioMixer: Display: 	Number of Async Source Workers:		  4
[2025.09.15-16.58.28:977][  0]LogAudio: Display: AudioDevice MaxSources: 32
[2025.09.15-16.58.28:977][  0]LogAudio: Display: Audio Spatialization Plugin: None (built-in).
[2025.09.15-16.58.28:977][  0]LogAudio: Display: Audio Reverb Plugin: None (built-in).
[2025.09.15-16.58.28:977][  0]LogAudio: Display: Audio Occlusion Plugin: None (built-in).
[2025.09.15-16.58.28:983][  0]LogAudioMixer: Display: Initializing audio mixer using platform API: 'XAudio2'
[2025.09.15-16.58.29:172][  0]LogAudioMixer: Display: Using Audio Hardware Device Colunas (2- Realtek(R) Audio)
[2025.09.15-16.58.29:172][  0]LogAudioMixer: Display: Initializing Sound Submixes...
[2025.09.15-16.58.29:173][  0]LogAudioMixer: Display: Creating Master Submix 'MasterSubmixDefault'
[2025.09.15-16.58.29:173][  0]LogAudioMixer: Display: Creating Master Submix 'MasterReverbSubmixDefault'
[2025.09.15-16.58.29:174][  0]LogAudioMixer: FMixerPlatformXAudio2::StartAudioStream() called. InstanceID=1
[2025.09.15-16.58.29:174][  0]LogAudioMixer: Display: Output buffers initialized: Frames=1024, Channels=2, Samples=2048, InstanceID=1
[2025.09.15-16.58.29:176][  0]LogInit: FAudioDevice initialized with ID 1.
[2025.09.15-16.58.29:176][  0]LogAudioMixer: Display: Starting AudioMixerPlatformInterface::RunInternal(), InstanceID=1
[2025.09.15-16.58.29:176][  0]LogAudioMixer: Display: FMixerPlatformXAudio2::SubmitBuffer() called for the first time. InstanceID=1
[2025.09.15-16.58.29:177][  0]LogAudio: Display: Audio Device (ID: 1) registered with world 'Untitled'.
[2025.09.15-16.58.29:177][  0]LogAudioMixer: Initializing Audio Bus Subsystem for audio device with ID 1
[2025.09.15-16.58.29:182][  0]LogCsvProfiler: Display: Metadata set : largeworldcoordinates="1"
[2025.09.15-16.58.29:186][  0]LogInit: Undo buffer set to 256 MB
[2025.09.15-16.58.29:186][  0]LogInit: Transaction tracking system initialized
[2025.09.15-16.58.29:203][  0]LogSourceControl: Display: Uncontrolled Changelist persistency file loaded ../../../../../../Auracron/Saved/SourceControl/UncontrolledChangelists.json
[2025.09.15-16.58.29:240][  0]LocalizationService: O serviço de localização está desativado.
[2025.09.15-16.58.29:262][  0]Warning: FEnumProperty: In asset 'None', there is an enum property of type 'EIOSVersion' with an invalid value of 'IOS_11' - loaded
[2025.09.15-16.58.29:262][  0]LogObj: Error: LoadConfig (/Script/IOSRuntimeSettings.Default__IOSRuntimeSettings): import failed for MinimumiOSVersion in: IOS_11
[2025.09.15-16.58.29:379][  0]LogNNEDenoiser: Ray Tracing is not enabled, therefore NNEDenoiser is not registered!
[2025.09.15-16.58.29:386][  0]LogFileCache: Scanning file cache for directory 'C:/Auracron/Content/' took 0.03s
[2025.09.15-16.58.29:413][  0]LogPython: Python enabled via CVar 'Engine.Python.IsEnabledByDefault'
[2025.09.15-16.58.29:414][  0]LogPython: Using Python 3.11.8
[2025.09.15-16.58.29:436][  0]LogPython: Display: No pip-enabled plugins with python dependencies found, skipping
[2025.09.15-16.58.30:080][  0]LogLevelSequenceEditor: LevelSequenceEditor subsystem initialized.
[2025.09.15-16.58.30:106][  0]LogEditorDataStorage: Initializing
[2025.09.15-16.58.30:111][  0]LogEditorDataStorage: Initialized
[2025.09.15-16.58.30:138][  0]LogCore: Display: GameplayInsights module auto-connecting to local trace server...
[2025.09.15-16.58.30:183][  0]LogAssetRegistry: Display: Triggering cache save on discovery complete
[2025.09.15-16.58.30:227][  0]LogGameplayAbilityAudit: Selected GameplayAbilityAuditRow as the best Gameplay Ability Audit Functionality
[2025.09.15-16.58.30:227][  0]LogInit: Display: Engine is initialized. Leaving FEngineLoop::Init()
[2025.09.15-16.58.30:232][  0]SourceControl: Controle de revisão desabilitado
[2025.09.15-16.58.30:232][  0]LogUnrealEdMisc: Loading editor; pre map load, took 8.694
[2025.09.15-16.58.30:304][  0]LogFactory: FactoryCreateFile: PackFactory with PackFactory (0 0 C:/Program Files/Epic Games/UE_5.6/FeaturePacks/StarterContent.upack)
[2025.09.15-16.58.30:306][  0]LogPackFactory: Finished extracting 8 files (including 0 errors).
[2025.09.15-16.58.30:306][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Architecture/Floor_400x400.uasset" to "../../../../../../Auracron/Content/StarterContent/Architecture/Floor_400x400.uasset"
[2025.09.15-16.58.30:307][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Architecture/Pillar_50x500.uasset" to "../../../../../../Auracron/Content/StarterContent/Architecture/Pillar_50x500.uasset"
[2025.09.15-16.58.30:307][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Architecture/SM_AssetPlatform.uasset" to "../../../../../../Auracron/Content/StarterContent/Architecture/SM_AssetPlatform.uasset"
[2025.09.15-16.58.30:308][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Architecture/Wall_400x200.uasset" to "../../../../../../Auracron/Content/StarterContent/Architecture/Wall_400x200.uasset"
[2025.09.15-16.58.30:308][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Architecture/Wall_400x300.uasset" to "../../../../../../Auracron/Content/StarterContent/Architecture/Wall_400x300.uasset"
[2025.09.15-16.58.30:309][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Architecture/Wall_400x400.uasset" to "../../../../../../Auracron/Content/StarterContent/Architecture/Wall_400x400.uasset"
[2025.09.15-16.58.30:309][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Architecture/Wall_500x500.uasset" to "../../../../../../Auracron/Content/StarterContent/Architecture/Wall_500x500.uasset"
[2025.09.15-16.58.30:309][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Architecture/Wall_Door_400x300.uasset" to "../../../../../../Auracron/Content/StarterContent/Architecture/Wall_Door_400x300.uasset"
[2025.09.15-16.58.30:310][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Architecture/Wall_Door_400x400.uasset" to "../../../../../../Auracron/Content/StarterContent/Architecture/Wall_Door_400x400.uasset"
[2025.09.15-16.58.30:310][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Architecture/Wall_Window_400x300.uasset" to "../../../../../../Auracron/Content/StarterContent/Architecture/Wall_Window_400x300.uasset"
[2025.09.15-16.58.30:312][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Architecture/Wall_Window_400x400.uasset" to "../../../../../../Auracron/Content/StarterContent/Architecture/Wall_Window_400x400.uasset"
[2025.09.15-16.58.30:363][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/HDRI/HDRI_Epic_Courtyard_Daylight.uasset" to "../../../../../../Auracron/Content/StarterContent/HDRI/HDRI_Epic_Courtyard_Daylight.uasset"
[2025.09.15-16.58.30:364][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Maps/Advanced_Lighting.umap" to "../../../../../../Auracron/Content/StarterContent/Maps/Advanced_Lighting.umap"
[2025.09.15-16.58.30:365][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Maps/Advanced_Lighting_BuiltData.uasset" to "../../../../../../Auracron/Content/StarterContent/Maps/Advanced_Lighting_BuiltData.uasset"
[2025.09.15-16.58.30:366][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Maps/Minimal_Default.umap" to "../../../../../../Auracron/Content/StarterContent/Maps/Minimal_Default.umap"
[2025.09.15-16.58.30:368][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Maps/Minimal_Default_BuiltData.uasset" to "../../../../../../Auracron/Content/StarterContent/Maps/Minimal_Default_BuiltData.uasset"
[2025.09.15-16.58.30:369][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Maps/StarterMap.umap" to "../../../../../../Auracron/Content/StarterContent/Maps/StarterMap.umap"
[2025.09.15-16.58.30:386][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Maps/StarterMap_BuiltData.uasset" to "../../../../../../Auracron/Content/StarterContent/Maps/StarterMap_BuiltData.uasset"
[2025.09.15-16.58.30:386][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Audio/Collapse01.uasset" to "../../../../../../Auracron/Content/StarterContent/Audio/Collapse01.uasset"
[2025.09.15-16.58.30:388][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Audio/Collapse02.uasset" to "../../../../../../Auracron/Content/StarterContent/Audio/Collapse02.uasset"
[2025.09.15-16.58.30:389][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Audio/Collapse_Cue.uasset" to "../../../../../../Auracron/Content/StarterContent/Audio/Collapse_Cue.uasset"
[2025.09.15-16.58.30:390][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Audio/Explosion01.uasset" to "../../../../../../Auracron/Content/StarterContent/Audio/Explosion01.uasset"
[2025.09.15-16.58.30:391][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Audio/Explosion02.uasset" to "../../../../../../Auracron/Content/StarterContent/Audio/Explosion02.uasset"
[2025.09.15-16.58.30:391][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Audio/Explosion_Cue.uasset" to "../../../../../../Auracron/Content/StarterContent/Audio/Explosion_Cue.uasset"
[2025.09.15-16.58.30:392][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Audio/Fire01.uasset" to "../../../../../../Auracron/Content/StarterContent/Audio/Fire01.uasset"
[2025.09.15-16.58.30:395][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Audio/Fire01_Cue.uasset" to "../../../../../../Auracron/Content/StarterContent/Audio/Fire01_Cue.uasset"
[2025.09.15-16.58.30:396][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Audio/Fire_Sparks01.uasset" to "../../../../../../Auracron/Content/StarterContent/Audio/Fire_Sparks01.uasset"
[2025.09.15-16.58.30:397][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Audio/Fire_Sparks01_Cue.uasset" to "../../../../../../Auracron/Content/StarterContent/Audio/Fire_Sparks01_Cue.uasset"
[2025.09.15-16.58.30:397][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Audio/Light01.uasset" to "../../../../../../Auracron/Content/StarterContent/Audio/Light01.uasset"
[2025.09.15-16.58.30:399][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Audio/Light01_Cue.uasset" to "../../../../../../Auracron/Content/StarterContent/Audio/Light01_Cue.uasset"
[2025.09.15-16.58.30:399][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Audio/Light02.uasset" to "../../../../../../Auracron/Content/StarterContent/Audio/Light02.uasset"
[2025.09.15-16.58.30:400][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Audio/Light02_Cue.uasset" to "../../../../../../Auracron/Content/StarterContent/Audio/Light02_Cue.uasset"
[2025.09.15-16.58.30:401][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Audio/Smoke01.uasset" to "../../../../../../Auracron/Content/StarterContent/Audio/Smoke01.uasset"
[2025.09.15-16.58.30:401][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Audio/Smoke01_Cue.uasset" to "../../../../../../Auracron/Content/StarterContent/Audio/Smoke01_Cue.uasset"
[2025.09.15-16.58.30:402][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Audio/Starter_Background_Cue.uasset" to "../../../../../../Auracron/Content/StarterContent/Audio/Starter_Background_Cue.uasset"
[2025.09.15-16.58.30:406][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Audio/Starter_Birds01.uasset" to "../../../../../../Auracron/Content/StarterContent/Audio/Starter_Birds01.uasset"
[2025.09.15-16.58.30:413][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Audio/Starter_Music01.uasset" to "../../../../../../Auracron/Content/StarterContent/Audio/Starter_Music01.uasset"
[2025.09.15-16.58.30:414][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Audio/Starter_Music_Cue.uasset" to "../../../../../../Auracron/Content/StarterContent/Audio/Starter_Music_Cue.uasset"
[2025.09.15-16.58.30:418][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Audio/Starter_Wind05.uasset" to "../../../../../../Auracron/Content/StarterContent/Audio/Starter_Wind05.uasset"
[2025.09.15-16.58.30:421][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Audio/Starter_Wind06.uasset" to "../../../../../../Auracron/Content/StarterContent/Audio/Starter_Wind06.uasset"
[2025.09.15-16.58.30:422][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Audio/Steam01.uasset" to "../../../../../../Auracron/Content/StarterContent/Audio/Steam01.uasset"
[2025.09.15-16.58.30:423][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Audio/Steam01_Cue.uasset" to "../../../../../../Auracron/Content/StarterContent/Audio/Steam01_Cue.uasset"
[2025.09.15-16.58.30:424][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Blueprints/Blueprint_CeilingLight.uasset" to "../../../../../../Auracron/Content/StarterContent/Blueprints/Blueprint_CeilingLight.uasset"
[2025.09.15-16.58.30:424][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Blueprints/Blueprint_Effect_Explosion.uasset" to "../../../../../../Auracron/Content/StarterContent/Blueprints/Blueprint_Effect_Explosion.uasset"
[2025.09.15-16.58.30:425][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Blueprints/Blueprint_Effect_Fire.uasset" to "../../../../../../Auracron/Content/StarterContent/Blueprints/Blueprint_Effect_Fire.uasset"
[2025.09.15-16.58.30:425][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Blueprints/Blueprint_Effect_Smoke.uasset" to "../../../../../../Auracron/Content/StarterContent/Blueprints/Blueprint_Effect_Smoke.uasset"
[2025.09.15-16.58.30:425][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Blueprints/Blueprint_Effect_Sparks.uasset" to "../../../../../../Auracron/Content/StarterContent/Blueprints/Blueprint_Effect_Sparks.uasset"
[2025.09.15-16.58.30:426][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Blueprints/Blueprint_Effect_Steam.uasset" to "../../../../../../Auracron/Content/StarterContent/Blueprints/Blueprint_Effect_Steam.uasset"
[2025.09.15-16.58.30:426][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Blueprints/Blueprint_WallSconce.uasset" to "../../../../../../Auracron/Content/StarterContent/Blueprints/Blueprint_WallSconce.uasset"
[2025.09.15-16.58.30:427][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Blueprints/BP_LightStudio.uasset" to "../../../../../../Auracron/Content/StarterContent/Blueprints/BP_LightStudio.uasset"
[2025.09.15-16.58.30:429][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Materials/M_AssetPlatform.uasset" to "../../../../../../Auracron/Content/StarterContent/Materials/M_AssetPlatform.uasset"
[2025.09.15-16.58.30:431][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Materials/M_Basic_Floor.uasset" to "../../../../../../Auracron/Content/StarterContent/Materials/M_Basic_Floor.uasset"
[2025.09.15-16.58.30:432][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Materials/M_Basic_Wall.uasset" to "../../../../../../Auracron/Content/StarterContent/Materials/M_Basic_Wall.uasset"
[2025.09.15-16.58.30:432][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Materials/M_Brick_Clay_Beveled.uasset" to "../../../../../../Auracron/Content/StarterContent/Materials/M_Brick_Clay_Beveled.uasset"
[2025.09.15-16.58.30:433][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Materials/M_Brick_Clay_New.uasset" to "../../../../../../Auracron/Content/StarterContent/Materials/M_Brick_Clay_New.uasset"
[2025.09.15-16.58.30:434][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Materials/M_Brick_Clay_Old.uasset" to "../../../../../../Auracron/Content/StarterContent/Materials/M_Brick_Clay_Old.uasset"
[2025.09.15-16.58.30:434][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Materials/M_Brick_Cut_Stone.uasset" to "../../../../../../Auracron/Content/StarterContent/Materials/M_Brick_Cut_Stone.uasset"
[2025.09.15-16.58.30:435][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Materials/M_Brick_Hewn_Stone.uasset" to "../../../../../../Auracron/Content/StarterContent/Materials/M_Brick_Hewn_Stone.uasset"
[2025.09.15-16.58.30:436][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Materials/M_Ceramic_Tile_Checker.uasset" to "../../../../../../Auracron/Content/StarterContent/Materials/M_Ceramic_Tile_Checker.uasset"
[2025.09.15-16.58.30:436][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Materials/M_CobbleStone_Pebble.uasset" to "../../../../../../Auracron/Content/StarterContent/Materials/M_CobbleStone_Pebble.uasset"
[2025.09.15-16.58.30:437][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Materials/M_CobbleStone_Rough.uasset" to "../../../../../../Auracron/Content/StarterContent/Materials/M_CobbleStone_Rough.uasset"
[2025.09.15-16.58.30:437][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Materials/M_CobbleStone_Smooth.uasset" to "../../../../../../Auracron/Content/StarterContent/Materials/M_CobbleStone_Smooth.uasset"
[2025.09.15-16.58.30:438][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Materials/M_ColorGrid_LowSpec.uasset" to "../../../../../../Auracron/Content/StarterContent/Materials/M_ColorGrid_LowSpec.uasset"
[2025.09.15-16.58.30:438][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Particles/P_Ambient_Dust.uasset" to "../../../../../../Auracron/Content/StarterContent/Particles/P_Ambient_Dust.uasset"
[2025.09.15-16.58.30:438][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Materials/M_Concrete_Grime.uasset" to "../../../../../../Auracron/Content/StarterContent/Materials/M_Concrete_Grime.uasset"
[2025.09.15-16.58.30:439][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Particles/P_Explosion.uasset" to "../../../../../../Auracron/Content/StarterContent/Particles/P_Explosion.uasset"
[2025.09.15-16.58.30:440][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Materials/M_Concrete_Panels.uasset" to "../../../../../../Auracron/Content/StarterContent/Materials/M_Concrete_Panels.uasset"
[2025.09.15-16.58.30:440][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Particles/P_Fire.uasset" to "../../../../../../Auracron/Content/StarterContent/Particles/P_Fire.uasset"
[2025.09.15-16.58.30:443][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Particles/P_Smoke.uasset" to "../../../../../../Auracron/Content/StarterContent/Particles/P_Smoke.uasset"
[2025.09.15-16.58.30:443][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Materials/M_Concrete_Poured.uasset" to "../../../../../../Auracron/Content/StarterContent/Materials/M_Concrete_Poured.uasset"
[2025.09.15-16.58.30:443][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Materials/M_Concrete_Tiles.uasset" to "../../../../../../Auracron/Content/StarterContent/Materials/M_Concrete_Tiles.uasset"
[2025.09.15-16.58.30:443][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Particles/P_Sparks.uasset" to "../../../../../../Auracron/Content/StarterContent/Particles/P_Sparks.uasset"
[2025.09.15-16.58.30:448][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Materials/M_Glass.uasset" to "../../../../../../Auracron/Content/StarterContent/Materials/M_Glass.uasset"
[2025.09.15-16.58.30:449][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Particles/P_Steam_Lit.uasset" to "../../../../../../Auracron/Content/StarterContent/Particles/P_Steam_Lit.uasset"
[2025.09.15-16.58.30:450][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Materials/M_Ground_Grass.uasset" to "../../../../../../Auracron/Content/StarterContent/Materials/M_Ground_Grass.uasset"
[2025.09.15-16.58.30:451][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Materials/M_Ground_Gravel.uasset" to "../../../../../../Auracron/Content/StarterContent/Materials/M_Ground_Gravel.uasset"
[2025.09.15-16.58.30:451][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Materials/M_Ground_Moss.uasset" to "../../../../../../Auracron/Content/StarterContent/Materials/M_Ground_Moss.uasset"
[2025.09.15-16.58.30:452][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Materials/M_Metal_Brushed_Nickel.uasset" to "../../../../../../Auracron/Content/StarterContent/Materials/M_Metal_Brushed_Nickel.uasset"
[2025.09.15-16.58.30:452][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Materials/M_Metal_Burnished_Steel.uasset" to "../../../../../../Auracron/Content/StarterContent/Materials/M_Metal_Burnished_Steel.uasset"
[2025.09.15-16.58.30:453][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Materials/M_Metal_Chrome.uasset" to "../../../../../../Auracron/Content/StarterContent/Materials/M_Metal_Chrome.uasset"
[2025.09.15-16.58.30:454][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Materials/M_Metal_Copper.uasset" to "../../../../../../Auracron/Content/StarterContent/Materials/M_Metal_Copper.uasset"
[2025.09.15-16.58.30:455][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Materials/M_Metal_Gold.uasset" to "../../../../../../Auracron/Content/StarterContent/Materials/M_Metal_Gold.uasset"
[2025.09.15-16.58.30:456][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Materials/M_Metal_Rust.uasset" to "../../../../../../Auracron/Content/StarterContent/Materials/M_Metal_Rust.uasset"
[2025.09.15-16.58.30:456][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Materials/M_Metal_Steel.uasset" to "../../../../../../Auracron/Content/StarterContent/Materials/M_Metal_Steel.uasset"
[2025.09.15-16.58.30:457][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Materials/M_Rock_Basalt.uasset" to "../../../../../../Auracron/Content/StarterContent/Materials/M_Rock_Basalt.uasset"
[2025.09.15-16.58.30:457][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Materials/M_Rock_Marble_Polished.uasset" to "../../../../../../Auracron/Content/StarterContent/Materials/M_Rock_Marble_Polished.uasset"
[2025.09.15-16.58.30:458][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Materials/M_Rock_Sandstone.uasset" to "../../../../../../Auracron/Content/StarterContent/Materials/M_Rock_Sandstone.uasset"
[2025.09.15-16.58.30:459][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Materials/M_Rock_Slate.uasset" to "../../../../../../Auracron/Content/StarterContent/Materials/M_Rock_Slate.uasset"
[2025.09.15-16.58.30:459][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Materials/M_Tech_Checker_Dot.uasset" to "../../../../../../Auracron/Content/StarterContent/Materials/M_Tech_Checker_Dot.uasset"
[2025.09.15-16.58.30:459][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Materials/M_Tech_Hex_Tile.uasset" to "../../../../../../Auracron/Content/StarterContent/Materials/M_Tech_Hex_Tile.uasset"
[2025.09.15-16.58.30:463][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Materials/M_Tech_Hex_Tile_Pulse.uasset" to "../../../../../../Auracron/Content/StarterContent/Materials/M_Tech_Hex_Tile_Pulse.uasset"
[2025.09.15-16.58.30:463][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Materials/M_Tech_Panel.uasset" to "../../../../../../Auracron/Content/StarterContent/Materials/M_Tech_Panel.uasset"
[2025.09.15-16.58.30:463][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Materials/M_Water_Lake.uasset" to "../../../../../../Auracron/Content/StarterContent/Materials/M_Water_Lake.uasset"
[2025.09.15-16.58.30:464][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Materials/M_Water_Ocean.uasset" to "../../../../../../Auracron/Content/StarterContent/Materials/M_Water_Ocean.uasset"
[2025.09.15-16.58.30:464][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Materials/M_Wood_Floor_Walnut_Polished.uasset" to "../../../../../../Auracron/Content/StarterContent/Materials/M_Wood_Floor_Walnut_Polished.uasset"
[2025.09.15-16.58.30:465][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Materials/M_Wood_Floor_Walnut_Worn.uasset" to "../../../../../../Auracron/Content/StarterContent/Materials/M_Wood_Floor_Walnut_Worn.uasset"
[2025.09.15-16.58.30:466][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Materials/M_Wood_Oak.uasset" to "../../../../../../Auracron/Content/StarterContent/Materials/M_Wood_Oak.uasset"
[2025.09.15-16.58.30:467][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Materials/M_Wood_Pine.uasset" to "../../../../../../Auracron/Content/StarterContent/Materials/M_Wood_Pine.uasset"
[2025.09.15-16.58.30:472][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Materials/M_Wood_Walnut.uasset" to "../../../../../../Auracron/Content/StarterContent/Materials/M_Wood_Walnut.uasset"
[2025.09.15-16.58.30:473][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Props/MaterialSphere.uasset" to "../../../../../../Auracron/Content/StarterContent/Props/MaterialSphere.uasset"
[2025.09.15-16.58.30:473][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Props/SM_Bush.uasset" to "../../../../../../Auracron/Content/StarterContent/Props/SM_Bush.uasset"
[2025.09.15-16.58.30:475][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Props/SM_Chair.uasset" to "../../../../../../Auracron/Content/StarterContent/Props/SM_Chair.uasset"
[2025.09.15-16.58.30:475][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Props/SM_CornerFrame.uasset" to "../../../../../../Auracron/Content/StarterContent/Props/SM_CornerFrame.uasset"
[2025.09.15-16.58.30:475][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Props/SM_Couch.uasset" to "../../../../../../Auracron/Content/StarterContent/Props/SM_Couch.uasset"
[2025.09.15-16.58.30:475][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Props/SM_Door.uasset" to "../../../../../../Auracron/Content/StarterContent/Props/SM_Door.uasset"
[2025.09.15-16.58.30:476][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Props/SM_DoorFrame.uasset" to "../../../../../../Auracron/Content/StarterContent/Props/SM_DoorFrame.uasset"
[2025.09.15-16.58.30:477][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Props/SM_GlassWindow.uasset" to "../../../../../../Auracron/Content/StarterContent/Props/SM_GlassWindow.uasset"
[2025.09.15-16.58.30:477][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Props/SM_Lamp_Ceiling.uasset" to "../../../../../../Auracron/Content/StarterContent/Props/SM_Lamp_Ceiling.uasset"
[2025.09.15-16.58.30:479][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Props/SM_Lamp_Wall.uasset" to "../../../../../../Auracron/Content/StarterContent/Props/SM_Lamp_Wall.uasset"
[2025.09.15-16.58.30:479][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Props/SM_PillarFrame.uasset" to "../../../../../../Auracron/Content/StarterContent/Props/SM_PillarFrame.uasset"
[2025.09.15-16.58.30:479][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Props/SM_PillarFrame300.uasset" to "../../../../../../Auracron/Content/StarterContent/Props/SM_PillarFrame300.uasset"
[2025.09.15-16.58.30:479][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Props/SM_Rock.uasset" to "../../../../../../Auracron/Content/StarterContent/Props/SM_Rock.uasset"
[2025.09.15-16.58.30:480][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Props/SM_Shelf.uasset" to "../../../../../../Auracron/Content/StarterContent/Props/SM_Shelf.uasset"
[2025.09.15-16.58.30:480][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Props/SM_Stairs.uasset" to "../../../../../../Auracron/Content/StarterContent/Props/SM_Stairs.uasset"
[2025.09.15-16.58.30:481][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Props/SM_Statue.uasset" to "../../../../../../Auracron/Content/StarterContent/Props/SM_Statue.uasset"
[2025.09.15-16.58.30:482][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Props/SM_TableRound.uasset" to "../../../../../../Auracron/Content/StarterContent/Props/SM_TableRound.uasset"
[2025.09.15-16.58.30:482][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Props/SM_WindowFrame.uasset" to "../../../../../../Auracron/Content/StarterContent/Props/SM_WindowFrame.uasset"
[2025.09.15-16.58.30:483][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Shapes/Shape_Cone.uasset" to "../../../../../../Auracron/Content/StarterContent/Shapes/Shape_Cone.uasset"
[2025.09.15-16.58.30:484][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Shapes/Shape_Cube.uasset" to "../../../../../../Auracron/Content/StarterContent/Shapes/Shape_Cube.uasset"
[2025.09.15-16.58.30:484][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Shapes/Shape_Cylinder.uasset" to "../../../../../../Auracron/Content/StarterContent/Shapes/Shape_Cylinder.uasset"
[2025.09.15-16.58.30:484][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Shapes/Shape_NarrowCapsule.uasset" to "../../../../../../Auracron/Content/StarterContent/Shapes/Shape_NarrowCapsule.uasset"
[2025.09.15-16.58.30:484][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Shapes/Shape_Pipe.uasset" to "../../../../../../Auracron/Content/StarterContent/Shapes/Shape_Pipe.uasset"
[2025.09.15-16.58.30:484][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Shapes/Shape_Pipe_180.uasset" to "../../../../../../Auracron/Content/StarterContent/Shapes/Shape_Pipe_180.uasset"
[2025.09.15-16.58.30:486][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Shapes/Shape_Pipe_90.uasset" to "../../../../../../Auracron/Content/StarterContent/Shapes/Shape_Pipe_90.uasset"
[2025.09.15-16.58.30:486][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Shapes/Shape_Plane.uasset" to "../../../../../../Auracron/Content/StarterContent/Shapes/Shape_Plane.uasset"
[2025.09.15-16.58.30:486][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Shapes/Shape_QuadPyramid.uasset" to "../../../../../../Auracron/Content/StarterContent/Shapes/Shape_QuadPyramid.uasset"
[2025.09.15-16.58.30:486][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Shapes/Shape_Sphere.uasset" to "../../../../../../Auracron/Content/StarterContent/Shapes/Shape_Sphere.uasset"
[2025.09.15-16.58.30:487][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Shapes/Shape_Torus.uasset" to "../../../../../../Auracron/Content/StarterContent/Shapes/Shape_Torus.uasset"
[2025.09.15-16.58.30:489][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Shapes/Shape_Trim.uasset" to "../../../../../../Auracron/Content/StarterContent/Shapes/Shape_Trim.uasset"
[2025.09.15-16.58.30:490][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Shapes/Shape_Trim_90_In.uasset" to "../../../../../../Auracron/Content/StarterContent/Shapes/Shape_Trim_90_In.uasset"
[2025.09.15-16.58.30:490][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Shapes/Shape_Trim_90_Out.uasset" to "../../../../../../Auracron/Content/StarterContent/Shapes/Shape_Trim_90_Out.uasset"
[2025.09.15-16.58.30:491][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Shapes/Shape_TriPyramid.uasset" to "../../../../../../Auracron/Content/StarterContent/Shapes/Shape_TriPyramid.uasset"
[2025.09.15-16.58.30:491][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Shapes/Shape_Tube.uasset" to "../../../../../../Auracron/Content/StarterContent/Shapes/Shape_Tube.uasset"
[2025.09.15-16.58.30:491][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Shapes/Shape_Wedge_A.uasset" to "../../../../../../Auracron/Content/StarterContent/Shapes/Shape_Wedge_A.uasset"
[2025.09.15-16.58.30:493][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Shapes/Shape_Wedge_B.uasset" to "../../../../../../Auracron/Content/StarterContent/Shapes/Shape_Wedge_B.uasset"
[2025.09.15-16.58.30:493][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Shapes/Shape_WideCapsule.uasset" to "../../../../../../Auracron/Content/StarterContent/Shapes/Shape_WideCapsule.uasset"
[2025.09.15-16.58.30:500][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Brick_Clay_Beveled_D.uasset" to "../../../../../../Auracron/Content/StarterContent/Textures/T_Brick_Clay_Beveled_D.uasset"
[2025.09.15-16.58.30:510][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Brick_Clay_Beveled_M.uasset" to "../../../../../../Auracron/Content/StarterContent/Textures/T_Brick_Clay_Beveled_M.uasset"
[2025.09.15-16.58.30:518][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Brick_Clay_Beveled_N.uasset" to "../../../../../../Auracron/Content/StarterContent/Textures/T_Brick_Clay_Beveled_N.uasset"
[2025.09.15-16.58.30:524][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Brick_Clay_New_D.uasset" to "../../../../../../Auracron/Content/StarterContent/Textures/T_Brick_Clay_New_D.uasset"
[2025.09.15-16.58.30:533][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Brick_Clay_New_M.uasset" to "../../../../../../Auracron/Content/StarterContent/Textures/T_Brick_Clay_New_M.uasset"
[2025.09.15-16.58.30:539][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Brick_Clay_New_N.uasset" to "../../../../../../Auracron/Content/StarterContent/Textures/T_Brick_Clay_New_N.uasset"
[2025.09.15-16.58.30:547][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Brick_Clay_Old_D.uasset" to "../../../../../../Auracron/Content/StarterContent/Textures/T_Brick_Clay_Old_D.uasset"
[2025.09.15-16.58.30:552][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Brick_Clay_Old_N.uasset" to "../../../../../../Auracron/Content/StarterContent/Textures/T_Brick_Clay_Old_N.uasset"
[2025.09.15-16.58.30:560][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Brick_Cut_Stone_D.uasset" to "../../../../../../Auracron/Content/StarterContent/Textures/T_Brick_Cut_Stone_D.uasset"
[2025.09.15-16.58.30:564][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Brick_Cut_Stone_N.uasset" to "../../../../../../Auracron/Content/StarterContent/Textures/T_Brick_Cut_Stone_N.uasset"
[2025.09.15-16.58.30:570][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Brick_Hewn_Stone_D.uasset" to "../../../../../../Auracron/Content/StarterContent/Textures/T_Brick_Hewn_Stone_D.uasset"
[2025.09.15-16.58.30:574][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Brick_Hewn_Stone_M.uasset" to "../../../../../../Auracron/Content/StarterContent/Textures/T_Brick_Hewn_Stone_M.uasset"
[2025.09.15-16.58.30:580][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Brick_Hewn_Stone_N.uasset" to "../../../../../../Auracron/Content/StarterContent/Textures/T_Brick_Hewn_Stone_N.uasset"
[2025.09.15-16.58.30:581][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Burst_M.uasset" to "../../../../../../Auracron/Content/StarterContent/Textures/T_Burst_M.uasset"
[2025.09.15-16.58.30:581][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Bush_D.uasset" to "../../../../../../Auracron/Content/StarterContent/Textures/T_Bush_D.uasset"
[2025.09.15-16.58.30:582][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Bush_N.uasset" to "../../../../../../Auracron/Content/StarterContent/Textures/T_Bush_N.uasset"
[2025.09.15-16.58.30:583][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Ceramic_Tile_M.uasset" to "../../../../../../Auracron/Content/StarterContent/Textures/T_Ceramic_Tile_M.uasset"
[2025.09.15-16.58.30:584][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Ceramic_Tile_N.uasset" to "../../../../../../Auracron/Content/StarterContent/Textures/T_Ceramic_Tile_N.uasset"
[2025.09.15-16.58.30:585][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Chair_M.uasset" to "../../../../../../Auracron/Content/StarterContent/Textures/T_Chair_M.uasset"
[2025.09.15-16.58.30:587][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Chair_N.uasset" to "../../../../../../Auracron/Content/StarterContent/Textures/T_Chair_N.uasset"
[2025.09.15-16.58.30:588][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Checker_Noise_M.uasset" to "../../../../../../Auracron/Content/StarterContent/Textures/T_Checker_Noise_M.uasset"
[2025.09.15-16.58.30:598][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_CobbleStone_Pebble_D.uasset" to "../../../../../../Auracron/Content/StarterContent/Textures/T_CobbleStone_Pebble_D.uasset"
[2025.09.15-16.58.30:599][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_CobbleStone_Pebble_M.uasset" to "../../../../../../Auracron/Content/StarterContent/Textures/T_CobbleStone_Pebble_M.uasset"
[2025.09.15-16.58.30:608][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_CobbleStone_Pebble_N.uasset" to "../../../../../../Auracron/Content/StarterContent/Textures/T_CobbleStone_Pebble_N.uasset"
[2025.09.15-16.58.30:610][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_CobbleStone_Rough_D.uasset" to "../../../../../../Auracron/Content/StarterContent/Textures/T_CobbleStone_Rough_D.uasset"
[2025.09.15-16.58.30:613][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_CobbleStone_Rough_N.uasset" to "../../../../../../Auracron/Content/StarterContent/Textures/T_CobbleStone_Rough_N.uasset"
[2025.09.15-16.58.30:618][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_CobbleStone_Smooth_D.uasset" to "../../../../../../Auracron/Content/StarterContent/Textures/T_CobbleStone_Smooth_D.uasset"
[2025.09.15-16.58.30:624][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_CobbleStone_Smooth_M.uasset" to "../../../../../../Auracron/Content/StarterContent/Textures/T_CobbleStone_Smooth_M.uasset"
[2025.09.15-16.58.30:630][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_CobbleStone_Smooth_N.uasset" to "../../../../../../Auracron/Content/StarterContent/Textures/T_CobbleStone_Smooth_N.uasset"
[2025.09.15-16.58.30:636][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Concrete_Grime_D.uasset" to "../../../../../../Auracron/Content/StarterContent/Textures/T_Concrete_Grime_D.uasset"
[2025.09.15-16.58.30:641][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Concrete_Panels_D.uasset" to "../../../../../../Auracron/Content/StarterContent/Textures/T_Concrete_Panels_D.uasset"
[2025.09.15-16.58.30:645][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Concrete_Panels_N.uasset" to "../../../../../../Auracron/Content/StarterContent/Textures/T_Concrete_Panels_N.uasset"
[2025.09.15-16.58.30:652][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Concrete_Poured_D.uasset" to "../../../../../../Auracron/Content/StarterContent/Textures/T_Concrete_Poured_D.uasset"
[2025.09.15-16.58.30:658][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Concrete_Poured_N.uasset" to "../../../../../../Auracron/Content/StarterContent/Textures/T_Concrete_Poured_N.uasset"
[2025.09.15-16.58.30:662][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Concrete_Tiles_D.uasset" to "../../../../../../Auracron/Content/StarterContent/Textures/T_Concrete_Tiles_D.uasset"
[2025.09.15-16.58.30:666][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Concrete_Tiles_M.uasset" to "../../../../../../Auracron/Content/StarterContent/Textures/T_Concrete_Tiles_M.uasset"
[2025.09.15-16.58.30:670][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Concrete_Tiles_N.uasset" to "../../../../../../Auracron/Content/StarterContent/Textures/T_Concrete_Tiles_N.uasset"
[2025.09.15-16.58.30:671][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Concrete_Tiles_Variation_M.uasset" to "../../../../../../Auracron/Content/StarterContent/Textures/T_Concrete_Tiles_Variation_M.uasset"
[2025.09.15-16.58.30:674][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Detail_Rocky_N.uasset" to "../../../../../../Auracron/Content/StarterContent/Textures/T_Detail_Rocky_N.uasset"
[2025.09.15-16.58.30:674][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Door_M.uasset" to "../../../../../../Auracron/Content/StarterContent/Textures/T_Door_M.uasset"
[2025.09.15-16.58.30:675][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Door_N.uasset" to "../../../../../../Auracron/Content/StarterContent/Textures/T_Door_N.uasset"
[2025.09.15-16.58.30:676][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Dust_Particle_D.uasset" to "../../../../../../Auracron/Content/StarterContent/Textures/T_Dust_Particle_D.uasset"
[2025.09.15-16.58.30:679][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Explosion_SubUV.uasset" to "../../../../../../Auracron/Content/StarterContent/Textures/T_Explosion_SubUV.uasset"
[2025.09.15-16.58.30:680][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Fire_SubUV.uasset" to "../../../../../../Auracron/Content/StarterContent/Textures/T_Fire_SubUV.uasset"
[2025.09.15-16.58.30:681][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Fire_Tiled_D.uasset" to "../../../../../../Auracron/Content/StarterContent/Textures/T_Fire_Tiled_D.uasset"
[2025.09.15-16.58.30:682][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Frame_M.uasset" to "../../../../../../Auracron/Content/StarterContent/Textures/T_Frame_M.uasset"
[2025.09.15-16.58.30:684][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Frame_N.uasset" to "../../../../../../Auracron/Content/StarterContent/Textures/T_Frame_N.uasset"
[2025.09.15-16.58.30:685][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Gradinet_01.uasset" to "../../../../../../Auracron/Content/StarterContent/Textures/T_Gradinet_01.uasset"
[2025.09.15-16.58.30:696][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Ground_Grass_D.uasset" to "../../../../../../Auracron/Content/StarterContent/Textures/T_Ground_Grass_D.uasset"
[2025.09.15-16.58.30:707][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Ground_Grass_N.uasset" to "../../../../../../Auracron/Content/StarterContent/Textures/T_Ground_Grass_N.uasset"
[2025.09.15-16.58.30:713][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Ground_Gravel_D.uasset" to "../../../../../../Auracron/Content/StarterContent/Textures/T_Ground_Gravel_D.uasset"
[2025.09.15-16.58.30:720][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Ground_Gravel_N.uasset" to "../../../../../../Auracron/Content/StarterContent/Textures/T_Ground_Gravel_N.uasset"
[2025.09.15-16.58.30:733][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_ground_Moss_D.uasset" to "../../../../../../Auracron/Content/StarterContent/Textures/T_ground_Moss_D.uasset"
[2025.09.15-16.58.30:742][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Ground_Moss_N.uasset" to "../../../../../../Auracron/Content/StarterContent/Textures/T_Ground_Moss_N.uasset"
[2025.09.15-16.58.30:744][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Lamp_M.uasset" to "../../../../../../Auracron/Content/StarterContent/Textures/T_Lamp_M.uasset"
[2025.09.15-16.58.30:748][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Lamp_N.uasset" to "../../../../../../Auracron/Content/StarterContent/Textures/T_Lamp_N.uasset"
[2025.09.15-16.58.30:758][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_MacroVariation.uasset" to "../../../../../../Auracron/Content/StarterContent/Textures/T_MacroVariation.uasset"
[2025.09.15-16.58.30:767][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Metal_Aluminum_D.uasset" to "../../../../../../Auracron/Content/StarterContent/Textures/T_Metal_Aluminum_D.uasset"
[2025.09.15-16.58.30:777][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Metal_Copper_D.uasset" to "../../../../../../Auracron/Content/StarterContent/Textures/T_Metal_Copper_D.uasset"
[2025.09.15-16.58.30:788][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Metal_Gold_D.uasset" to "../../../../../../Auracron/Content/StarterContent/Textures/T_Metal_Gold_D.uasset"
[2025.09.15-16.58.30:792][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Metal_Gold_N.uasset" to "../../../../../../Auracron/Content/StarterContent/Textures/T_Metal_Gold_N.uasset"
[2025.09.15-16.58.30:802][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Metal_Rust_D.uasset" to "../../../../../../Auracron/Content/StarterContent/Textures/T_Metal_Rust_D.uasset"
[2025.09.15-16.58.30:811][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Metal_Rust_N.uasset" to "../../../../../../Auracron/Content/StarterContent/Textures/T_Metal_Rust_N.uasset"
[2025.09.15-16.58.30:816][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Metal_Steel_D.uasset" to "../../../../../../Auracron/Content/StarterContent/Textures/T_Metal_Steel_D.uasset"
[2025.09.15-16.58.30:821][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Metal_Steel_N.uasset" to "../../../../../../Auracron/Content/StarterContent/Textures/T_Metal_Steel_N.uasset"
[2025.09.15-16.58.30:830][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Perlin_Noise_M.uasset" to "../../../../../../Auracron/Content/StarterContent/Textures/T_Perlin_Noise_M.uasset"
[2025.09.15-16.58.30:834][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_RockMesh_M.uasset" to "../../../../../../Auracron/Content/StarterContent/Textures/T_RockMesh_M.uasset"
[2025.09.15-16.58.30:841][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_RockMesh_N.uasset" to "../../../../../../Auracron/Content/StarterContent/Textures/T_RockMesh_N.uasset"
[2025.09.15-16.58.30:850][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Rock_Basalt_D.uasset" to "../../../../../../Auracron/Content/StarterContent/Textures/T_Rock_Basalt_D.uasset"
[2025.09.15-16.58.30:858][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Rock_Basalt_N.uasset" to "../../../../../../Auracron/Content/StarterContent/Textures/T_Rock_Basalt_N.uasset"
[2025.09.15-16.58.30:863][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Rock_Marble_Polished_D.uasset" to "../../../../../../Auracron/Content/StarterContent/Textures/T_Rock_Marble_Polished_D.uasset"
[2025.09.15-16.58.30:873][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Rock_Sandstone_D.uasset" to "../../../../../../Auracron/Content/StarterContent/Textures/T_Rock_Sandstone_D.uasset"
[2025.09.15-16.58.30:881][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Rock_Sandstone_N.uasset" to "../../../../../../Auracron/Content/StarterContent/Textures/T_Rock_Sandstone_N.uasset"
[2025.09.15-16.58.30:892][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Rock_Slate_D.uasset" to "../../../../../../Auracron/Content/StarterContent/Textures/T_Rock_Slate_D.uasset"
[2025.09.15-16.58.30:900][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Rock_Slate_N.uasset" to "../../../../../../Auracron/Content/StarterContent/Textures/T_Rock_Slate_N.uasset"
[2025.09.15-16.58.30:909][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Rock_Smooth_Granite_D.uasset" to "../../../../../../Auracron/Content/StarterContent/Textures/T_Rock_Smooth_Granite_D.uasset"
[2025.09.15-16.58.30:911][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Shelf_M.uasset" to "../../../../../../Auracron/Content/StarterContent/Textures/T_Shelf_M.uasset"
[2025.09.15-16.58.30:913][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Shelf_N.uasset" to "../../../../../../Auracron/Content/StarterContent/Textures/T_Shelf_N.uasset"
[2025.09.15-16.58.30:914][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Single_Tile_N.uasset" to "../../../../../../Auracron/Content/StarterContent/Textures/T_Single_Tile_N.uasset"
[2025.09.15-16.58.30:918][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Smoke_SubUV.uasset" to "../../../../../../Auracron/Content/StarterContent/Textures/T_Smoke_SubUV.uasset"
[2025.09.15-16.58.30:919][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Smoke_Tiled_D.uasset" to "../../../../../../Auracron/Content/StarterContent/Textures/T_Smoke_Tiled_D.uasset"
[2025.09.15-16.58.30:920][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Spark_Core.uasset" to "../../../../../../Auracron/Content/StarterContent/Textures/T_Spark_Core.uasset"
[2025.09.15-16.58.30:921][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Statue_M.uasset" to "../../../../../../Auracron/Content/StarterContent/Textures/T_Statue_M.uasset"
[2025.09.15-16.58.30:923][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Statue_N.uasset" to "../../../../../../Auracron/Content/StarterContent/Textures/T_Statue_N.uasset"
[2025.09.15-16.58.30:925][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_TableRound_M.uasset" to "../../../../../../Auracron/Content/StarterContent/Textures/T_TableRound_M.uasset"
[2025.09.15-16.58.30:928][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_TableRound_N.uasset" to "../../../../../../Auracron/Content/StarterContent/Textures/T_TableRound_N.uasset"
[2025.09.15-16.58.30:929][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Tech_Dot_M.uasset" to "../../../../../../Auracron/Content/StarterContent/Textures/T_Tech_Dot_M.uasset"
[2025.09.15-16.58.30:930][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Tech_Dot_N.uasset" to "../../../../../../Auracron/Content/StarterContent/Textures/T_Tech_Dot_N.uasset"
[2025.09.15-16.58.30:931][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Tech_Hex_Tile_M.uasset" to "../../../../../../Auracron/Content/StarterContent/Textures/T_Tech_Hex_Tile_M.uasset"
[2025.09.15-16.58.30:932][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Tech_Hex_Tile_N.uasset" to "../../../../../../Auracron/Content/StarterContent/Textures/T_Tech_Hex_Tile_N.uasset"
[2025.09.15-16.58.30:933][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Tech_Panel_M.uasset" to "../../../../../../Auracron/Content/StarterContent/Textures/T_Tech_Panel_M.uasset"
[2025.09.15-16.58.30:936][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Tech_Panel_N.uasset" to "../../../../../../Auracron/Content/StarterContent/Textures/T_Tech_Panel_N.uasset"
[2025.09.15-16.58.30:939][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Water_M.uasset" to "../../../../../../Auracron/Content/StarterContent/Textures/T_Water_M.uasset"
[2025.09.15-16.58.30:942][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Water_N.uasset" to "../../../../../../Auracron/Content/StarterContent/Textures/T_Water_N.uasset"
[2025.09.15-16.58.30:948][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Wood_Floor_Walnut_D.uasset" to "../../../../../../Auracron/Content/StarterContent/Textures/T_Wood_Floor_Walnut_D.uasset"
[2025.09.15-16.58.30:952][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Wood_Floor_Walnut_M.uasset" to "../../../../../../Auracron/Content/StarterContent/Textures/T_Wood_Floor_Walnut_M.uasset"
[2025.09.15-16.58.30:957][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Wood_Floor_Walnut_N.uasset" to "../../../../../../Auracron/Content/StarterContent/Textures/T_Wood_Floor_Walnut_N.uasset"
[2025.09.15-16.58.30:964][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Wood_Oak_D.uasset" to "../../../../../../Auracron/Content/StarterContent/Textures/T_Wood_Oak_D.uasset"
[2025.09.15-16.58.30:972][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Wood_Oak_N.uasset" to "../../../../../../Auracron/Content/StarterContent/Textures/T_Wood_Oak_N.uasset"
[2025.09.15-16.58.30:981][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Wood_Pine_D.uasset" to "../../../../../../Auracron/Content/StarterContent/Textures/T_Wood_Pine_D.uasset"
[2025.09.15-16.58.30:987][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Wood_Pine_N.uasset" to "../../../../../../Auracron/Content/StarterContent/Textures/T_Wood_Pine_N.uasset"
[2025.09.15-16.58.30:993][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Wood_Walnut_D.uasset" to "../../../../../../Auracron/Content/StarterContent/Textures/T_Wood_Walnut_D.uasset"
[2025.09.15-16.58.30:997][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Wood_Walnut_N.uasset" to "../../../../../../Auracron/Content/StarterContent/Textures/T_Wood_Walnut_N.uasset"
[2025.09.15-16.58.30:998][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Particles/Materials/M_Burst.uasset" to "../../../../../../Auracron/Content/StarterContent/Particles/Materials/M_Burst.uasset"
[2025.09.15-16.58.30:998][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Particles/Materials/M_Dust_Particle.uasset" to "../../../../../../Auracron/Content/StarterContent/Particles/Materials/M_Dust_Particle.uasset"
[2025.09.15-16.58.30:999][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Particles/Materials/M_explosion_subUV.uasset" to "../../../../../../Auracron/Content/StarterContent/Particles/Materials/M_explosion_subUV.uasset"
[2025.09.15-16.58.30:999][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Particles/Materials/M_Fire_SubUV.uasset" to "../../../../../../Auracron/Content/StarterContent/Particles/Materials/M_Fire_SubUV.uasset"
[2025.09.15-16.58.31:001][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Particles/Materials/m_flare_01.uasset" to "../../../../../../Auracron/Content/StarterContent/Particles/Materials/m_flare_01.uasset"
[2025.09.15-16.58.31:001][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Particles/Materials/M_Heat_Distortion.uasset" to "../../../../../../Auracron/Content/StarterContent/Particles/Materials/M_Heat_Distortion.uasset"
[2025.09.15-16.58.31:004][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Particles/Materials/M_Radial_Gradient.uasset" to "../../../../../../Auracron/Content/StarterContent/Particles/Materials/M_Radial_Gradient.uasset"
[2025.09.15-16.58.31:004][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Particles/Materials/M_radial_ramp.uasset" to "../../../../../../Auracron/Content/StarterContent/Particles/Materials/M_radial_ramp.uasset"
[2025.09.15-16.58.31:010][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Particles/Materials/M_smoke_subUV.uasset" to "../../../../../../Auracron/Content/StarterContent/Particles/Materials/M_smoke_subUV.uasset"
[2025.09.15-16.58.31:012][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Particles/Materials/M_Spark.uasset" to "../../../../../../Auracron/Content/StarterContent/Particles/Materials/M_Spark.uasset"
[2025.09.15-16.58.31:013][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Props/Materials/M_Bush.uasset" to "../../../../../../Auracron/Content/StarterContent/Props/Materials/M_Bush.uasset"
[2025.09.15-16.58.31:013][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Props/Materials/M_Chair.uasset" to "../../../../../../Auracron/Content/StarterContent/Props/Materials/M_Chair.uasset"
[2025.09.15-16.58.31:014][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Props/Materials/M_Door.uasset" to "../../../../../../Auracron/Content/StarterContent/Props/Materials/M_Door.uasset"
[2025.09.15-16.58.31:014][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Props/Materials/M_Frame.uasset" to "../../../../../../Auracron/Content/StarterContent/Props/Materials/M_Frame.uasset"
[2025.09.15-16.58.31:015][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Props/Materials/M_Lamp.uasset" to "../../../../../../Auracron/Content/StarterContent/Props/Materials/M_Lamp.uasset"
[2025.09.15-16.58.31:015][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Props/Materials/M_Rock.uasset" to "../../../../../../Auracron/Content/StarterContent/Props/Materials/M_Rock.uasset"
[2025.09.15-16.58.31:015][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Props/Materials/M_Shelf.uasset" to "../../../../../../Auracron/Content/StarterContent/Props/Materials/M_Shelf.uasset"
[2025.09.15-16.58.31:016][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Props/Materials/M_Statue.uasset" to "../../../../../../Auracron/Content/StarterContent/Props/Materials/M_Statue.uasset"
[2025.09.15-16.58.31:016][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Props/Materials/M_StatueGlass.uasset" to "../../../../../../Auracron/Content/StarterContent/Props/Materials/M_StatueGlass.uasset"
[2025.09.15-16.58.31:016][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Props/Materials/M_TableRound.uasset" to "../../../../../../Auracron/Content/StarterContent/Props/Materials/M_TableRound.uasset"
[2025.09.15-16.58.31:017][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Blueprints/Assets/FogBrightnessLUT.uasset" to "../../../../../../Auracron/Content/StarterContent/Blueprints/Assets/FogBrightnessLUT.uasset"
[2025.09.15-16.58.31:019][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Blueprints/Assets/M_LightStage_Arrows.uasset" to "../../../../../../Auracron/Content/StarterContent/Blueprints/Assets/M_LightStage_Arrows.uasset"
[2025.09.15-16.58.31:019][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Blueprints/Assets/M_LightStage_Skybox_Black.uasset" to "../../../../../../Auracron/Content/StarterContent/Blueprints/Assets/M_LightStage_Skybox_Black.uasset"
[2025.09.15-16.58.31:020][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Blueprints/Assets/M_LightStage_Skybox_HDRI.uasset" to "../../../../../../Auracron/Content/StarterContent/Blueprints/Assets/M_LightStage_Skybox_HDRI.uasset"
[2025.09.15-16.58.31:021][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Blueprints/Assets/M_LightStage_Skybox_Master.uasset" to "../../../../../../Auracron/Content/StarterContent/Blueprints/Assets/M_LightStage_Skybox_Master.uasset"
[2025.09.15-16.58.31:021][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Blueprints/Assets/Skybox.uasset" to "../../../../../../Auracron/Content/StarterContent/Blueprints/Assets/Skybox.uasset"
[2025.09.15-16.58.31:022][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Blueprints/Assets/SM_Arrows.uasset" to "../../../../../../Auracron/Content/StarterContent/Blueprints/Assets/SM_Arrows.uasset"
[2025.09.15-16.58.31:023][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Blueprints/Assets/SunlightColorLUT.uasset" to "../../../../../../Auracron/Content/StarterContent/Blueprints/Assets/SunlightColorLUT.uasset"
[2025.09.15-16.58.31:236][  0]LogFileHelpers: InternalPromptForCheckoutAndSave started...
[2025.09.15-16.58.31:237][  0]OBJ SavePackage: Generating thumbnails for [1] asset(s) in package [/Game/StarterContent/Architecture/Floor_400x400] ([1] browsable assets)...
[2025.09.15-16.58.31:237][  0]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_0
[2025.09.15-16.58.31:841][  0]OBJ SavePackage:     Rendered thumbnail for [StaticMesh /Game/StarterContent/Architecture/Floor_400x400.Floor_400x400]
[2025.09.15-16.58.31:841][  0]OBJ SavePackage: Finished generating thumbnails for package [/Game/StarterContent/Architecture/Floor_400x400]
[2025.09.15-16.58.31:841][  0]Cmd: OBJ SAVEPACKAGE PACKAGE="/Game/StarterContent/Architecture/Floor_400x400" FILE="../../../../../../Auracron/Content/StarterContent/Architecture/Floor_400x400.uasset" SILENT=true
[2025.09.15-16.58.31:856][  0]LogSavePackage: Moving output files for package: /Game/StarterContent/Architecture/Floor_400x400
[2025.09.15-16.58.31:857][  0]LogSavePackage: Moving '../../../../../../Auracron/Saved/Floor_400x4008E8BB17E430F9043BA3DE79B72E74695.tmp' to '../../../../../../Auracron/Content/StarterContent/Architecture/Floor_400x400.uasset'
[2025.09.15-16.58.31:859][  0]LogFileHelpers: InternalPromptForCheckoutAndSave took 623.322 ms
[2025.09.15-16.58.31:859][  0]LogFeaturePack: Inserted 1 feature packs
[2025.09.15-16.58.31:859][  0]Cmd: MAP LOAD FILE="../../../../../../Auracron/Content/FirstPersonBP/Maps/FirstPersonExampleMap.umap" TEMPLATE=0 SHOWPROGRESS=1 FEATURELEVEL=3
[2025.09.15-16.58.31:861][  0]LogWorld: UWorld::CleanupWorld for Untitled, bSessionEnded=true, bCleanupResources=true
[2025.09.15-16.58.31:862][  0]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.09.15-16.58.31:886][  0]LogAudio: Display: Audio Device unregistered from world 'None'.
[2025.09.15-16.58.31:889][  0]LogUObjectHash: Compacting FUObjectHashTables data took   0.78ms
[2025.09.15-16.58.31:891][  0]LogLinker: Warning: [AssetLog] C:\Auracron\Content\FirstPersonBP\Maps\FirstPersonExampleMap.umap: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.58.31:892][  0]LogLinker: Warning: [AssetLog] C:\Auracron\Content\FirstPersonBP\Maps\FirstPersonExampleMap_BuiltData.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.58.31:920][  0]LogLinker: Warning: [AssetLog] C:\Auracron\Content\FirstPersonBP\Blueprints\FirstPersonHUD.uasset: VerifyImport: Failed to find script package for import object 'Package /Script/XRBase'
[2025.09.15-16.58.31:926][  0]LogBlueprint: Error: [AssetLog] C:\Auracron\Content\FirstPersonBP\Blueprints\FirstPersonHUD.uasset: [Compiler] O pin  Return Value  que está em uso não existe mais no nódulo  Is Head Mounted Display Enabled . Atualize o nódulo ou desfaça os vínculos para remover o pin.
[2025.09.15-16.58.31:926][  0]LogBlueprint: Error: [AssetLog] C:\Auracron\Content\FirstPersonBP\Blueprints\FirstPersonHUD.uasset: [Compiler] Não foi possível encontrar uma função de nome "IsHeadMountedDisplayEnabled" em "FirstPersonHUD".
Certifique-se de que "FirstPersonHUD" foi compilado para  Is Head Mounted Display Enabled
[2025.09.15-16.58.31:945][  0]LogAudio: Display: Audio Device (ID: 1) registered with world 'FirstPersonExampleMap'.
[2025.09.15-16.58.31:945][  0]LogChaosDD: Creating Chaos Debug Draw Scene for world FirstPersonExampleMap
[2025.09.15-16.58.31:945][  0]LogAssetRegistry: Display: Asset registry cache written as 94.2 MiB to ../../../../../../Auracron/Intermediate/CachedAssetRegistry_*.bin
[2025.09.15-16.58.31:958][  0]LogEditorServer: Finished looking for orphan Actors (0.000 secs)
[2025.09.15-16.58.31:981][  0]LogUObjectHash: Compacting FUObjectHashTables data took   0.66ms
[2025.09.15-16.58.31:982][  0]Cmd: MAP CHECKDEP NOCLEARLOG
[2025.09.15-16.58.31:982][  0]MapCheck: Verificação do mapa concluída: 0 erro(s), 0 aviso(s), levou 0,074ms para ser concluída.
[2025.09.15-16.58.31:990][  0]LogUnrealEdMisc: Total Editor Startup Time, took 10.452
[2025.09.15-16.58.32:119][  0]LogPlacementMode: Display: The Asset Registry is not yet fully loaded so some placeable classes might be missing.
[2025.09.15-16.58.32:169][  0]LogSlate: The tab "TopLeftModeTab" attempted to spawn in layout 'LevelEditor_Layout_v1.8' but failed for some reason. It will not be displayed.
[2025.09.15-16.58.32:391][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.09.15-16.58.32:545][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.09.15-16.58.32:768][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.09.15-16.58.32:902][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.09.15-16.58.33:118][  0]LogSceneOutliner: Outliner Column Data Layer Visibility does not have a localizable name, please specify one to FSceneOutlinerColumnInfo
[2025.09.15-16.58.33:118][  0]LogSceneOutliner: Outliner Column Data Layer Loaded In Editor does not have a localizable name, please specify one to FSceneOutlinerColumnInfo
[2025.09.15-16.58.33:118][  0]LogSceneOutliner: Outliner Column Item Label does not have a localizable name, please specify one to FSceneOutlinerColumnInfo
[2025.09.15-16.58.33:119][  0]LogSceneOutliner: Outliner Column Remove Actor does not have a localizable name, please specify one to FSceneOutlinerColumnInfo
[2025.09.15-16.58.33:120][  0]LogSceneOutliner: Outliner Column ID Name does not have a localizable name, please specify one to FSceneOutlinerColumnInfo
[2025.09.15-16.58.33:121][  0]LogSceneOutliner: Outliner Column Initial State does not have a localizable name, please specify one to FSceneOutlinerColumnInfo
[2025.09.15-16.58.33:123][  0]LogSceneOutliner: Outliner Column Streaming Priority does not have a localizable name, please specify one to FSceneOutlinerColumnInfo
[2025.09.15-16.58.33:124][  0]LogSceneOutliner: Outliner Column Debug Color does not have a localizable name, please specify one to FSceneOutlinerColumnInfo
[2025.09.15-16.58.33:126][  0]LogSceneOutliner: Outliner Column Data Layer Has Errors does not have a localizable name, please specify one to FSceneOutlinerColumnInfo
[2025.09.15-16.58.33:194][  0]LogPakFile: Initializing PakPlatformFile
[2025.09.15-16.58.33:195][  0]LogPakFile: Display: Mounted Pak file 'C:/Program Files/Epic Games/UE_5.6/FeaturePacks/StarterContent.upack', mount point: 'root:/'
[2025.09.15-16.58.33:195][  0]LogPakFile: Initializing PakPlatformFile
[2025.09.15-16.58.33:196][  0]LogPakFile: Display: Mounted Pak file 'C:/Program Files/Epic Games/UE_5.6/FeaturePacks/TP_FirstPerson.upack', mount point: 'root:/'
[2025.09.15-16.58.33:196][  0]LogPakFile: Initializing PakPlatformFile
[2025.09.15-16.58.33:196][  0]LogPakFile: Display: Mounted Pak file 'C:/Program Files/Epic Games/UE_5.6/FeaturePacks/TP_FirstPersonBP.upack', mount point: 'root:/'
[2025.09.15-16.58.33:196][  0]LogPakFile: Initializing PakPlatformFile
[2025.09.15-16.58.33:197][  0]LogPakFile: Display: Mounted Pak file 'C:/Program Files/Epic Games/UE_5.6/FeaturePacks/TP_HandheldARBP.upack', mount point: 'root:/'
[2025.09.15-16.58.33:198][  0]LogPakFile: Initializing PakPlatformFile
[2025.09.15-16.58.33:198][  0]LogPakFile: Display: Mounted Pak file 'C:/Program Files/Epic Games/UE_5.6/FeaturePacks/TP_ThirdPerson.upack', mount point: 'root:/'
[2025.09.15-16.58.33:198][  0]LogPakFile: Initializing PakPlatformFile
[2025.09.15-16.58.33:199][  0]LogPakFile: Display: Mounted Pak file 'C:/Program Files/Epic Games/UE_5.6/FeaturePacks/TP_ThirdPersonBP.upack', mount point: 'root:/'
[2025.09.15-16.58.33:199][  0]LogPakFile: Initializing PakPlatformFile
[2025.09.15-16.58.33:199][  0]LogPakFile: Display: Mounted Pak file 'C:/Program Files/Epic Games/UE_5.6/FeaturePacks/TP_TopDown.upack', mount point: 'root:/'
[2025.09.15-16.58.33:199][  0]LogPakFile: Initializing PakPlatformFile
[2025.09.15-16.58.33:200][  0]LogPakFile: Display: Mounted Pak file 'C:/Program Files/Epic Games/UE_5.6/FeaturePacks/TP_TopDownBP.upack', mount point: 'root:/'
[2025.09.15-16.58.33:200][  0]LogPakFile: Initializing PakPlatformFile
[2025.09.15-16.58.33:200][  0]LogPakFile: Display: Mounted Pak file 'C:/Program Files/Epic Games/UE_5.6/FeaturePacks/TP_VehicleAdvBP.upack', mount point: 'root:/'
[2025.09.15-16.58.33:200][  0]LogPakFile: Initializing PakPlatformFile
[2025.09.15-16.58.33:201][  0]LogPakFile: Display: Mounted Pak file 'C:/Program Files/Epic Games/UE_5.6/FeaturePacks/TP_VirtualRealityBP.upack', mount point: 'root:/'
[2025.09.15-16.58.33:711][  0]LogSlate: Took 0.000230 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Regular.ttf' (155K)
[2025.09.15-16.58.33:714][  0]LogSlate: Took 0.000200 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Italic.ttf' (157K)
[2025.09.15-16.58.33:715][  0]LogSlate: Took 0.000176 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/DroidSansMono.ttf' (77K)
[2025.09.15-16.58.34:114][  0]LogStall: Startup...
[2025.09.15-16.58.34:121][  0]LogStall: Startup complete.
[2025.09.15-16.58.34:131][  0]LogInput: Warning: Action Fire uses invalid key MotionController_Right_Trigger.
[2025.09.15-16.58.34:131][  0]LogInput: Warning: Action Jump uses invalid key MotionController_Left_Trigger.
[2025.09.15-16.58.34:131][  0]LogInput: Warning: Action ResetVR uses invalid key MotionController_Left_Grip1.
[2025.09.15-16.58.34:131][  0]LogInput: Warning: Action Fire uses invalid key OculusTouchpad_Touchpad.
[2025.09.15-16.58.34:131][  0]LogInput: Warning: Use -RemoveInvalidKeys to remove instances of these keys from the action mapping.
[2025.09.15-16.58.34:131][  0]LogLoad: (Engine Initialization) Total time: 12.59 seconds
[2025.09.15-16.58.34:636][  0]LogSlate: Took 0.000159 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Bold.ttf' (160K)
[2025.09.15-16.58.34:689][  0]LogContentValidation: Display: Starting to validate 1 assets
[2025.09.15-16.58.34:689][  0]LogContentValidation: Enabled validators:
[2025.09.15-16.58.34:689][  0]LogContentValidation: Warning: UpdateValidators request made before RegisterBlueprintValidators. Blueprint validators may be missing!
[2025.09.15-16.58.34:689][  0]LogContentValidation: 	/Script/DataValidation.EditorValidator_Material
[2025.09.15-16.58.34:689][  0]LogContentValidation: 	/Script/DataValidation.DirtyFilesChangelistValidator
[2025.09.15-16.58.34:689][  0]LogContentValidation: 	/Script/DataValidation.EditorValidator_Localization
[2025.09.15-16.58.34:689][  0]LogContentValidation: 	/Script/DataValidation.PackageFileValidator
[2025.09.15-16.58.34:689][  0]LogContentValidation: 	/Script/DataValidation.WorldPartitionChangelistValidator
[2025.09.15-16.58.34:689][  0]LogContentValidation: 	/Script/InputBlueprintNodes.EnhancedInputUserWidgetValidator
[2025.09.15-16.58.34:689][  0]LogContentValidation: 	/Script/GameFeaturesEditor.IllegalPluginDependenciesValidator
[2025.09.15-16.58.34:689][  0]LogContentValidation: 	/Script/AssetReferenceRestrictions.EditorValidator_PluginAssetReferences
[2025.09.15-16.58.34:689][  0]LogContentValidation: 	/Script/AssetReferenceRestrictions.AssetValidator_AssetReferenceRestrictions
[2025.09.15-16.58.34:689][  0]LogContentValidation: Warning: UpdateValidators request made before RegisterBlueprintValidators. Blueprint validators may be missing!
[2025.09.15-16.58.34:690][  0]AssetCheck: /Game/StarterContent/Architecture/Floor_400x400 Validando ativo
[2025.09.15-16.58.34:690][  0]AssetCheck: Error: /Game/StarterContent/Architecture/Floor_400x400 Avisos registrados durante a validação do ativo UpdateValidators request made before RegisterBlueprintValidators. Blueprint validators may be missing!
[2025.09.15-16.58.34:916][  0]LogContentStreaming: Texture pool size now 1000 MB
[2025.09.15-16.58.34:916][  0]LogCsvProfiler: Display: Metadata set : streamingpoolsizemb="1000"
[2025.09.15-16.58.34:992][  0]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.09.15-16.58.34:993][  0]LogPython: Display: Executando código de inicialização C:/Program Files/Epic Games/UE_5.6/Engine/Plugins/MovieScene/MovieRenderPipeline/Content/Python/init_unreal.py... started...
[2025.09.15-16.58.35:012][  0]LogPython: Display: Executando código de inicialização C:/Program Files/Epic Games/UE_5.6/Engine/Plugins/MovieScene/MovieRenderPipeline/Content/Python/init_unreal.py... took 19.173 ms
[2025.09.15-16.58.35:012][  0]LogPython: Display: Executando código de inicialização C:/Program Files/Epic Games/UE_5.6/Engine/Plugins/Animation/ControlRig/Content/Python/init_unreal.py... started...
[2025.09.15-16.58.35:029][  0]LogPython: registering <class 'ControlRigWorkflows.workflow_deformation_rig_preset.provider'>

[2025.09.15-16.58.35:039][  0]LogPython: Display: Executando código de inicialização C:/Program Files/Epic Games/UE_5.6/Engine/Plugins/Animation/ControlRig/Content/Python/init_unreal.py... took 27.157 ms (total: 46.331 ms)
[2025.09.15-16.58.35:039][  0]LoadErrors: Warning: Ao tentar carregar o pacote /Game/FirstPersonBP/Maps/FirstPersonExampleMap, um pacote dependente /HDRIBackdrop/Materials/MI_HDRI_CenterProjection não estava disponível. Confira mais informações detalhadas:
FPackageName: Skipped package /HDRIBackdrop/Materials/MI_HDRI_CenterProjection has a valid, mounted, mount point but does not exist either on disk or in iostore. The uncooked file would be expected on disk at 'C:/Program Files/Epic Games/UE_5.6/Engine/Plugins/Runtime/HDRIBackdrop/Content/Materials/MI_HDRI_CenterProjection'. Perhaps it has been deleted or was not synced?

[2025.09.15-16.58.35:040][  0]LogSourceControl: Display: Uncontrolled Changelist persistency file saved ../../../../../../Auracron/Saved/SourceControl/UncontrolledChangelists.json
[2025.09.15-16.58.35:384][  1]LogAssetRegistry: AssetRegistryGather time 0.3541s: AssetDataDiscovery 0.0488s, AssetDataGather 0.1724s, StoreResults 0.1329s. Wall time 10.0430s.
	NumCachedDirectories 0. NumUncachedDirectories 1933. NumCachedFiles 9715. NumUncachedFiles 534.
	BackgroundTickInterruptions 0.
[2025.09.15-16.58.35:410][  1]LogPlacementMode: Display: The Asset Registry is done with its initial scan, the list of placeable classes has been updated.
[2025.09.15-16.58.35:418][  1]LogCollectionManager: Fixed up redirectors for 0 collections in 0.000000 seconds (updated 0 objects)
[2025.09.15-16.58.35:419][  1]LogSourceControl: Uncontrolled asset discovery started...
[2025.09.15-16.58.35:811][  2]LogSourceControl: Uncontrolled asset discovery finished in 0.391923 seconds (Found 9958 uncontrolled assets)
[2025.09.15-16.58.35:816][  2]LogEOSSDK: LogEOS: SDK Config Platform Update Request Successful, Time: 7.458668
[2025.09.15-16.58.35:818][  2]LogEOSSDK: LogEOSAnalytics: EOS SDK Analytics disabled for route [1].
[2025.09.15-16.58.35:819][  2]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 7.482220
[2025.09.15-16.58.37:143][  6]LogEOSSDK: LogEOSAnalytics: Start Session (User: ...)
[2025.09.15-16.58.37:809][  8]LogEOSSDK: LogEOS: SDK Config Product Update Request Successful, Time: 9.141302
[2025.09.15-16.58.37:812][  8]LogEOSSDK: LogEOS: SDK Config Data - Watermark: 1542370262
[2025.09.15-16.58.37:812][  8]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 9.141302, Update Interval: 303.330780
[2025.09.15-16.59.14:066][428]LogSlate: Window 'Log de Mensagens' being destroyed
[2025.09.15-16.59.14:138][428]LogSlate: Window 'Log de Mensagens' being destroyed
[2025.09.15-16.59.20:482][577]LogSlate: Took 0.000218 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Light.ttf' (167K)
[2025.09.15-16.59.26:418][685]LogUObjectHash: Compacting FUObjectHashTables data took   0.53ms
[2025.09.15-16.59.26:419][685]Cmd: MAP LOAD FILE="../../../../../../Auracron/Content/Maps/RealLeague/RealLeague.umap" TEMPLATE=0 SHOWPROGRESS=1 FEATURELEVEL=3
[2025.09.15-16.59.26:434][685]LogWorld: UWorld::CleanupWorld for FirstPersonExampleMap, bSessionEnded=true, bCleanupResources=true
[2025.09.15-16.59.26:434][685]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.09.15-16.59.26:460][685]LogAudio: Display: Audio Device unregistered from world 'None'.
[2025.09.15-16.59.26:462][685]LogUObjectHash: Compacting FUObjectHashTables data took   0.44ms
[2025.09.15-16.59.26:462][685]LogStreaming: Display: FlushAsyncLoading(380): 1 QueuedPackages, 0 AsyncPackages
[2025.09.15-16.59.26:474][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\FreedomPuzzle\Blueprints\Tools\BP_MeshSplines.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:474][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Maps\RealLeague\RealLeague_sharedassets\Dirt_LayerInfo.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:474][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Maps\RealLeague\RealLeague_sharedassets\DirtRock_LayerInfo.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:474][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Maps\RealLeague\RealLeague_sharedassets\Grass_LayerInfo.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:474][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Env\RealLeague\Sequences\RiotTest\LeagueDirect2Unreal.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:475][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Env\RealLeague\Landscape\M_LeagueLandscape_Inst.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:475][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Env\RealLeague\Overlay\M_Overlay_Still1.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:475][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Env\RealLeague\Puddle\M_Puddle_Inst.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:475][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Env\RealLeague\Puddle\M_Spots_Inst.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:475][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Env\RealLeague\Sequences\RiotTest\SceneDepthNew_Inst.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:476][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Env\RockArea\Materials\MF_StillWater_Inst1.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:476][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Asset\010_rock_jagged_skjsK\010_rock_jagged_skjsK_2K_inst1.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:476][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Asset\01_rock_mossy_tbpwp\01_rock_mossy_tbpwp_2K_inst1.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:476][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Asset\02_sandy_rough_tdpseesda\02_sandy_rough_tdpseesda_2K_inst2.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:476][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Asset\02_sandy_rough_tdpseesda\02_sandy_rough_tdpseesda_2K_inst3.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:476][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Asset\03_sandy_smooth_tdpsddvda\03_sandy_smooth_tdpsddvda_2K_inst2.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:476][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Asset\07_mossy_smooth_tcvscgada\07_mossy_smooth_tcvscgada_2K_inst1.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:476][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Asset\09_rock_jagged_taclb\09_rock_jagged_taclb_2K_inst1.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:477][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Asset\11_rock_cliffs_skhxj\11_rock_cliffs_skhxj_2K_inst1.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:477][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Asset\49_Roman_Column_tkxkaeqfa\49_Roman_Column_tkxkaeqfa_2K_inst1.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:477][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Asset\53_Roman_Marble_Capital_thzgcaffa\53_Roman_Marble_Capital_thzgcaffa_2K_inst1.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:488][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Env\RealLeague\Landscape\M_LeagueLandscape.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:488][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Env\RealLeague\Overlay\M_Overlay.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:488][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Env\RealLeague\Overlay\c2bd7aede8be22e8808185428166e5a9.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:488][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Env\RealLeague\Puddle\M_Puddle.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:489][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Env\RealLeague\Puddle\M_Spots.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:489][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Env\RealLeague\Sequences\RiotTest\SceneDepthNew.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:489][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\Master_Materials\DirectionalBlend_MasterMaterial.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:490][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Asset\010_rock_jagged_skjsK\skjsK_2K_Albedo.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:490][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Asset\010_rock_jagged_skjsK\skjsK_2K_Normal_LOD0.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:490][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Asset\010_rock_jagged_skjsK\skjsK_2K_Roughness.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:490][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\Surface\04_Mossy_Rock_tjylbagr\tjylbagr_Albedo.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:490][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\Surface\04_Mossy_Rock_tjylbagr\tjylbagr_Normal.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:490][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\Surface\04_Mossy_Rock_tjylbagr\tjylbagr_Roughness.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:490][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Asset\01_rock_mossy_tbpwp\tbpwp_2K_Albedo.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:491][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Asset\01_rock_mossy_tbpwp\tbpwp_2K_Displacement.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:491][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Asset\01_rock_mossy_tbpwp\tbpwp_2K_Normal_LOD0.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:491][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Asset\01_rock_mossy_tbpwp\tbpwp_2K_Roughness.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:491][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Asset\02_sandy_rough_tdpseesda\tdpseesda_2K_Albedo.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:491][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Asset\02_sandy_rough_tdpseesda\tdpseesda_2K_Normal_LOD0.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:491][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Asset\02_sandy_rough_tdpseesda\tdpseesda_2K_Roughness.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:491][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Asset\03_sandy_smooth_tdpsddvda\tdpsddvda_2K_Albedo.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:492][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Asset\03_sandy_smooth_tdpsddvda\tdpsddvda_2K_Normal_LOD0.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:492][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Asset\03_sandy_smooth_tdpsddvda\tdpsddvda_2K_Roughness.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:492][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Asset\07_mossy_smooth_tcvscgada\tcvscgada_2K_Albedo.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:492][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Asset\07_mossy_smooth_tcvscgada\tcvscgada_2K_Normal_LOD0.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:492][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Asset\07_mossy_smooth_tcvscgada\tcvscgada_2K_Roughness.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:492][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Asset\09_rock_jagged_taclb\taclb_2K_Albedo.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:492][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Asset\09_rock_jagged_taclb\taclb_2K_Normal_LOD0.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:493][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Asset\09_rock_jagged_taclb\taclb_2K_Roughness.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:493][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Asset\11_rock_cliffs_skhxj\skhxj_2K_Albedo.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:493][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Asset\11_rock_cliffs_skhxj\skhxj_2K_Normal_LOD0.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:493][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Asset\11_rock_cliffs_skhxj\skhxj_2K_Roughness.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:493][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Asset\49_Roman_Column_tkxkaeqfa\tkxkaeqfa_Albedo.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:493][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Asset\49_Roman_Column_tkxkaeqfa\tkxkaeqfa_Displacement.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:493][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Asset\49_Roman_Column_tkxkaeqfa\tkxkaeqfa_Normal.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:493][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Asset\49_Roman_Column_tkxkaeqfa\tkxkaeqfa_Roughness.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:494][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Asset\53_Roman_Marble_Capital_thzgcaffa\thzgcaffa_Albedo.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:494][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Asset\53_Roman_Marble_Capital_thzgcaffa\thzgcaffa_Displacement.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:494][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Asset\53_Roman_Marble_Capital_thzgcaffa\thzgcaffa_Normal.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:494][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Asset\53_Roman_Marble_Capital_thzgcaffa\thzgcaffa_Roughness.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:494][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Env\IceSnow\Trees\Bark_Branch_Mat.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:494][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Env\IceSnow\Trees\Pine1_Leaf_Mat_Inst.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:494][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Env\IceSnow\Trees\Pine1_Leaf_Mat_Inst_Inst.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:495][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Env\RealLeague\Roads\MI_Road.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:495][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Asset\54_Damaged_Castle_Stairs_sevuM\54_Damaged_Castle_Stairs_sevuM_2K_inst.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:495][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\Scatter_3D\07_Cement_Curbs_sekx4\07_Cement_Curbs_sekx4_2K_inst.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:495][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Asset\010_rock_jagged_skjsK\010_rock_jagged_skjsK_2K_inst.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:495][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Asset\01_rock_mossy_tbpwp\01_rock_mossy_tbpwp_2K_inst.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:496][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Asset\02_sandy_rough_tdpseesda\02_sandy_rough_tdpseesda_2K_inst.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:496][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Asset\03_sandy_smooth_tdpsddvda\03_sandy_smooth_tdpsddvda_2K_inst.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:496][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Asset\07_mossy_smooth_tcvscgada\07_mossy_smooth_tcvscgada_2K_inst.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:496][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Asset\09_rock_jagged_taclb\09_rock_jagged_taclb_2K_inst.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:496][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Asset\11_rock_cliffs_skhxj\11_rock_cliffs_skhxj_2K_inst.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:496][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Asset\15_tree_stump_qlEtl\15_tree_stump_qlEtl_2K_inst.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:496][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Asset\16_rock_volcanic_qcDam\16_rock_volcanic_qcDam_2K_inst.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:496][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Asset\17_tree_branch_phyrG\17_tree_branch_phyrG_2K_inst.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:496][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Asset\34_Mossy_Rocks_tixjbfdfa\34_Mossy_Rocks_tixjbfdfa_4K_inst.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:496][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Asset\35_Mossy_Rocks_th5fchafa\35_Mossy_Rocks_th5fchafa_4K_inst.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:496][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Asset\37_Mossy_Embankment_tlnvecpfa\37_Mossy_Embankment_tlnvecpfa_2K_inst.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:497][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Asset\38_Forest_Roots_tkwqbbofa\38_Forest_Roots_tkwqbbofa_2K_inst.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:497][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Asset\40_Weathered_Icelandic_Lava_Spire_sinxD\40_Weathered_Icelandic_Lava_Spire_sinxD_2K_inst.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:497][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Asset\42_Mossy_Tree_Stump_qdyf7\42_Mossy_Tree_Stump_qdyf7_2K_inst.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:497][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Asset\43_Broken_Tree_Stump_rfxaw\43_Broken_Tree_Stump_rfxaw_2K_inst.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:497][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Asset\44_Fallen_Pine_Tree_rfgxx\44_Fallen_Pine_Tree_rfgxx_2K_inst.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:497][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Asset\45_Mossy_Tree_qbtt7\45_Mossy_Tree_qbtt7_2K_inst.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:498][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Asset\49_Roman_Column_tkxkaeqfa\49_Roman_Column_tkxkaeqfa_2K_inst.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:498][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Asset\53_Roman_Marble_Capital_thzgcaffa\53_Roman_Marble_Capital_thzgcaffa_2K_inst.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:498][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Asset\55_Ceramic_Bowl_tltkbaahw\55_Ceramic_Bowl_tltkbaahw_2K_inst.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:498][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Asset\56_Damaged_Castle_Floor_Stones_sgmgu\56_Damaged_Castle_Floor_Stones_sgmgu_2K_inst.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:498][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Asset\57_Castle_Floor_Ruins_sgmgm\57_Castle_Floor_Ruins_sgmgm_2K_inst.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:499][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Plant\01_plants_3d_tdcnfcdr\01_plants_3d_tdcnfcdr_2K_inst.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:499][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Plant\03_plants_3d_rmyxk\03_plants_3d_rmyxk_2K_inst.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:499][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Plant\04_plants_3d_rhDso\04_plants_3d_rhDso_2K_inst.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:499][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Plant\11_Lily_Of_The_Valley_tkehfifia\11_Lily_Of_The_Valley_tkehfifia_2K_inst.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:499][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Plant\Grass\rbjkK_Grass.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:500][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\Scatter_3D\03_Dry_Branches_pdye3\03_Dry_Branches_pdye3_2K_inst.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:500][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\Scatter_3D\04_Icelandic_Sandy_Volcanic_Rock_slnv6\04_Icelandic_Sandy_Volcanic_Rock_slnv6_2K_inst.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:500][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Env\RockArea\Materials\MF\MF_Dirt.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:500][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Env\RockArea\Materials\MF\MF_Dirt2.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:501][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Env\RockArea\Materials\MF\MF_Grass.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:501][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Env\RockArea\Textures\Landscape\CloverGrass\sbykrep0_Albedo.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:501][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Env\RockArea\Textures\Landscape\CloverGrass\sbykrep0_Normal.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:501][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Env\RockArea\Textures\Landscape\CloverGrass\sbykrep0_R_AO_H_M.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:501][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Env\RockArea\Textures\Landscape\Dirt\Dirt2_Albedo.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:501][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Env\RockArea\Textures\Landscape\Dirt\Dirt2_Normal.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:501][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Env\RockArea\Textures\Landscape\Dirt\Dirt2_R_AO_H_M.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:501][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Env\RockArea\Textures\Landscape\Dirt\sbhjzfp0_Albedo.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:502][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Env\RockArea\Textures\Landscape\Dirt\sbhjzfp0_Normal.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:502][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Env\RockArea\Textures\Landscape\Dirt\sbhjzfp0_R_AO_H_M.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:502][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Env\RealLeague\Puddle\Puddle_Albedo.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:502][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Env\RealLeague\Puddle\Puddle_Mask.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:502][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Env\RealLeague\Puddle\Puddle_Normal.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:502][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Env\RealLeague\Puddle\Puddle_Roughness.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:504][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Env\RockArea\Materials\MF\MF_TessDistance.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:504][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Env\RealLeague\MapColor.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:504][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Env\IceSnow\Textures\Glitter_Diffuse.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:504][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Env\RockArea\Textures\Landscape\IcelandicStone\IcelandicStone_Normal.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:504][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Env\IceSnow\Trees\Bark_Color_Tex.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:504][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Env\IceSnow\Trees\Bark_Normal_Tex.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:504][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Env\IceSnow\Trees\Pine1_Leaf_Mat.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:505][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Env\IceSnow\Trees\Pine3_Color.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:505][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Env\IceSnow\Trees\Pine3_Normal.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:505][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\Master_Materials\Standard_MasterMaterial.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:505][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Env\RealLeague\Roads\DirtRoad_ShoeWheelPrints_Albedo.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:505][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Env\RealLeague\Roads\DirtRoad_ShoeWheelPrints_Displacement.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:506][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Env\RealLeague\Roads\DirtRoad_ShoeWheelPrints_Normal.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:506][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Env\RealLeague\Roads\DirtRoad_ShoeWheelPrints_Roughness.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:506][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Asset\54_Damaged_Castle_Stairs_sevuM\sevuM_Albedo.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:506][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Asset\54_Damaged_Castle_Stairs_sevuM\sevuM_Displacement.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:506][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Asset\54_Damaged_Castle_Stairs_sevuM\sevuM_Normal.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:506][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Asset\54_Damaged_Castle_Stairs_sevuM\sevuM_Roughness.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:507][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\Scatter_3D\07_Cement_Curbs_sekx4\sekx4_Albedo.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:507][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\Scatter_3D\07_Cement_Curbs_sekx4\sekx4_Displacement.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:507][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\Scatter_3D\07_Cement_Curbs_sekx4\sekx4_Normal.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:507][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\Scatter_3D\07_Cement_Curbs_sekx4\sekx4_Roughness.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:508][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Asset\15_tree_stump_qlEtl\Aset_wood_tree_L_qlEtl_2K_Albedo.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:508][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Asset\15_tree_stump_qlEtl\Aset_wood_tree_L_qlEtl_2K_Normal_LOD0.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:508][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Asset\15_tree_stump_qlEtl\Aset_wood_tree_L_qlEtl_2K_Roughness.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:508][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Asset\16_rock_volcanic_qcDam\Aset_rock_volcanic_M_qcDam_2K_Albedo.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:508][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Asset\16_rock_volcanic_qcDam\Aset_rock_volcanic_M_qcDam_2K_Normal_LOD0.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:508][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Asset\16_rock_volcanic_qcDam\Aset_rock_volcanic_M_qcDam_2K_Roughness.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:508][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Asset\17_tree_branch_phyrG\Aset_wood_branch_S_phyrG_2K_Albedo.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:508][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Asset\17_tree_branch_phyrG\Aset_wood_branch_S_phyrG_2K_Normal_LOD0.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:509][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Asset\17_tree_branch_phyrG\Aset_wood_branch_S_phyrG_2K_Roughness.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:509][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Asset\34_Mossy_Rocks_tixjbfdfa\tixjbfdfa_4K_Albedo.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:509][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Asset\34_Mossy_Rocks_tixjbfdfa\tixjbfdfa_4K_Displacement.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:509][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Asset\34_Mossy_Rocks_tixjbfdfa\tixjbfdfa_4K_Normal_LOD0.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:509][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Asset\34_Mossy_Rocks_tixjbfdfa\tixjbfdfa_4K_Roughness.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:509][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Asset\35_Mossy_Rocks_th5fchafa\th5fchafa_4K_Albedo.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:509][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Asset\35_Mossy_Rocks_th5fchafa\th5fchafa_4K_Displacement.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:510][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Asset\35_Mossy_Rocks_th5fchafa\th5fchafa_4K_Normal_LOD0.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:510][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Asset\35_Mossy_Rocks_th5fchafa\th5fchafa_4K_Roughness.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:510][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Asset\37_Mossy_Embankment_tlnvecpfa\tlnvecpfa_Albedo.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:510][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Asset\37_Mossy_Embankment_tlnvecpfa\tlnvecpfa_Displacement.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:510][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Asset\37_Mossy_Embankment_tlnvecpfa\tlnvecpfa_Normal.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:511][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Asset\37_Mossy_Embankment_tlnvecpfa\tlnvecpfa_Roughness.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:511][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Asset\38_Forest_Roots_tkwqbbofa\tkwqbbofa_Albedo.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:511][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Asset\38_Forest_Roots_tkwqbbofa\tkwqbbofa_Displacement.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:511][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Asset\38_Forest_Roots_tkwqbbofa\tkwqbbofa_Normal.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:511][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Asset\38_Forest_Roots_tkwqbbofa\tkwqbbofa_Roughness.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:511][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Asset\40_Weathered_Icelandic_Lava_Spire_sinxD\sinxD_Albedo.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:511][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Asset\40_Weathered_Icelandic_Lava_Spire_sinxD\sinxD_Displacement.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:512][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Asset\40_Weathered_Icelandic_Lava_Spire_sinxD\sinxD_Normal.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:512][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Asset\40_Weathered_Icelandic_Lava_Spire_sinxD\sinxD_Roughness.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:512][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Asset\42_Mossy_Tree_Stump_qdyf7\qdyf7_Albedo.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:512][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Asset\42_Mossy_Tree_Stump_qdyf7\qdyf7_Displacement.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:512][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Asset\42_Mossy_Tree_Stump_qdyf7\qdyf7_Normal.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:512][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Asset\42_Mossy_Tree_Stump_qdyf7\qdyf7_Roughness.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:512][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Asset\43_Broken_Tree_Stump_rfxaw\rfxaw_Albedo.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:512][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Asset\43_Broken_Tree_Stump_rfxaw\rfxaw_Displacement.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:513][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Asset\43_Broken_Tree_Stump_rfxaw\rfxaw_Normal.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:513][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Asset\43_Broken_Tree_Stump_rfxaw\rfxaw_Roughness.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:513][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Asset\44_Fallen_Pine_Tree_rfgxx\Aset_wood_log_M_rfgxx_2K_Normal_LOD2.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:513][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Asset\44_Fallen_Pine_Tree_rfgxx\rfgxx_Albedo.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:513][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Asset\44_Fallen_Pine_Tree_rfgxx\rfgxx_Displacement.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:514][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Asset\44_Fallen_Pine_Tree_rfgxx\rfgxx_Roughness.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:514][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Asset\45_Mossy_Tree_qbtt7\qbtt7_Albedo.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:514][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Asset\45_Mossy_Tree_qbtt7\qbtt7_Displacement.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:514][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Asset\45_Mossy_Tree_qbtt7\qbtt7_Normal.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:514][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Asset\45_Mossy_Tree_qbtt7\qbtt7_Roughness.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:515][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Env\IceSnow\Textures\Snow_Diffuse.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:515][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Env\IceSnow\Textures\Snow_Normal.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:515][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Env\IceSnow\Textures\Snow_R_AO_H_G.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:515][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Asset\55_Ceramic_Bowl_tltkbaahw\tltkbaahw_Albedo.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:515][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Asset\55_Ceramic_Bowl_tltkbaahw\tltkbaahw_Displacement.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:515][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Asset\55_Ceramic_Bowl_tltkbaahw\tltkbaahw_Normal.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:516][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Asset\55_Ceramic_Bowl_tltkbaahw\tltkbaahw_Roughness.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:516][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\Surface\01_Rock_Cliff_Layered_2x2_M_thficeas\thficeas_2K_Albedo.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:516][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\Surface\01_Rock_Cliff_Layered_2x2_M_thficeas\thficeas_2K_Normal.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:516][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\Surface\01_Rock_Cliff_Layered_2x2_M_thficeas\thficeas_2K_Roughness.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:516][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Asset\56_Damaged_Castle_Floor_Stones_sgmgu\sgmgu_Albedo.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:516][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Asset\56_Damaged_Castle_Floor_Stones_sgmgu\sgmgu_Displacement.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:516][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Asset\56_Damaged_Castle_Floor_Stones_sgmgu\sgmgu_Normal.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:516][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Asset\56_Damaged_Castle_Floor_Stones_sgmgu\sgmgu_Roughness.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:516][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Asset\57_Castle_Floor_Ruins_sgmgm\sgmgm_Albedo.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:517][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Asset\57_Castle_Floor_Ruins_sgmgm\sgmgm_Displacement.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:517][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Asset\57_Castle_Floor_Ruins_sgmgm\sgmgm_Normal.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:517][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Asset\57_Castle_Floor_Ruins_sgmgm\sgmgm_Roughness.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:517][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\Master_Materials\Foliage_MasterMaterial.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:518][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Plant\01_plants_3d_tdcnfcdr\tdbn2ap_2K_Albedo.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:518][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Plant\01_plants_3d_tdcnfcdr\tdbn2ap_2K_Displacement.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:518][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Plant\01_plants_3d_tdcnfcdr\tdbn2ap_2K_Normal.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:518][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Plant\01_plants_3d_tdcnfcdr\tdbn2ap_2K_Opacity.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:518][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Plant\01_plants_3d_tdcnfcdr\tdbn2ap_2K_Roughness.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:518][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Plant\01_plants_3d_tdcnfcdr\tdbn2ap_2K_Translucency.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:519][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Plant\03_plants_3d_rmyxk\CreeperIvy_2K_Albedo.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:519][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Plant\03_plants_3d_rmyxk\CreeperIvy_2K_Displacement.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:519][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Plant\03_plants_3d_rmyxk\CreeperIvy_2K_Normal.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:519][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Plant\03_plants_3d_rmyxk\CreeperIvy_2K_Opacity.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:519][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Plant\03_plants_3d_rmyxk\CreeperIvy_2K_Roughness.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:520][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Plant\03_plants_3d_rmyxk\CreeperIvy_2K_Translucency.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:520][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\MSPresets\Foliage_Material\Foliage_Material.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:520][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Plant\04_plants_3d_rhDso\ForestFern_2K_Albedo.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:520][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Plant\04_plants_3d_rhDso\ForestFern_2K_Normal.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:520][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Plant\04_plants_3d_rhDso\ForestFern_2K_Opacity.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:521][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Plant\04_plants_3d_rhDso\ForestFern_2K_Roughness.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:521][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Plant\04_plants_3d_rhDso\ForestFern_2K_Translucency.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:521][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Plant\11_Lily_Of_The_Valley_tkehfifia\tkehfifia_Albedo.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:521][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Plant\11_Lily_Of_The_Valley_tkehfifia\tkehfifia_Normal.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:521][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Plant\11_Lily_Of_The_Valley_tkehfifia\tkehfifia_Opacity.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:521][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Plant\11_Lily_Of_The_Valley_tkehfifia\tkehfifia_Translucency.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:522][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Plant\Grass\rbjkK_2K_Albedo.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:522][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Plant\Grass\rbjkK_2K_Normal.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:522][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Plant\Grass\rbjkK_2K_Opacity.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:522][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Plant\Grass\rbjkK_2K_Specular.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:522][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\3D_Plant\Grass\rbjkK_2K_Translucency.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:522][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\Scatter_3D\03_Dry_Branches_pdye3\pdye3_Albedo.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:523][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\Scatter_3D\03_Dry_Branches_pdye3\pdye3_Displacement.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:523][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\Scatter_3D\03_Dry_Branches_pdye3\pdye3_Roughness.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:523][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\Scatter_3D\04_Icelandic_Sandy_Volcanic_Rock_slnv6\slnv6_2K_Normal_LOD1.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:523][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\Scatter_3D\04_Icelandic_Sandy_Volcanic_Rock_slnv6\slnv6_Albedo.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:523][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\Scatter_3D\04_Icelandic_Sandy_Volcanic_Rock_slnv6\slnv6_Displacement.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:524][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Megascans\Scatter_3D\04_Icelandic_Sandy_Volcanic_Rock_slnv6\slnv6_Roughness.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:534][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Env\IceSnow\Trees\Pine1_Color.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.26:534][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Env\IceSnow\Trees\Pine1_Normal.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.27:109][685]LogAudio: Display: Audio Device (ID: 1) registered with world 'RealLeague'.
[2025.09.15-16.59.27:111][685]LogChaosDD: Creating Chaos Debug Draw Scene for world RealLeague
[2025.09.15-16.59.27:216][685]LogStreaming: Display: ULevelStreaming::RequestLevel(/Game/Maps/RealLeague/RealLeague_Towers) is flushing async loading
[2025.09.15-16.59.27:217][685]LogStreaming: Display: ULevelStreaming::RequestLevel(/Game/Maps/RealLeague/Lighting/RealLeague_Normal) is flushing async loading
[2025.09.15-16.59.27:738][685]LogStreaming: Display: ULevelStreaming::RequestLevel(/Game/Maps/RealLeague/Lighting/RealLeague_Light1_LUT) is flushing async loading
[2025.09.15-16.59.27:739][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Env\LUTs\LUT_PurpleOrange.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.27:746][685]LogStreaming: Display: ULevelStreaming::RequestLevel(/Game/Maps/RealLeague/Lighting/RealLeague_Normal_LUT) is flushing async loading
[2025.09.15-16.59.27:748][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Env\LUTs\LUT_GrimDay.uasset: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.27:759][685]LogStreaming: Display: ULevelStreaming::RequestLevel(/Game/Maps/RealLeague/Lighting/RealLeague_Light2) is flushing async loading
[2025.09.15-16.59.27:764][685]LogStreaming: Display: ULevelStreaming::RequestLevel(/Game/Maps/RealLeague/RealLeague_Cameras) is flushing async loading
[2025.09.15-16.59.27:764][685]LogLinker: Warning: [AssetLog] C:\Auracron\Content\Maps\RealLeague\RealLeague_Cameras.umap: Asset has been saved with empty engine version. The asset will be loaded but may be incompatible.
[2025.09.15-16.59.27:765][685]LogStreaming: Display: ULevelStreaming::RequestLevel(/Game/Maps/RealLeague/Lighting/RealLeague_Light1) is flushing async loading
[2025.09.15-16.59.27:909][685]LogEditorServer: Finished looking for orphan Actors (0.000 secs)
[2025.09.15-16.59.27:966][685]LogUObjectHash: Compacting FUObjectHashTables data took   0.70ms
[2025.09.15-16.59.27:971][685]Cmd: MAP CHECKDEP NOCLEARLOG
[2025.09.15-16.59.27:971][685]MapCheck: Verificação do mapa concluída: 0 erro(s), 0 aviso(s), levou 0,233ms para ser concluída.
[2025.09.15-16.59.27:972][685]LogSlate: Window 'Abrir nível' being destroyed
[2025.09.15-16.59.28:785][685]LoadErrors: Warning: Ao tentar carregar o pacote /Game/Maps/RealLeague/Lighting/RealLeague_Light2, um pacote dependente /Game/M5VFXVOL2/Particles/Fire/Fire_02 não estava disponível. Confira mais informações detalhadas:
FPackageName: Skipped package /Game/M5VFXVOL2/Particles/Fire/Fire_02 has a valid, mounted, mount point but does not exist either on disk or in iostore. The uncooked file would be expected on disk at 'C:/Auracron/Content/M5VFXVOL2/Particles/Fire/Fire_02'. Perhaps it has been deleted or was not synced?

[2025.09.15-16.59.28:786][685]LoadErrors: Warning: Ao tentar carregar o pacote /Game/Maps/RealLeague/Lighting/RealLeague_Light1, um pacote dependente /Game/M5VFXVOL2/Particles/Fire/Fire_02 não estava disponível. Confira mais informações detalhadas:
FPackageName: Skipped package /Game/M5VFXVOL2/Particles/Fire/Fire_02 has a valid, mounted, mount point but does not exist either on disk or in iostore. The uncooked file would be expected on disk at 'C:/Auracron/Content/M5VFXVOL2/Particles/Fire/Fire_02'. Perhaps it has been deleted or was not synced?

[2025.09.15-16.59.29:342][687]LogSlate: Took 0.000337 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Regular.ttf' (155K)
[2025.09.15-16.59.34:496][728]LogEngine: Error: Requested reflection capture cube size of 1024 with 2 elements results in too large a resource for host machine, limiting reflection capture cube size to 512
[2025.09.15-16.59.37:071][751]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_1
[2025.09.15-17.00.24:208][334]LogDerivedDataCache: C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: Maintenance finished in +00:00:00.000 and deleted 0 files with total size 0 MiB and 0 empty folders. Scanned 0 files in 1 folders with total size 0 MiB.
[2025.09.15-17.00.31:995][415]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_2
[2025.09.15-17.01.34:885][ 24]LogShaderCompilers: Display: ================================================
[2025.09.15-17.01.34:885][ 24]LogShaderCompilers: Display: === FShaderJobCache stats ===
[2025.09.15-17.01.34:886][ 24]LogShaderCompilers: Display: Total job queries 441, among them cache hits 63 (14.29%), DDC hits 313 (70.98%), Duplicates 61 (13.83%)
[2025.09.15-17.01.34:886][ 24]LogShaderCompilers: Display: Tracking 317 distinct input hashes that result in 210 distinct outputs (66.25%)
[2025.09.15-17.01.34:886][ 24]LogShaderCompilers: Display: RAM used: 745,67 KiB of 1,60 GiB budget. Usage: 0.04%
[2025.09.15-17.01.34:886][ 24]LogShaderCompilers: Display: === Shader Compilation stats ===
[2025.09.15-17.01.34:886][ 24]LogShaderCompilers: Display: Shaders Compiled: 4
[2025.09.15-17.01.34:886][ 24]LogShaderCompilers: Display: Jobs assigned 4, completed 4 (100%)
[2025.09.15-17.01.34:886][ 24]LogShaderCompilers: Display: Average time worker was idle: 26.60 s
[2025.09.15-17.01.34:886][ 24]LogShaderCompilers: Display: Time job spent in pending queue: average 0.03 s, longest 0.09 s
[2025.09.15-17.01.34:886][ 24]LogShaderCompilers: Display: Job execution time: average 3.01 s, max 5.25 s
[2025.09.15-17.01.34:886][ 24]LogShaderCompilers: Display: Job life time (pending + execution): average 3.04 s, max 5.34
[2025.09.15-17.01.34:886][ 24]LogShaderCompilers: Display: Shader code size: total 122,246 KiB, numShaders 4, average 30,562 KiB, min 3,543 KiB, max 58,375 KiB
[2025.09.15-17.01.34:886][ 24]LogShaderCompilers: Display: Time at least one job was in flight (either pending or executed): 11.44 s
[2025.09.15-17.01.34:886][ 24]LogShaderCompilers: Display: Mutex wait stall in FShaderJobCache::SubmitJobs:  0.10%
[2025.09.15-17.01.34:886][ 24]LogShaderCompilers: Display: Jobs were issued in 4 batches (only local compilation was used), average 1.00 jobs/batch
[2025.09.15-17.01.34:886][ 24]LogShaderCompilers: Display: Average processing rate: 0.35 jobs/sec
[2025.09.15-17.01.34:886][ 24]LogShaderCompilers: Display: Total thread time: 11,154 s
[2025.09.15-17.01.34:886][ 24]LogShaderCompilers: Display: Total thread preprocess time: 7,375 s
[2025.09.15-17.01.34:886][ 24]LogShaderCompilers: Display: Percentage time preprocessing: 66.12%
[2025.09.15-17.01.34:886][ 24]LogShaderCompilers: Display: Effective parallelization: 0.97 (times faster than compiling all shaders on one thread). Compare with number of workers: 8 - 0.121837
[2025.09.15-17.01.34:886][ 24]LogShaderCompilers: Display: Top 5 most expensive shader types by average time:
[2025.09.15-17.01.34:886][ 24]LogShaderCompilers: Display:      TBasePassPSFPrecomputedVolumetricLightmapLightingPolicy (compiled    1 times, average 4.93 sec, max 4.93 sec, min 4.93 sec)
[2025.09.15-17.01.34:886][ 24]LogShaderCompilers: Display:                                 TBasePassPSFNoLightMapPolicy (compiled    2 times, average 2.85 sec, max 4.96 sec, min 0.74 sec)
[2025.09.15-17.01.34:886][ 24]LogShaderCompilers: Display:                                 TBasePassVSFNoLightMapPolicy (compiled    1 times, average 0.52 sec, max 0.52 sec, min 0.52 sec)
[2025.09.15-17.01.34:886][ 24]LogShaderCompilers: Display:                                      FLandscapeGrassWeightPS (compiled    0 times, average 0.00 sec, max 0.00 sec, min 0.00 sec)
[2025.09.15-17.01.34:886][ 24]LogShaderCompilers: Display:                        FLumenTranslucencyRadianceCacheMarkVS (compiled    0 times, average 0.00 sec, max 0.00 sec, min 0.00 sec)
[2025.09.15-17.01.34:886][ 24]LogShaderCompilers: Display: Top 5 shader types by total compile time:
[2025.09.15-17.01.34:886][ 24]LogShaderCompilers: Display:                                 TBasePassPSFNoLightMapPolicy - 51.13% of total time (compiled    2 times, average 2.85 sec, max 4.96 sec, min 0.74 sec)
[2025.09.15-17.01.34:886][ 24]LogShaderCompilers: Display:      TBasePassPSFPrecomputedVolumetricLightmapLightingPolicy - 44.19% of total time (compiled    1 times, average 4.93 sec, max 4.93 sec, min 4.93 sec)
[2025.09.15-17.01.34:886][ 24]LogShaderCompilers: Display:                                 TBasePassVSFNoLightMapPolicy - 4.68% of total time (compiled    1 times, average 0.52 sec, max 0.52 sec, min 0.52 sec)
[2025.09.15-17.01.34:886][ 24]LogShaderCompilers: Display:                        FLumenTranslucencyRadianceCacheMarkVS - 0.00% of total time (compiled    0 times, average 0.00 sec, max 0.00 sec, min 0.00 sec)
[2025.09.15-17.01.34:886][ 24]LogShaderCompilers: Display:                        FLumenTranslucencyRadianceCacheMarkPS - 0.00% of total time (compiled    0 times, average 0.00 sec, max 0.00 sec, min 0.00 sec)
[2025.09.15-17.01.34:886][ 24]LogShaderCompilers: Display: === Material stats ===
[2025.09.15-17.01.34:886][ 24]LogShaderCompilers: Display: Materials Cooked:        0
[2025.09.15-17.01.34:886][ 24]LogShaderCompilers: Display: Materials Translated:    177
[2025.09.15-17.01.34:886][ 24]LogShaderCompilers: Display: Material Total Translate Time: 0.21 s
[2025.09.15-17.01.34:886][ 24]LogShaderCompilers: Display: Material Translation Only: 0.06 s (29%)
[2025.09.15-17.01.34:886][ 24]LogShaderCompilers: Display: Material DDC Serialization Only: 0.00 s (1%)
[2025.09.15-17.01.34:886][ 24]LogShaderCompilers: Display: Material Cache Hits: 41 (23%)
[2025.09.15-17.01.34:886][ 24]LogShaderCompilers: Display: ================================================
[2025.09.15-17.02.31:814][360]LogAssetTools: Warning: Falha ao importar 'C:\Auracron\Content\Env\RealLeague\Structures\Inhib_Blue.uasset'. Extensão desconhecida 'uasset'.
[2025.09.15-17.32.01:621][526]LogHttp: Warning: 000001A4CD77B600: request failed, libcurl error: 6 (Could not resolve hostname)
[2025.09.15-17.32.01:621][526]LogHttp: Warning: 000001A4CD77B600: libcurl info message cache 0 (Too old connection (1772 seconds idle), disconnect it)
[2025.09.15-17.32.01:621][526]LogHttp: Warning: 000001A4CD77B600: libcurl info message cache 1 (Connection 0 seems to be dead)
[2025.09.15-17.32.01:621][526]LogHttp: Warning: 000001A4CD77B600: libcurl info message cache 2 (shutting down connection #0)
[2025.09.15-17.32.01:621][526]LogHttp: Warning: 000001A4CD77B600: libcurl info message cache 3 (TLSv1.3 (IN), TLS alert, close notify (256):)
[2025.09.15-17.32.01:621][526]LogHttp: Warning: 000001A4CD77B600: libcurl info message cache 4 (Send failure: Connection was aborted)
[2025.09.15-17.32.01:621][526]LogHttp: Warning: 000001A4CD77B600: libcurl info message cache 5 (Send failure: Connection was aborted)
[2025.09.15-17.32.01:621][526]LogHttp: Warning: 000001A4CD77B600: libcurl info message cache 6 (Could not resolve host: datarouter.ol.epicgames.com)
[2025.09.15-17.32.01:621][526]LogHttp: Warning: 000001A4CD77B600: libcurl info message cache 7 (shutting down connection #1)
[2025.09.15-17.32.01:621][526]LogHttp: Warning: 000001A4CD77B600 POST https://datarouter.ol.epicgames.com/datarouter/api/v1/public/data?SessionID=%7B59FAE209-4637-E8F8-B07F-8196EE858B5A%7D&AppID=UEEditor.Rocket.Release&AppVersion=5.6.1-44394996%2B%2B%2BUE5%2BRelease-5.6&UserID=b4f508ed4bd797d4247ed9a748e6dcab%7C6460d6b6163c4887bdafe06ad4082897%7C3ff58bd0-1eef-4049-8d1e-6ac30b330d7e&AppEnvironment=datacollector-binary&UploadType=eteventstream completed with reason 'ConnectionError' after 0.01s
[2025.09.15-17.32.01:621][526]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 2013.286133
[2025.09.15-17.32.01:621][526]LogEOSSDK: Warning: LogHttp: 000001A4C26EE400: invalid HTTP response code received. URL: https://api.epicgames.dev/telemetry/data/datarouter/api/v1/public/data?SessionID=%7BAA94F0FC-4565-B32B-14BB-738C928B6A54%7D&AppID=EOSSDK.PhaseRelease.ReleaseBuild&AppVersion=1.17.0-41373641%20-%20%2B%2BEOSSDK%2BRelease-1.17.0-***********&UserID=&AppEnvironment=Production&UploadType=sdkevents, HTTP code: 0, content length: 0, actual payload size: 0
[2025.09.15-17.32.01:621][526]LogEOSSDK: Warning: LogHttp: 000001A4C26EE400: request failed, libcurl error: 6 (Couldn't resolve host name)
[2025.09.15-17.32.01:621][526]LogEOSSDK: Warning: LogHttp: 000001A4C26EE400: libcurl info message cache 0 (Too old connection (1772 seconds idle), disconnect it)
[2025.09.15-17.32.01:621][526]LogEOSSDK: Warning: LogHttp: 000001A4C26EE400: libcurl info message cache 1 (Connection 2 seems to be dead)
[2025.09.15-17.32.01:621][526]LogEOSSDK: Warning: LogHttp: 000001A4C26EE400: libcurl info message cache 2 (Closing connection)
[2025.09.15-17.32.01:621][526]LogEOSSDK: Warning: LogHttp: 000001A4C26EE400: libcurl info message cache 3 (TLSv1.3 (IN), TLS alert, close notify (256):)
[2025.09.15-17.32.01:621][526]LogEOSSDK: Warning: LogHttp: 000001A4C26EE400: libcurl info message cache 4 (Send failure: Connection was aborted)
[2025.09.15-17.32.01:621][526]LogEOSSDK: Warning: LogHttp: 000001A4C26EE400: libcurl info message cache 5 (Too old connection (1772 seconds idle), disconnect it)
[2025.09.15-17.32.01:621][526]LogEOSSDK: Warning: LogHttp: 000001A4C26EE400: libcurl info message cache 6 (Connection 3 seems to be dead)
[2025.09.15-17.32.01:621][527]LogEOSSDK: Warning: LogHttp: 000001A4C26EE400: libcurl info message cache 7 (Closing connection)
[2025.09.15-17.32.01:621][527]LogEOSSDK: Warning: LogHttp: 000001A4C26EE400: libcurl info message cache 8 (TLSv1.3 (IN), TLS alert, close notify (256):)
[2025.09.15-17.32.01:622][527]LogEOSSDK: Warning: LogHttp: 000001A4C26EE400: libcurl info message cache 9 (Send failure: Connection was aborted)
[2025.09.15-17.32.01:622][527]LogEOSSDK: Warning: LogHttp: 000001A4C26EE400: libcurl info message cache 10 (Hostname in DNS cache was stale, zapped)
[2025.09.15-17.32.01:622][527]LogEOSSDK: Warning: LogHttp: 000001A4C26EE400: libcurl info message cache 11 (Could not resolve host: api.epicgames.dev)
[2025.09.15-17.32.01:622][527]LogEOSSDK: Warning: LogHttp: 000001A4C26EE400: libcurl info message cache 12 (Closing connection)
[2025.09.15-17.32.01:622][527]LogEOSSDK: Warning: LogHttp: Retry exhausted on https://api.epicgames.dev/telemetry/data/datarouter/api/v1/public/data?SessionID=%7BAA94F0FC-4565-B32B-14BB-738C928B6A54%7D&AppID=EOSSDK.PhaseRelease.ReleaseBuild&AppVersion=1.17.0-41373641%20-%20%2B%2BEOSSDK%2BRelease-1.17.0-***********&UserID=&AppEnvironment=Production&UploadType=sdkevents
[2025.09.15-17.32.01:954][527]LogEOSSDK: Warning: LogHttp: 000001A4C26EA200: invalid HTTP response code received. URL: https://api.epicgames.dev/datarouter/api/v1/public/data/clients?AppID=86f32f1151354e7cb39c12f8ab2c22a3&AppVersion=1.17.0-41373641&AppEnvironment=********************************&UploadType=eteventstream&SessionID=B1251CEF4B3DFC2D92681799A2CE73DC, HTTP code: 0, content length: 0, actual payload size: 0
[2025.09.15-17.32.01:954][527]LogEOSSDK: Warning: LogHttp: 000001A4C26EA200: request failed, libcurl error: 6 (Couldn't resolve host name)
[2025.09.15-17.32.01:954][527]LogEOSSDK: Warning: LogHttp: 000001A4C26EA200: libcurl info message cache 0 (Could not resolve host: api.epicgames.dev)
[2025.09.15-17.32.01:954][527]LogEOSSDK: Warning: LogHttp: 000001A4C26EA200: libcurl info message cache 1 (Closing connection)
[2025.09.15-17.32.01:954][527]LogEOSSDK: Warning: LogHttp: Lockout of 1.000000s on https://api.epicgames.dev/datarouter/api/v1/public/data/clients?AppID=86f32f1151354e7cb39c12f8ab2c22a3&AppVersion=1.17.0-41373641&AppEnvironment=********************************&UploadType=eteventstream&SessionID=B1251CEF4B3DFC2D92681799A2CE73DC
[2025.09.15-17.32.02:287][528]LogEOSSDK: Warning: LogHttp: 000001A4C26E9C00: invalid HTTP response code received. URL: https://api.epicgames.dev/sdk/v1/product/86f32f1151354e7cb39c12f8ab2c22a3?platformId=WIN&deploymentId=a652a72ea1664dcab3a467891eea5f30, HTTP code: 0, content length: 0, actual payload size: 0
[2025.09.15-17.32.02:287][528]LogEOSSDK: Warning: LogHttp: 000001A4C26E9C00: request failed, libcurl error: 6 (Couldn't resolve host name)
[2025.09.15-17.32.02:287][528]LogEOSSDK: Warning: LogHttp: 000001A4C26E9C00: libcurl info message cache 0 (Could not resolve host: api.epicgames.dev)
[2025.09.15-17.32.02:287][528]LogEOSSDK: Warning: LogHttp: 000001A4C26E9C00: libcurl info message cache 1 (Closing connection)
[2025.09.15-17.32.02:287][529]LogEOSSDK: Warning: LogHttp: Retry exhausted on https://api.epicgames.dev/sdk/v1/product/86f32f1151354e7cb39c12f8ab2c22a3?platformId=WIN&deploymentId=a652a72ea1664dcab3a467891eea5f30
[2025.09.15-17.32.02:287][529]LogEOSSDK: Warning: LogEOS: Failed to connect to the backend. ServiceName=[SDKConfig], OperationName=[GetProductConfigRoute], Url=[<Redacted>]
[2025.09.15-17.32.02:621][529]LogEOSSDK: Warning: LogEOS: SDK Config Product Update Request Failed, Result Code: EOS_NoConnection, Retrying after 2.722068 seconds
[2025.09.15-17.32.02:955][530]LogUdpMessaging: Warning: UDP messaging encountered an error. Auto repair routine started for reinitialization
[2025.09.15-17.32.02:957][530]LogEOSSDK: Warning: LogHttp: Retry 1 on https://api.epicgames.dev/datarouter/api/v1/public/data/clients?AppID=86f32f1151354e7cb39c12f8ab2c22a3&AppVersion=1.17.0-41373641&AppEnvironment=********************************&UploadType=eteventstream&SessionID=B1251CEF4B3DFC2D92681799A2CE73DC
[2025.09.15-17.32.03:289][531]LogEOSSDK: Warning: LogHttp: 000001A4C26EA200: invalid HTTP response code received. URL: https://api.epicgames.dev/datarouter/api/v1/public/data/clients?AppID=86f32f1151354e7cb39c12f8ab2c22a3&AppVersion=1.17.0-41373641&AppEnvironment=********************************&UploadType=eteventstream&SessionID=B1251CEF4B3DFC2D92681799A2CE73DC, HTTP code: 0, content length: 0, actual payload size: 0
[2025.09.15-17.32.03:289][532]LogEOSSDK: Warning: LogHttp: 000001A4C26EA200: request failed, libcurl error: 6 (Couldn't resolve host name)
[2025.09.15-17.32.03:289][532]LogEOSSDK: Warning: LogHttp: 000001A4C26EA200: libcurl info message cache 2 (Could not resolve host: api.epicgames.dev)
[2025.09.15-17.32.03:289][532]LogEOSSDK: Warning: LogHttp: 000001A4C26EA200: libcurl info message cache 3 (Closing connection)
[2025.09.15-17.32.03:289][532]LogEOSSDK: Warning: LogHttp: Lockout of 1.000000s on https://api.epicgames.dev/datarouter/api/v1/public/data/clients?AppID=86f32f1151354e7cb39c12f8ab2c22a3&AppVersion=1.17.0-41373641&AppEnvironment=********************************&UploadType=eteventstream&SessionID=B1251CEF4B3DFC2D92681799A2CE73DC
[2025.09.15-17.32.04:289][534]LogEOSSDK: Warning: LogHttp: Retry 2 on https://api.epicgames.dev/datarouter/api/v1/public/data/clients?AppID=86f32f1151354e7cb39c12f8ab2c22a3&AppVersion=1.17.0-41373641&AppEnvironment=********************************&UploadType=eteventstream&SessionID=B1251CEF4B3DFC2D92681799A2CE73DC
[2025.09.15-17.32.05:112][535]LogUdpMessaging: Display: Unicast socket bound to '0.0.0.0:62038'.
[2025.09.15-17.32.05:117][535]LogUdpMessaging: Display: Added local interface '************' to multicast group '*********:6666'
[2025.09.15-17.32.05:126][535]LogUdpMessaging: Display: Using asynchronous task graph for message deserialization.
[2025.09.15-17.32.05:134][536]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 2016.799194
[2025.09.15-17.32.05:134][536]LogEOSSDK: Warning: LogHttp: Success on https://api.epicgames.dev/datarouter/api/v1/public/data/clients?AppID=86f32f1151354e7cb39c12f8ab2c22a3&AppVersion=1.17.0-41373641&AppEnvironment=********************************&UploadType=eteventstream&SessionID=B1251CEF4B3DFC2D92681799A2CE73DC
[2025.09.15-17.32.06:136][539]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.09.15-17.32.06:136][539]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 2017.466431, Update Interval: 358.668793
[2025.09.15-17.32.31:592][ 28]LogWebBrowser: Loaded CEF3 version 90.6.7.2358 from C:/Program Files/Epic Games/UE_5.6/Engine/Binaries/ThirdParty/CEF3/Win64/
[2025.09.15-17.32.31:597][ 28]LogCEFBrowser: CEF GPU acceleration enabled
[2025.09.15-17.38.09:455][809]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 2381.119873
[2025.09.15-17.38.09:934][812]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.09.15-17.38.09:934][812]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 2381.477295, Update Interval: 326.668304
[2025.09.15-17.38.39:530][ 41]LogSlate: Window 'Bridge' being destroyed
[2025.09.15-17.38.39:962][ 41]LogSlate: Window 'Bridge' being destroyed
[2025.09.15-17.38.39:962][ 41]LogWebBrowser: Closing browser during destruction, this may cause a later crash.
[2025.09.15-17.38.39:962][ 41]LogWebBrowser: Deleting browser for Url=file:///C:/Program%20Files/Epic%20Games/UE_5.6/Engine/Plugins/Bridge/ThirdParty/megascans/index.html#/megascans/?search=base.
[2025.09.15-17.38.48:068][402]LogFab: Display: Logging in using persist
[2025.09.15-17.38.48:068][402]LogEOSSDK: Warning: LogEOSAuth: No existing persistent auth credentials were found for automatic login.
[2025.09.15-17.38.48:098][402]LogFab: Error: Login failed - error code: EOS_InvalidAuth
[2025.09.15-17.38.48:098][402]LogFab: Display: Logging in using exchange code
[2025.09.15-17.38.48:098][402]LogFab: Display: Reading exchange code from commandline
[2025.09.15-17.38.48:098][402]LogEOSSDK: Error: LogEOSAuth: Invalid parameter EOS_Auth_Credentials.Token reason: must not be null or empty
[2025.09.15-17.38.48:128][403]LogFab: Error: Login failed - error code: EOS_InvalidParameters
[2025.09.15-17.38.53:074][436]LogEOSSDK: Error: LogEOS: EOS_Auth_CopyUserAuthToken - One or more parameters are null
[2025.09.15-17.38.53:074][436]LogFab: Error: User auth token is invalid - unable to get auth token
[2025.09.15-18.12.44:801][483]LogHttp: Warning: 000001A4CD771C00: request failed, libcurl error: 6 (Could not resolve hostname)
[2025.09.15-18.12.44:801][483]LogHttp: Warning: 000001A4CD771C00: libcurl info message cache 0 (Too old connection (2049 seconds idle), disconnect it)
[2025.09.15-18.12.44:801][483]LogHttp: Warning: 000001A4CD771C00: libcurl info message cache 1 (Connection 2 seems to be dead)
[2025.09.15-18.12.44:801][483]LogHttp: Warning: 000001A4CD771C00: libcurl info message cache 2 (shutting down connection #2)
[2025.09.15-18.12.44:801][483]LogHttp: Warning: 000001A4CD771C00: libcurl info message cache 3 (Recv failure: Connection was reset)
[2025.09.15-18.12.44:801][483]LogHttp: Warning: 000001A4CD771C00: libcurl info message cache 4 (Send failure: Connection was reset)
[2025.09.15-18.12.44:801][483]LogHttp: Warning: 000001A4CD771C00: libcurl info message cache 5 (Recv failure: Connection was reset)
[2025.09.15-18.12.44:801][483]LogHttp: Warning: 000001A4CD771C00: libcurl info message cache 6 (Could not resolve host: datarouter.ol.epicgames.com)
[2025.09.15-18.12.44:801][483]LogHttp: Warning: 000001A4CD771C00: libcurl info message cache 7 (shutting down connection #3)
[2025.09.15-18.12.44:801][483]LogHttp: Warning: 000001A4CD771C00 POST https://datarouter.ol.epicgames.com/datarouter/api/v1/public/data?SessionID=%7B59FAE209-4637-E8F8-B07F-8196EE858B5A%7D&AppID=UEEditor.Rocket.Release&AppVersion=5.6.1-44394996%2B%2B%2BUE5%2BRelease-5.6&UserID=b4f508ed4bd797d4247ed9a748e6dcab%7C6460d6b6163c4887bdafe06ad4082897%7C3ff58bd0-1eef-4049-8d1e-6ac30b330d7e&AppEnvironment=datacollector-binary&UploadType=eteventstream completed with reason 'ConnectionError' after 0.05s
[2025.09.15-18.12.44:801][483]LogEOSSDK: Warning: LogHttp: 000001A44BECAE00: invalid HTTP response code received. URL: https://api.epicgames.dev/telemetry/data/datarouter/api/v1/public/data?SessionID=%7BF1962110-4E35-5FC4-9BE6-91ADA7E7FC95%7D&AppID=EOSSDK.PhaseRelease.ReleaseBuild&AppVersion=1.17.0-41373641%20-%20%2B%2BEOSSDK%2BRelease-1.17.0-***********&UserID=&AppEnvironment=Production&UploadType=sdkevents, HTTP code: 0, content length: 0, actual payload size: 0
[2025.09.15-18.12.44:801][483]LogEOSSDK: Warning: LogHttp: 000001A44BECAE00: request failed, libcurl error: 6 (Couldn't resolve host name)
[2025.09.15-18.12.44:801][483]LogEOSSDK: Warning: LogHttp: 000001A44BECAE00: libcurl info message cache 0 (Too old connection (2035 seconds idle), disconnect it)
[2025.09.15-18.12.44:801][483]LogEOSSDK: Warning: LogHttp: 000001A44BECAE00: libcurl info message cache 1 (Connection 9 seems to be dead)
[2025.09.15-18.12.44:801][483]LogEOSSDK: Warning: LogHttp: 000001A44BECAE00: libcurl info message cache 2 (Closing connection)
[2025.09.15-18.12.44:801][483]LogEOSSDK: Warning: LogHttp: 000001A44BECAE00: libcurl info message cache 3 (Recv failure: Connection was reset)
[2025.09.15-18.12.44:801][483]LogEOSSDK: Warning: LogHttp: 000001A44BECAE00: libcurl info message cache 4 (Send failure: Connection was reset)
[2025.09.15-18.12.44:801][483]LogEOSSDK: Warning: LogHttp: 000001A44BECAE00: libcurl info message cache 5 (Recv failure: Connection was reset)
[2025.09.15-18.12.44:801][483]LogEOSSDK: Warning: LogHttp: 000001A44BECAE00: libcurl info message cache 6 (Could not resolve host: api.epicgames.dev)
[2025.09.15-18.12.44:801][483]LogEOSSDK: Warning: LogHttp: 000001A44BECAE00: libcurl info message cache 7 (Closing connection)
[2025.09.15-18.12.44:801][483]LogEOSSDK: Warning: LogHttp: Retry exhausted on https://api.epicgames.dev/telemetry/data/datarouter/api/v1/public/data?SessionID=%7BF1962110-4E35-5FC4-9BE6-91ADA7E7FC95%7D&AppID=EOSSDK.PhaseRelease.ReleaseBuild&AppVersion=1.17.0-41373641%20-%20%2B%2BEOSSDK%2BRelease-1.17.0-***********&UserID=&AppEnvironment=Production&UploadType=sdkevents
[2025.09.15-18.12.45:135][484]LogEOSSDK: Warning: LogHttp: 000001A44BEC4E00: invalid HTTP response code received. URL: https://api.epicgames.dev/telemetry/data/datarouter/api/v1/public/data?SessionID=%7BAA94F0FC-4565-B32B-14BB-738C928B6A54%7D&AppID=EOSSDK.PhaseRelease.ReleaseBuild&AppVersion=1.17.0-41373641%20-%20%2B%2BEOSSDK%2BRelease-1.17.0-***********&UserID=&AppEnvironment=Production&UploadType=sdkevents, HTTP code: 0, content length: 0, actual payload size: 0
[2025.09.15-18.12.45:135][484]LogEOSSDK: Warning: LogHttp: 000001A44BEC4E00: request failed, libcurl error: 6 (Couldn't resolve host name)
[2025.09.15-18.12.45:135][484]LogEOSSDK: Warning: LogHttp: 000001A44BEC4E00: libcurl info message cache 0 (Found bundle for host: 0x1a4ad080240 [serially])
[2025.09.15-18.12.45:135][484]LogEOSSDK: Warning: LogHttp: 000001A44BEC4E00: libcurl info message cache 1 (Could not resolve host: api.epicgames.dev)
[2025.09.15-18.12.45:135][484]LogEOSSDK: Warning: LogHttp: 000001A44BEC4E00: libcurl info message cache 2 (Closing connection)
[2025.09.15-18.12.45:135][484]LogEOSSDK: Warning: LogHttp: 000001A44BEC1200: invalid HTTP response code received. URL: https://api.epicgames.dev/auth/v1/oauth/token, HTTP code: 0, content length: 0, actual payload size: 0
[2025.09.15-18.12.45:135][484]LogEOSSDK: Warning: LogHttp: 000001A44BEC1200: request failed, libcurl error: 6 (Couldn't resolve host name)
[2025.09.15-18.12.45:135][484]LogEOSSDK: Warning: LogHttp: 000001A44BEC1200: libcurl info message cache 0 (Could not resolve host: api.epicgames.dev)
[2025.09.15-18.12.45:135][484]LogEOSSDK: Warning: LogHttp: 000001A44BEC1200: libcurl info message cache 1 (Closing connection)
[2025.09.15-18.12.45:135][484]LogEOSSDK: Warning: LogHttp: 000001A44BECEA00: invalid HTTP response code received. URL: https://api.epicgames.dev/datarouter/api/v1/public/data/clients?AppID=86f32f1151354e7cb39c12f8ab2c22a3&AppVersion=1.17.0-41373641&AppEnvironment=********************************&UploadType=eteventstream&SessionID=B1251CEF4B3DFC2D92681799A2CE73DC, HTTP code: 0, content length: 0, actual payload size: 0
[2025.09.15-18.12.45:135][485]LogEOSSDK: Warning: LogHttp: 000001A44BECEA00: request failed, libcurl error: 6 (Couldn't resolve host name)
[2025.09.15-18.12.45:135][485]LogEOSSDK: Warning: LogHttp: 000001A44BECEA00: libcurl info message cache 0 (Found bundle for host: 0x1a4ad080240 [serially])
[2025.09.15-18.12.45:135][485]LogEOSSDK: Warning: LogHttp: 000001A44BECEA00: libcurl info message cache 1 (Could not resolve host: api.epicgames.dev)
[2025.09.15-18.12.45:135][485]LogEOSSDK: Warning: LogHttp: 000001A44BECEA00: libcurl info message cache 2 (Closing connection)
[2025.09.15-18.12.45:135][485]LogEOSSDK: Warning: LogHttp: Retry exhausted on https://api.epicgames.dev/telemetry/data/datarouter/api/v1/public/data?SessionID=%7BAA94F0FC-4565-B32B-14BB-738C928B6A54%7D&AppID=EOSSDK.PhaseRelease.ReleaseBuild&AppVersion=1.17.0-41373641%20-%20%2B%2BEOSSDK%2BRelease-1.17.0-***********&UserID=&AppEnvironment=Production&UploadType=sdkevents
[2025.09.15-18.12.45:135][485]LogEOSSDK: Warning: LogHttp: Lockout of 1.000000s on https://api.epicgames.dev/auth/v1/oauth/token
[2025.09.15-18.12.45:135][485]LogEOSSDK: Warning: LogHttp: Lockout of 1.000000s on https://api.epicgames.dev/datarouter/api/v1/public/data/clients?AppID=86f32f1151354e7cb39c12f8ab2c22a3&AppVersion=1.17.0-41373641&AppEnvironment=********************************&UploadType=eteventstream&SessionID=B1251CEF4B3DFC2D92681799A2CE73DC
[2025.09.15-18.12.45:469][486]LogUdpMessaging: Error: Socket error detected when communicating with 127.0.0.1:57763. Banning communication to that endpoint.
[2025.09.15-18.12.45:790][486]LogUdpMessaging: Warning: UDP messaging encountered an error. Auto repair routine started for reinitialization
[2025.09.15-18.12.46:467][488]LogEOSSDK: Warning: LogHttp: Retry 1 on https://api.epicgames.dev/auth/v1/oauth/token
[2025.09.15-18.12.46:467][488]LogEOSSDK: Warning: LogHttp: Retry 1 on https://api.epicgames.dev/datarouter/api/v1/public/data/clients?AppID=86f32f1151354e7cb39c12f8ab2c22a3&AppVersion=1.17.0-41373641&AppEnvironment=********************************&UploadType=eteventstream&SessionID=B1251CEF4B3DFC2D92681799A2CE73DC
[2025.09.15-18.12.46:800][489]LogEOSSDK: Warning: LogHttp: 000001A44BECEA00: invalid HTTP response code received. URL: https://api.epicgames.dev/datarouter/api/v1/public/data/clients?AppID=86f32f1151354e7cb39c12f8ab2c22a3&AppVersion=1.17.0-41373641&AppEnvironment=********************************&UploadType=eteventstream&SessionID=B1251CEF4B3DFC2D92681799A2CE73DC, HTTP code: 0, content length: 0, actual payload size: 0
[2025.09.15-18.12.46:801][489]LogEOSSDK: Warning: LogHttp: 000001A44BECEA00: request failed, libcurl error: 6 (Couldn't resolve host name)
[2025.09.15-18.12.46:801][489]LogEOSSDK: Warning: LogHttp: 000001A44BECEA00: libcurl info message cache 3 (Found bundle for host: 0x1a4ad080540 [serially])
[2025.09.15-18.12.46:801][489]LogEOSSDK: Warning: LogHttp: 000001A44BECEA00: libcurl info message cache 4 (Could not resolve host: api.epicgames.dev)
[2025.09.15-18.12.46:801][489]LogEOSSDK: Warning: LogHttp: 000001A44BECEA00: libcurl info message cache 5 (Closing connection)
[2025.09.15-18.12.46:801][489]LogEOSSDK: Warning: LogHttp: 000001A44BEC1200: invalid HTTP response code received. URL: https://api.epicgames.dev/auth/v1/oauth/token, HTTP code: 0, content length: 0, actual payload size: 0
[2025.09.15-18.12.46:801][489]LogEOSSDK: Warning: LogHttp: 000001A44BEC1200: request failed, libcurl error: 6 (Couldn't resolve host name)
[2025.09.15-18.12.46:801][489]LogEOSSDK: Warning: LogHttp: 000001A44BEC1200: libcurl info message cache 2 (Could not resolve host: api.epicgames.dev)
[2025.09.15-18.12.46:801][489]LogEOSSDK: Warning: LogHttp: 000001A44BEC1200: libcurl info message cache 3 (Closing connection)
[2025.09.15-18.12.46:801][489]LogEOSSDK: Warning: LogHttp: Lockout of 1.000000s on https://api.epicgames.dev/auth/v1/oauth/token
[2025.09.15-18.12.46:801][490]LogEOSSDK: Warning: LogHttp: Lockout of 1.000000s on https://api.epicgames.dev/datarouter/api/v1/public/data/clients?AppID=86f32f1151354e7cb39c12f8ab2c22a3&AppVersion=1.17.0-41373641&AppEnvironment=********************************&UploadType=eteventstream&SessionID=B1251CEF4B3DFC2D92681799A2CE73DC
[2025.09.15-18.12.48:395][491]LogUdpMessaging: Display: Unicast socket bound to '0.0.0.0:55694'.
[2025.09.15-18.12.48:399][491]LogUdpMessaging: Display: Added local interface '************' to multicast group '*********:6666'
[2025.09.15-18.12.48:406][491]LogUdpMessaging: Display: Using asynchronous task graph for message deserialization.
[2025.09.15-18.12.48:421][492]LogEOSSDK: Warning: LogHttp: Retry 2 on https://api.epicgames.dev/auth/v1/oauth/token
[2025.09.15-18.12.48:421][492]LogEOSSDK: Warning: LogHttp: Retry 2 on https://api.epicgames.dev/datarouter/api/v1/public/data/clients?AppID=86f32f1151354e7cb39c12f8ab2c22a3&AppVersion=1.17.0-41373641&AppEnvironment=********************************&UploadType=eteventstream&SessionID=B1251CEF4B3DFC2D92681799A2CE73DC
[2025.09.15-18.12.48:752][493]LogEOSSDK: Warning: LogHttp: 000001A44BEC1200: invalid HTTP response code received. URL: https://api.epicgames.dev/auth/v1/oauth/token, HTTP code: 0, content length: 0, actual payload size: 0
[2025.09.15-18.12.48:752][493]LogEOSSDK: Warning: LogHttp: 000001A44BEC1200: request failed, libcurl error: 6 (Couldn't resolve host name)
[2025.09.15-18.12.48:752][493]LogEOSSDK: Warning: LogHttp: 000001A44BEC1200: libcurl info message cache 4 (Could not resolve host: api.epicgames.dev)
[2025.09.15-18.12.48:752][493]LogEOSSDK: Warning: LogHttp: 000001A44BEC1200: libcurl info message cache 5 (Closing connection)
[2025.09.15-18.12.48:752][493]LogEOSSDK: Warning: LogHttp: 000001A44BECEA00: invalid HTTP response code received. URL: https://api.epicgames.dev/datarouter/api/v1/public/data/clients?AppID=86f32f1151354e7cb39c12f8ab2c22a3&AppVersion=1.17.0-41373641&AppEnvironment=********************************&UploadType=eteventstream&SessionID=B1251CEF4B3DFC2D92681799A2CE73DC, HTTP code: 0, content length: 0, actual payload size: 0
[2025.09.15-18.12.48:752][493]LogEOSSDK: Warning: LogHttp: 000001A44BECEA00: request failed, libcurl error: 6 (Couldn't resolve host name)
[2025.09.15-18.12.48:752][493]LogEOSSDK: Warning: LogHttp: 000001A44BECEA00: libcurl info message cache 6 (Found bundle for host: 0x1a4ad080120 [serially])
[2025.09.15-18.12.48:752][493]LogEOSSDK: Warning: LogHttp: 000001A44BECEA00: libcurl info message cache 7 (Could not resolve host: api.epicgames.dev)
[2025.09.15-18.12.48:752][493]LogEOSSDK: Warning: LogHttp: 000001A44BECEA00: libcurl info message cache 8 (Closing connection)
[2025.09.15-18.12.48:752][493]LogEOSSDK: Warning: LogHttp: Lockout of 1.000000s on https://api.epicgames.dev/auth/v1/oauth/token
[2025.09.15-18.12.48:752][493]LogEOSSDK: Warning: LogHttp: Retry exhausted on https://api.epicgames.dev/datarouter/api/v1/public/data/clients?AppID=86f32f1151354e7cb39c12f8ab2c22a3&AppVersion=1.17.0-41373641&AppEnvironment=********************************&UploadType=eteventstream&SessionID=B1251CEF4B3DFC2D92681799A2CE73DC
[2025.09.15-18.12.48:753][494]LogEOSSDK: Warning: LogEOS: Failed to connect to the backend. ServiceName=[Metrics], OperationName=[SendBackendEvent], Url=[<Redacted>]
[2025.09.15-19.12.35:361][495]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 8047.026367
[2025.09.15-19.12.35:361][495]LogEOSSDK: Warning: LogHttp: Retry 3 on https://api.epicgames.dev/auth/v1/oauth/token
[2025.09.15-19.12.35:679][496]LogUdpMessaging: Error: Socket error detected when communicating with 127.0.0.1:57763. Banning communication to that endpoint.
[2025.09.15-19.12.35:679][496]LogUdpMessaging: Warning: UDP messaging encountered an error. Auto repair routine started for reinitialization
[2025.09.15-19.12.35:861][496]LogHttp: Warning: 000001A4CD779300: request failed, libcurl error: 6 (Could not resolve hostname)
[2025.09.15-19.12.35:861][496]LogHttp: Warning: 000001A4CD779300: libcurl info message cache 0 (Could not resolve host: datarouter.ol.epicgames.com)
[2025.09.15-19.12.35:861][496]LogHttp: Warning: 000001A4CD779300: libcurl info message cache 1 (shutting down connection #4)
[2025.09.15-19.12.35:861][496]LogHttp: Warning: 000001A4CD779300 POST https://datarouter.ol.epicgames.com/datarouter/api/v1/public/data?SessionID=%7B59FAE209-4637-E8F8-B07F-8196EE858B5A%7D&AppID=UEEditor.Rocket.Release&AppVersion=5.6.1-44394996%2B%2B%2BUE5%2BRelease-5.6&UserID=b4f508ed4bd797d4247ed9a748e6dcab%7C6460d6b6163c4887bdafe06ad4082897%7C3ff58bd0-1eef-4049-8d1e-6ac30b330d7e&AppEnvironment=datacollector-binary&UploadType=eteventstream completed with reason 'ConnectionError' after 0.04s
[2025.09.15-19.12.35:862][496]LogEOSSDK: Warning: LogHttp: 000001A44BECF600: invalid HTTP response code received. URL: https://api.epicgames.dev/telemetry/data/datarouter/api/v1/public/data?SessionID=%7BAA94F0FC-4565-B32B-14BB-738C928B6A54%7D&AppID=EOSSDK.PhaseRelease.ReleaseBuild&AppVersion=1.17.0-41373641%20-%20%2B%2BEOSSDK%2BRelease-1.17.0-***********&UserID=&AppEnvironment=Production&UploadType=sdkevents, HTTP code: 0, content length: 0, actual payload size: 0
[2025.09.15-19.12.35:862][496]LogEOSSDK: Warning: LogHttp: 000001A44BECF600: request failed, libcurl error: 6 (Couldn't resolve host name)
[2025.09.15-19.12.35:862][496]LogEOSSDK: Warning: LogHttp: 000001A44BECF600: libcurl info message cache 0 (Could not resolve host: api.epicgames.dev)
[2025.09.15-19.12.35:862][496]LogEOSSDK: Warning: LogHttp: 000001A44BECF600: libcurl info message cache 1 (Closing connection)
[2025.09.15-19.12.35:862][496]LogEOSSDK: Warning: LogHttp: 000001A44BEC1200: invalid HTTP response code received. URL: https://api.epicgames.dev/auth/v1/oauth/token, HTTP code: 0, content length: 0, actual payload size: 0
[2025.09.15-19.12.35:862][496]LogEOSSDK: Warning: LogHttp: 000001A44BEC1200: request failed, libcurl error: 6 (Couldn't resolve host name)
[2025.09.15-19.12.35:862][496]LogEOSSDK: Warning: LogHttp: 000001A44BEC1200: libcurl info message cache 6 (Found bundle for host: 0x1a4ad080750 [serially])
[2025.09.15-19.12.35:862][496]LogEOSSDK: Warning: LogHttp: 000001A44BEC1200: libcurl info message cache 7 (Could not resolve host: api.epicgames.dev)
[2025.09.15-19.12.35:862][496]LogEOSSDK: Warning: LogHttp: 000001A44BEC1200: libcurl info message cache 8 (Closing connection)
[2025.09.15-19.12.35:862][496]LogEOSSDK: Warning: LogHttp: Retry exhausted on https://api.epicgames.dev/telemetry/data/datarouter/api/v1/public/data?SessionID=%7BAA94F0FC-4565-B32B-14BB-738C928B6A54%7D&AppID=EOSSDK.PhaseRelease.ReleaseBuild&AppVersion=1.17.0-41373641%20-%20%2B%2BEOSSDK%2BRelease-1.17.0-***********&UserID=&AppEnvironment=Production&UploadType=sdkevents
[2025.09.15-19.12.35:862][496]LogEOSSDK: Warning: LogHttp: Lockout of 1.000000s on https://api.epicgames.dev/auth/v1/oauth/token
[2025.09.15-19.12.37:023][501]LogEOSSDK: Warning: LogHttp: Retry 4 on https://api.epicgames.dev/auth/v1/oauth/token
[2025.09.15-19.12.37:358][501]LogEOSSDK: Warning: LogHttp: Success on https://api.epicgames.dev/auth/v1/oauth/token
[2025.09.15-19.12.38:456][501]LogUdpMessaging: Display: Unicast socket bound to '0.0.0.0:57660'.
[2025.09.15-19.12.38:461][501]LogUdpMessaging: Display: Added local interface '************' to multicast group '*********:6666'
[2025.09.15-19.12.38:474][501]LogUdpMessaging: Display: Using asynchronous task graph for message deserialization.
[2025.09.15-19.12.39:494][505]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.09.15-19.12.39:494][505]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 8050.826660, Update Interval: 311.316254
[2025.09.15-19.13.17:169][819]LogFab: Display: Asset Type = unreal-engine
[2025.09.15-19.13.17:169][819]LogFab: Display: Is Quixel = 0
[2025.09.15-19.13.17:170][819]LogFab: Display: Base Url https://download.epicgames.com/Builds/Rocket/Automated/ParagonProps/CloudDir,https://epicgames-download1.akamaized.net/Builds/Rocket/Automated/ParagonProps/CloudDir,https://fastly-download.epicgames.com/Builds/Rocket/Automated/ParagonProps/CloudDir,https://cloudflare.epicgamescdn.com/Builds/Rocket/Automated/ParagonProps/CloudDir
[2025.09.15-19.13.28:364][907]LogSlate: Window 'Fab' being destroyed
[2025.09.15-19.13.28:614][907]LogSlate: Window 'Fab' being destroyed
[2025.09.15-19.13.29:865][920]LogWebBrowser: Closing browser during destruction, this may cause a later crash.
[2025.09.15-19.13.29:865][920]LogWebBrowser: Deleting browser for Url=https://www.fab.com/plugins/ue5/listings/6f401fb5-88b5-41b4-bf1b-62321414e1f0.
[2025.09.15-19.15.13:424][922]LogFab: Display: Import Cancelled
[2025.09.15-19.15.35:419][102]LogShaderCompilers: Display: ================================================
[2025.09.15-19.15.35:420][102]LogShaderCompilers: Display: === FShaderJobCache stats ===
[2025.09.15-19.15.35:420][102]LogShaderCompilers: Display: Total job queries 455, among them cache hits 70 (15.38%), DDC hits 318 (69.89%), Duplicates 61 (13.41%)
[2025.09.15-19.15.35:420][102]LogShaderCompilers: Display: Tracking 324 distinct input hashes that result in 217 distinct outputs (66.98%)
[2025.09.15-19.15.35:420][102]LogShaderCompilers: Display: RAM used: 767,88 KiB of 1,60 GiB budget. Usage: 0.05%
[2025.09.15-19.15.35:420][102]LogShaderCompilers: Display: === Shader Compilation stats ===
[2025.09.15-19.15.35:420][102]LogShaderCompilers: Display: Shaders Compiled: 6
[2025.09.15-19.15.35:420][102]LogShaderCompilers: Display: Jobs assigned 6, completed 6 (100%)
[2025.09.15-19.15.35:420][102]LogShaderCompilers: Display: Average time worker was idle: 2013.17 s
[2025.09.15-19.15.35:420][102]LogShaderCompilers: Display: Time job spent in pending queue: average 0.05 s, longest 0.10 s
[2025.09.15-19.15.35:420][102]LogShaderCompilers: Display: Job execution time: average 2.38 s, max 5.25 s
[2025.09.15-19.15.35:420][102]LogShaderCompilers: Display: Job life time (pending + execution): average 2.43 s, max 5.34
[2025.09.15-19.15.35:420][102]LogShaderCompilers: Display: Shader code size: total 139,273 KiB, numShaders 6, average 23,212 KiB, min 3,543 KiB, max 58,375 KiB
[2025.09.15-19.15.35:420][102]LogShaderCompilers: Display: Time at least one job was in flight (either pending or executed): 13.86 s
[2025.09.15-19.15.35:420][102]LogShaderCompilers: Display: Mutex wait stall in FShaderJobCache::SubmitJobs:  0.10%
[2025.09.15-19.15.35:420][102]LogShaderCompilers: Display: Jobs were issued in 6 batches (only local compilation was used), average 1.00 jobs/batch
[2025.09.15-19.15.35:420][102]LogShaderCompilers: Display: Average processing rate: 0.43 jobs/sec
[2025.09.15-19.15.35:420][102]LogShaderCompilers: Display: Total thread time: 12,972 s
[2025.09.15-19.15.35:420][102]LogShaderCompilers: Display: Total thread preprocess time: 7,762 s
[2025.09.15-19.15.35:420][102]LogShaderCompilers: Display: Percentage time preprocessing: 59.83%
[2025.09.15-19.15.35:420][102]LogShaderCompilers: Display: Effective parallelization: 0.94 (times faster than compiling all shaders on one thread). Compare with number of workers: 8 - 0.116974
[2025.09.15-19.15.35:420][102]LogShaderCompilers: Display: Top 5 most expensive shader types by average time:
[2025.09.15-19.15.35:420][102]LogShaderCompilers: Display:      TBasePassPSFPrecomputedVolumetricLightmapLightingPolicy (compiled    1 times, average 4.93 sec, max 4.93 sec, min 4.93 sec)
[2025.09.15-19.15.35:420][102]LogShaderCompilers: Display:                                 TBasePassPSFNoLightMapPolicy (compiled    4 times, average 1.88 sec, max 4.96 sec, min 0.74 sec)
[2025.09.15-19.15.35:421][102]LogShaderCompilers: Display:                                 TBasePassVSFNoLightMapPolicy (compiled    1 times, average 0.52 sec, max 0.52 sec, min 0.52 sec)
[2025.09.15-19.15.35:421][102]LogShaderCompilers: Display:                                      FLandscapeGrassWeightPS (compiled    0 times, average 0.00 sec, max 0.00 sec, min 0.00 sec)
[2025.09.15-19.15.35:421][102]LogShaderCompilers: Display:                        FLumenTranslucencyRadianceCacheMarkVS (compiled    0 times, average 0.00 sec, max 0.00 sec, min 0.00 sec)
[2025.09.15-19.15.35:421][102]LogShaderCompilers: Display: Top 5 shader types by total compile time:
[2025.09.15-19.15.35:421][102]LogShaderCompilers: Display:                                 TBasePassPSFNoLightMapPolicy - 57.98% of total time (compiled    4 times, average 1.88 sec, max 4.96 sec, min 0.74 sec)
[2025.09.15-19.15.35:421][102]LogShaderCompilers: Display:      TBasePassPSFPrecomputedVolumetricLightmapLightingPolicy - 38.00% of total time (compiled    1 times, average 4.93 sec, max 4.93 sec, min 4.93 sec)
[2025.09.15-19.15.35:421][102]LogShaderCompilers: Display:                                 TBasePassVSFNoLightMapPolicy - 4.02% of total time (compiled    1 times, average 0.52 sec, max 0.52 sec, min 0.52 sec)
[2025.09.15-19.15.35:421][102]LogShaderCompilers: Display:                        FLumenTranslucencyRadianceCacheMarkVS - 0.00% of total time (compiled    0 times, average 0.00 sec, max 0.00 sec, min 0.00 sec)
[2025.09.15-19.15.35:421][102]LogShaderCompilers: Display:                        FLumenTranslucencyRadianceCacheMarkPS - 0.00% of total time (compiled    0 times, average 0.00 sec, max 0.00 sec, min 0.00 sec)
[2025.09.15-19.15.35:421][102]LogShaderCompilers: Display: === Material stats ===
[2025.09.15-19.15.35:421][102]LogShaderCompilers: Display: Materials Cooked:        0
[2025.09.15-19.15.35:421][102]LogShaderCompilers: Display: Materials Translated:    177
[2025.09.15-19.15.35:421][102]LogShaderCompilers: Display: Material Total Translate Time: 0.21 s
[2025.09.15-19.15.35:421][102]LogShaderCompilers: Display: Material Translation Only: 0.06 s (29%)
[2025.09.15-19.15.35:421][102]LogShaderCompilers: Display: Material DDC Serialization Only: 0.00 s (1%)
[2025.09.15-19.15.35:421][102]LogShaderCompilers: Display: Material Cache Hits: 41 (23%)
[2025.09.15-19.15.35:421][102]LogShaderCompilers: Display: ================================================
[2025.09.15-19.18.17:960][799]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 8389.616211
[2025.09.15-19.18.18:316][806]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.09.15-19.18.18:316][806]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 8389.890625, Update Interval: 350.873749
[2025.09.15-19.19.28:962][450]LogDebuggerCommands: Repeating last play command: Janela de visualização selecionada
[2025.09.15-19.19.28:997][450]LogPlayLevel: PlayLevel: No blueprints needed recompiling
[2025.09.15-19.19.28:997][450]BlueprintLog: Warning: Falha ao compilar blueprint:  FirstPersonHUD
[2025.09.15-19.19.30:813][450]LogSlate: Window 'Erros de compilação de blueprint' being destroyed
[2025.09.15-19.19.30:841][450]LogOnline: OSS: Created online subsystem instance for: NULL
[2025.09.15-19.19.30:842][450]LogOnline: OSS: TryLoadSubsystemAndSetDefault: Loaded subsystem for type [NULL]
[2025.09.15-19.19.30:844][450]LogPlayLevel: Creating play world package: /Game/Maps/RealLeague/UEDPIE_0_RealLeague
[2025.09.15-19.19.31:305][450]LogPlayLevel: PIE: StaticDuplicateObject took: (0.460976s)
[2025.09.15-19.19.31:305][450]LogPlayLevel: PIE: Created PIE world by copying editor world from /Game/Maps/RealLeague/RealLeague.RealLeague to /Game/Maps/RealLeague/UEDPIE_0_RealLeague.RealLeague (0.461080s)
[2025.09.15-19.19.31:333][450]LogUObjectHash: Compacting FUObjectHashTables data took   0.71ms
[2025.09.15-19.19.31:339][450]LogChaosDD: Creating Chaos Debug Draw Scene for world RealLeague
[2025.09.15-19.19.31:344][450]LogPlayLevel: PIE: World Init took: (0.004951s)
[2025.09.15-19.19.31:345][450]LogAudio: Display: Creating Audio Device:                 Id: 2, Scope: Unique, Realtime: True
[2025.09.15-19.19.31:346][450]LogAudioMixer: Display: Audio Mixer Platform Settings:
[2025.09.15-19.19.31:346][450]LogAudioMixer: Display: 	Sample Rate:						  48000
[2025.09.15-19.19.31:346][450]LogAudioMixer: Display: 	Callback Buffer Frame Size Requested: 1024
[2025.09.15-19.19.31:346][450]LogAudioMixer: Display: 	Callback Buffer Frame Size To Use:	  1024
[2025.09.15-19.19.31:346][450]LogAudioMixer: Display: 	Number of buffers to queue:			  1
[2025.09.15-19.19.31:346][450]LogAudioMixer: Display: 	Max Channels (voices):				  32
[2025.09.15-19.19.31:346][450]LogAudioMixer: Display: 	Number of Async Source Workers:		  4
[2025.09.15-19.19.31:346][450]LogAudio: Display: AudioDevice MaxSources: 32
[2025.09.15-19.19.31:346][450]LogAudio: Display: Audio Spatialization Plugin: None (built-in).
[2025.09.15-19.19.31:346][450]LogAudio: Display: Audio Reverb Plugin: None (built-in).
[2025.09.15-19.19.31:346][450]LogAudio: Display: Audio Occlusion Plugin: None (built-in).
[2025.09.15-19.19.31:348][450]LogAudioMixer: Display: Initializing audio mixer using platform API: 'XAudio2'
[2025.09.15-19.19.31:381][450]LogAudioMixer: Display: Using Audio Hardware Device Colunas (2- Realtek(R) Audio)
[2025.09.15-19.19.31:381][450]LogAudioMixer: Display: Initializing Sound Submixes...
[2025.09.15-19.19.31:381][450]LogAudioMixer: Display: Creating Master Submix 'MasterSubmixDefault'
[2025.09.15-19.19.31:381][450]LogAudioMixer: Display: Creating Master Submix 'MasterReverbSubmixDefault'
[2025.09.15-19.19.31:382][450]LogAudioMixer: FMixerPlatformXAudio2::StartAudioStream() called. InstanceID=2
[2025.09.15-19.19.31:382][450]LogAudioMixer: Display: Output buffers initialized: Frames=1024, Channels=2, Samples=2048, InstanceID=2
[2025.09.15-19.19.31:384][450]LogAudioMixer: Display: Starting AudioMixerPlatformInterface::RunInternal(), InstanceID=2
[2025.09.15-19.19.31:384][450]LogAudioMixer: Display: FMixerPlatformXAudio2::SubmitBuffer() called for the first time. InstanceID=2
[2025.09.15-19.19.31:384][450]LogInit: FAudioDevice initialized with ID 2.
[2025.09.15-19.19.31:384][450]LogAudio: Display: Audio Device (ID: 2) registered with world 'RealLeague'.
[2025.09.15-19.19.31:384][450]LogAudioMixer: Initializing Audio Bus Subsystem for audio device with ID 2
[2025.09.15-19.19.31:393][450]LogLoad: Game class is 'FirstPersonGameMode_C'
[2025.09.15-19.19.31:555][450]LogWorld: Bringing World /Game/Maps/RealLeague/UEDPIE_0_RealLeague.RealLeague up for play (max tick rate 60) at 2025.09.15-16.19.31
[2025.09.15-19.19.31:587][450]LogWorld: Bringing up level for play took: 0.157253
[2025.09.15-19.19.31:591][450]LogOnline: OSS: Created online subsystem instance for: :Context_4
[2025.09.15-19.19.31:601][450]LogStreaming: Display: FlushAsyncLoading(388): 1 QueuedPackages, 0 AsyncPackages
[2025.09.15-19.19.31:633][450]PIE: Servidor conectado
[2025.09.15-19.19.31:635][450]PIE: Tempo inicial total de reprodução no editor: 2,657 s
[2025.09.15-19.19.31:686][450]LogEngine: Error: Requested reflection capture cube size of 1024 with 2 elements results in too large a resource for host machine, limiting reflection capture cube size to 512
[2025.09.15-19.19.52:020][621]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.09.15-19.19.52:021][621]LogWorld: BeginTearingDown for /Game/Maps/RealLeague/UEDPIE_0_RealLeague
[2025.09.15-19.19.52:037][621]LogWorld: UWorld::CleanupWorld for RealLeague, bSessionEnded=true, bCleanupResources=true
[2025.09.15-19.19.52:070][621]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.09.15-19.19.52:154][621]LogPlayLevel: Display: Shutting down PIE online subsystems
[2025.09.15-19.19.52:162][621]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.09.15-19.19.52:225][621]LogAudio: Display: Audio Device unregistered from world 'None'.
[2025.09.15-19.19.52:226][621]LogAudioMixer: Deinitializing Audio Bus Subsystem for audio device with ID 2
[2025.09.15-19.19.52:232][621]LogAudioMixer: Display: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=2, StreamState=4
[2025.09.15-19.19.52:237][621]LogAudioMixer: Display: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=2, StreamState=2
[2025.09.15-19.19.52:251][621]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.09.15-19.19.52:315][621]LogUObjectHash: Compacting FUObjectHashTables data took   3.04ms
[2025.09.15-19.19.52:389][622]LogPlayLevel: Display: Destroying online subsystem :Context_4
[2025.09.15-19.19.52:432][622]LogUObjectHash: Compacting FUObjectHashTables data took   0.96ms
[2025.09.15-19.24.56:418][141]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 8788.061523
[2025.09.15-19.24.57:375][148]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.09.15-19.24.57:375][148]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 8788.900391, Update Interval: 332.584625
[2025.09.15-20.21.18:551][ 93]LogUdpMessaging: Warning: UDP messaging encountered an error. Auto repair routine started for reinitialization
[2025.09.15-20.21.18:561][ 93]LogHttp: Warning: 000001A4CD0D3F00: request failed, libcurl error: 6 (Could not resolve hostname)
[2025.09.15-20.21.18:561][ 93]LogHttp: Warning: 000001A4CD0D3F00: libcurl info message cache 0 (Too old connection (3289 seconds idle), disconnect it)
[2025.09.15-20.21.18:561][ 93]LogHttp: Warning: 000001A4CD0D3F00: libcurl info message cache 1 (Connection 6 seems to be dead)
[2025.09.15-20.21.18:561][ 93]LogHttp: Warning: 000001A4CD0D3F00: libcurl info message cache 2 (shutting down connection #6)
[2025.09.15-20.21.18:561][ 93]LogHttp: Warning: 000001A4CD0D3F00: libcurl info message cache 3 (Recv failure: Connection was reset)
[2025.09.15-20.21.18:561][ 93]LogHttp: Warning: 000001A4CD0D3F00: libcurl info message cache 4 (Send failure: Connection was reset)
[2025.09.15-20.21.18:561][ 93]LogHttp: Warning: 000001A4CD0D3F00: libcurl info message cache 5 (Recv failure: Connection was reset)
[2025.09.15-20.21.18:561][ 93]LogHttp: Warning: 000001A4CD0D3F00: libcurl info message cache 6 (Could not resolve host: datarouter.ol.epicgames.com)
[2025.09.15-20.21.18:561][ 93]LogHttp: Warning: 000001A4CD0D3F00: libcurl info message cache 7 (shutting down connection #7)
[2025.09.15-20.21.18:561][ 93]LogHttp: Warning: 000001A4CD0D3F00 POST https://datarouter.ol.epicgames.com/datarouter/api/v1/public/data?SessionID=%7B59FAE209-4637-E8F8-B07F-8196EE858B5A%7D&AppID=UEEditor.Rocket.Release&AppVersion=5.6.1-44394996%2B%2B%2BUE5%2BRelease-5.6&UserID=b4f508ed4bd797d4247ed9a748e6dcab%7C6460d6b6163c4887bdafe06ad4082897%7C3ff58bd0-1eef-4049-8d1e-6ac30b330d7e&AppEnvironment=datacollector-binary&UploadType=eteventstream completed with reason 'ConnectionError' after 0.05s
[2025.09.15-20.21.18:561][ 93]LogEOSSDK: Warning: LogHttp: 000001A4C2200600: invalid HTTP response code received. URL: https://api.epicgames.dev/telemetry/data/datarouter/api/v1/public/data?SessionID=%7BAA94F0FC-4565-B32B-14BB-738C928B6A54%7D&AppID=EOSSDK.PhaseRelease.ReleaseBuild&AppVersion=1.17.0-41373641%20-%20%2B%2BEOSSDK%2BRelease-1.17.0-***********&UserID=&AppEnvironment=Production&UploadType=sdkevents, HTTP code: 0, content length: 0, actual payload size: 0
[2025.09.15-20.21.18:561][ 93]LogEOSSDK: Warning: LogHttp: 000001A4C2200600: request failed, libcurl error: 6 (Couldn't resolve host name)
[2025.09.15-20.21.18:561][ 94]LogEOSSDK: Warning: LogHttp: 000001A4C2200600: libcurl info message cache 0 (Too old connection (3281 seconds idle), disconnect it)
[2025.09.15-20.21.18:561][ 94]LogEOSSDK: Warning: LogHttp: 000001A4C2200600: libcurl info message cache 1 (Connection 26 seems to be dead)
[2025.09.15-20.21.18:561][ 94]LogEOSSDK: Warning: LogHttp: 000001A4C2200600: libcurl info message cache 2 (Closing connection)
[2025.09.15-20.21.18:561][ 94]LogEOSSDK: Warning: LogHttp: 000001A4C2200600: libcurl info message cache 3 (Recv failure: Connection was reset)
[2025.09.15-20.21.18:561][ 94]LogEOSSDK: Warning: LogHttp: 000001A4C2200600: libcurl info message cache 4 (Send failure: Connection was reset)
[2025.09.15-20.21.18:561][ 94]LogEOSSDK: Warning: LogHttp: 000001A4C2200600: libcurl info message cache 5 (Recv failure: Connection was reset)
[2025.09.15-20.21.18:561][ 94]LogEOSSDK: Warning: LogHttp: 000001A4C2200600: libcurl info message cache 6 (Hostname in DNS cache was stale, zapped)
[2025.09.15-20.21.18:561][ 94]LogEOSSDK: Warning: LogHttp: 000001A4C2200600: libcurl info message cache 7 (Could not resolve host: api.epicgames.dev)
[2025.09.15-20.21.18:561][ 94]LogEOSSDK: Warning: LogHttp: 000001A4C2200600: libcurl info message cache 8 (Closing connection)
[2025.09.15-20.21.18:896][ 94]LogEOSSDK: Warning: LogHttp: 000001A4C2204E00: invalid HTTP response code received. URL: https://api.epicgames.dev/auth/v1/oauth/token, HTTP code: 0, content length: 0, actual payload size: 0
[2025.09.15-20.21.18:896][ 94]LogEOSSDK: Warning: LogHttp: 000001A4C2204E00: request failed, libcurl error: 6 (Couldn't resolve host name)
[2025.09.15-20.21.18:896][ 94]LogEOSSDK: Warning: LogHttp: 000001A4C2204E00: libcurl info message cache 0 (Could not resolve host: api.epicgames.dev)
[2025.09.15-20.21.18:896][ 94]LogEOSSDK: Warning: LogHttp: 000001A4C2204E00: libcurl info message cache 1 (Closing connection)
[2025.09.15-20.21.18:898][ 94]LogEOSSDK: Warning: LogHttp: 000001A4C2201800: invalid HTTP response code received. URL: https://api.epicgames.dev/datarouter/api/v1/public/data/clients?AppID=86f32f1151354e7cb39c12f8ab2c22a3&AppVersion=1.17.0-41373641&AppEnvironment=********************************&UploadType=eteventstream&SessionID=B1251CEF4B3DFC2D92681799A2CE73DC, HTTP code: 0, content length: 0, actual payload size: 0
[2025.09.15-20.21.18:898][ 94]LogEOSSDK: Warning: LogHttp: 000001A4C2201800: request failed, libcurl error: 6 (Couldn't resolve host name)
[2025.09.15-20.21.18:898][ 94]LogEOSSDK: Warning: LogHttp: 000001A4C2201800: libcurl info message cache 0 (Found bundle for host: 0x1a4ad080480 [serially])
[2025.09.15-20.21.18:898][ 94]LogEOSSDK: Warning: LogHttp: 000001A4C2201800: libcurl info message cache 1 (Could not resolve host: api.epicgames.dev)
[2025.09.15-20.21.18:898][ 94]LogEOSSDK: Warning: LogHttp: 000001A4C2201800: libcurl info message cache 2 (Closing connection)
[2025.09.15-20.21.18:898][ 94]LogEOSSDK: Warning: LogHttp: Retry exhausted on https://api.epicgames.dev/telemetry/data/datarouter/api/v1/public/data?SessionID=%7BAA94F0FC-4565-B32B-14BB-738C928B6A54%7D&AppID=EOSSDK.PhaseRelease.ReleaseBuild&AppVersion=1.17.0-41373641%20-%20%2B%2BEOSSDK%2BRelease-1.17.0-***********&UserID=&AppEnvironment=Production&UploadType=sdkevents
[2025.09.15-20.21.18:898][ 94]LogEOSSDK: Warning: LogHttp: Lockout of 1.000000s on https://api.epicgames.dev/auth/v1/oauth/token
[2025.09.15-20.21.18:898][ 94]LogEOSSDK: Warning: LogHttp: Lockout of 1.000000s on https://api.epicgames.dev/datarouter/api/v1/public/data/clients?AppID=86f32f1151354e7cb39c12f8ab2c22a3&AppVersion=1.17.0-41373641&AppEnvironment=********************************&UploadType=eteventstream&SessionID=B1251CEF4B3DFC2D92681799A2CE73DC
[2025.09.15-20.21.20:231][ 98]LogEOSSDK: Warning: LogHttp: Retry 1 on https://api.epicgames.dev/auth/v1/oauth/token
[2025.09.15-20.21.20:231][ 98]LogEOSSDK: Warning: LogHttp: Retry 1 on https://api.epicgames.dev/datarouter/api/v1/public/data/clients?AppID=86f32f1151354e7cb39c12f8ab2c22a3&AppVersion=1.17.0-41373641&AppEnvironment=********************************&UploadType=eteventstream&SessionID=B1251CEF4B3DFC2D92681799A2CE73DC
[2025.09.15-20.21.20:487][ 98]LogUdpMessaging: Display: Unicast socket bound to '0.0.0.0:55008'.
[2025.09.15-20.21.20:497][ 98]Could not fetch the local adapter addresses
[2025.09.15-20.21.20:497][ 98]LogUdpMessaging: Warning: Failed to join multicast group '*********:6666' on detected local interface '0.0.0.0'
[2025.09.15-20.21.20:509][ 98]LogUdpMessaging: Display: Using asynchronous task graph for message deserialization.
[2025.09.15-20.21.20:555][ 99]LogUdpMessaging: Warning: UDP messaging encountered an error. Auto repair routine started for reinitialization
[2025.09.15-20.21.20:564][ 99]LogEOSSDK: Warning: LogHttp: 000001A4C2204E00: invalid HTTP response code received. URL: https://api.epicgames.dev/auth/v1/oauth/token, HTTP code: 0, content length: 0, actual payload size: 0
[2025.09.15-20.21.20:564][ 99]LogEOSSDK: Warning: LogHttp: 000001A4C2204E00: request failed, libcurl error: 6 (Couldn't resolve host name)
[2025.09.15-20.21.20:564][ 99]LogEOSSDK: Warning: LogHttp: 000001A4C2204E00: libcurl info message cache 2 (Could not resolve host: api.epicgames.dev)
[2025.09.15-20.21.20:564][ 99]LogEOSSDK: Warning: LogHttp: 000001A4C2204E00: libcurl info message cache 3 (Closing connection)
[2025.09.15-20.21.20:564][ 99]LogEOSSDK: Warning: LogHttp: 000001A4C2201800: invalid HTTP response code received. URL: https://api.epicgames.dev/datarouter/api/v1/public/data/clients?AppID=86f32f1151354e7cb39c12f8ab2c22a3&AppVersion=1.17.0-41373641&AppEnvironment=********************************&UploadType=eteventstream&SessionID=B1251CEF4B3DFC2D92681799A2CE73DC, HTTP code: 0, content length: 0, actual payload size: 0
[2025.09.15-20.21.20:564][ 99]LogEOSSDK: Warning: LogHttp: 000001A4C2201800: request failed, libcurl error: 6 (Couldn't resolve host name)
[2025.09.15-20.21.20:564][ 99]LogEOSSDK: Warning: LogHttp: 000001A4C2201800: libcurl info message cache 3 (Found bundle for host: 0x1a4ad080060 [serially])
[2025.09.15-20.21.20:564][ 99]LogEOSSDK: Warning: LogHttp: 000001A4C2201800: libcurl info message cache 4 (Could not resolve host: api.epicgames.dev)
[2025.09.15-20.21.20:564][ 99]LogEOSSDK: Warning: LogHttp: 000001A4C2201800: libcurl info message cache 5 (Closing connection)
[2025.09.15-20.21.20:564][ 99]LogEOSSDK: Warning: LogHttp: Lockout of 1.000000s on https://api.epicgames.dev/auth/v1/oauth/token
[2025.09.15-20.21.20:564][ 99]LogEOSSDK: Warning: LogHttp: Lockout of 1.000000s on https://api.epicgames.dev/datarouter/api/v1/public/data/clients?AppID=86f32f1151354e7cb39c12f8ab2c22a3&AppVersion=1.17.0-41373641&AppEnvironment=********************************&UploadType=eteventstream&SessionID=B1251CEF4B3DFC2D92681799A2CE73DC
[2025.09.15-20.21.21:897][103]LogEOSSDK: Warning: LogHttp: Retry 2 on https://api.epicgames.dev/auth/v1/oauth/token
[2025.09.15-20.21.21:897][104]LogEOSSDK: Warning: LogHttp: Retry 2 on https://api.epicgames.dev/datarouter/api/v1/public/data/clients?AppID=86f32f1151354e7cb39c12f8ab2c22a3&AppVersion=1.17.0-41373641&AppEnvironment=********************************&UploadType=eteventstream&SessionID=B1251CEF4B3DFC2D92681799A2CE73DC
[2025.09.15-20.21.22:228][104]LogEOSSDK: Warning: LogHttp: 000001A4C2204E00: invalid HTTP response code received. URL: https://api.epicgames.dev/auth/v1/oauth/token, HTTP code: 0, content length: 0, actual payload size: 0
[2025.09.15-20.21.22:228][104]LogEOSSDK: Warning: LogHttp: 000001A4C2204E00: request failed, libcurl error: 6 (Couldn't resolve host name)
[2025.09.15-20.21.22:228][104]LogEOSSDK: Warning: LogHttp: 000001A4C2204E00: libcurl info message cache 4 (Could not resolve host: api.epicgames.dev)
[2025.09.15-20.21.22:228][104]LogEOSSDK: Warning: LogHttp: 000001A4C2204E00: libcurl info message cache 5 (Closing connection)
[2025.09.15-20.21.22:228][104]LogEOSSDK: Warning: LogHttp: 000001A4C2201800: invalid HTTP response code received. URL: https://api.epicgames.dev/datarouter/api/v1/public/data/clients?AppID=86f32f1151354e7cb39c12f8ab2c22a3&AppVersion=1.17.0-41373641&AppEnvironment=********************************&UploadType=eteventstream&SessionID=B1251CEF4B3DFC2D92681799A2CE73DC, HTTP code: 0, content length: 0, actual payload size: 0
[2025.09.15-20.21.22:228][104]LogEOSSDK: Warning: LogHttp: 000001A4C2201800: request failed, libcurl error: 6 (Couldn't resolve host name)
[2025.09.15-20.21.22:228][104]LogEOSSDK: Warning: LogHttp: 000001A4C2201800: libcurl info message cache 6 (Found bundle for host: 0x1a4ad080150 [serially])
[2025.09.15-20.21.22:228][104]LogEOSSDK: Warning: LogHttp: 000001A4C2201800: libcurl info message cache 7 (Could not resolve host: api.epicgames.dev)
[2025.09.15-20.21.22:228][104]LogEOSSDK: Warning: LogHttp: 000001A4C2201800: libcurl info message cache 8 (Closing connection)
[2025.09.15-20.21.22:228][104]LogEOSSDK: Warning: LogHttp: Lockout of 1.000000s on https://api.epicgames.dev/auth/v1/oauth/token
[2025.09.15-20.21.22:228][104]LogEOSSDK: Warning: LogHttp: Retry exhausted on https://api.epicgames.dev/datarouter/api/v1/public/data/clients?AppID=86f32f1151354e7cb39c12f8ab2c22a3&AppVersion=1.17.0-41373641&AppEnvironment=********************************&UploadType=eteventstream&SessionID=B1251CEF4B3DFC2D92681799A2CE73DC
[2025.09.15-20.21.22:228][104]LogEOSSDK: Warning: LogEOS: Failed to connect to the backend. ServiceName=[Metrics], OperationName=[SendBackendEvent], Url=[<Redacted>]
[2025.09.15-20.21.22:415][104]LogUdpMessaging: Display: Unicast socket bound to '0.0.0.0:59632'.
[2025.09.15-20.21.22:424][104]Could not fetch the local adapter addresses
[2025.09.15-20.21.22:424][104]LogUdpMessaging: Warning: Failed to join multicast group '*********:6666' on detected local interface '0.0.0.0'
[2025.09.15-20.21.22:431][104]LogUdpMessaging: Display: Using asynchronous task graph for message deserialization.
[2025.09.15-20.21.22:556][105]LogUdpMessaging: Warning: UDP messaging encountered an error. Auto repair routine started for reinitialization
[2025.09.15-21.01.16:973][107]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 14568.620117
[2025.09.15-21.01.16:973][107]LogEOSSDK: Warning: LogHttp: Retry 3 on https://api.epicgames.dev/auth/v1/oauth/token
[2025.09.15-21.01.17:159][107]LogUdpMessaging: Display: Unicast socket bound to '0.0.0.0:51988'.
[2025.09.15-21.01.17:168][107]Could not fetch the local adapter addresses
[2025.09.15-21.01.17:168][107]LogUdpMessaging: Warning: Failed to join multicast group '*********:6666' on detected local interface '0.0.0.0'
[2025.09.15-21.01.17:180][107]LogUdpMessaging: Display: Using asynchronous task graph for message deserialization.
[2025.09.15-21.01.17:286][108]LogUdpMessaging: Warning: UDP messaging encountered an error. Auto repair routine started for reinitialization
[2025.09.15-21.01.17:489][108]LogHttp: Warning: 000001A4CD0D0700: request failed, libcurl error: 6 (Could not resolve hostname)
[2025.09.15-21.01.17:489][108]LogHttp: Warning: 000001A4CD0D0700: libcurl info message cache 0 (Could not resolve host: datarouter.ol.epicgames.com)
[2025.09.15-21.01.17:489][108]LogHttp: Warning: 000001A4CD0D0700: libcurl info message cache 1 (shutting down connection #8)
[2025.09.15-21.01.17:489][108]LogHttp: Warning: 000001A4CD0D0700 POST https://datarouter.ol.epicgames.com/datarouter/api/v1/public/data?SessionID=%7B59FAE209-4637-E8F8-B07F-8196EE858B5A%7D&AppID=UEEditor.Rocket.Release&AppVersion=5.6.1-44394996%2B%2B%2BUE5%2BRelease-5.6&UserID=b4f508ed4bd797d4247ed9a748e6dcab%7C6460d6b6163c4887bdafe06ad4082897%7C3ff58bd0-1eef-4049-8d1e-6ac30b330d7e&AppEnvironment=datacollector-binary&UploadType=eteventstream completed with reason 'ConnectionError' after 0.02s
[2025.09.15-21.01.17:489][108]LogEOSSDK: Warning: LogHttp: 000001A4C2204E00: invalid HTTP response code received. URL: https://api.epicgames.dev/auth/v1/oauth/token, HTTP code: 0, content length: 0, actual payload size: 0
[2025.09.15-21.01.17:489][108]LogEOSSDK: Warning: LogHttp: 000001A4C2204E00: request failed, libcurl error: 6 (Couldn't resolve host name)
[2025.09.15-21.01.17:489][108]LogEOSSDK: Warning: LogHttp: 000001A4C2204E00: libcurl info message cache 6 (Found bundle for host: 0x1a4ad0807b0 [serially])
[2025.09.15-21.01.17:489][108]LogEOSSDK: Warning: LogHttp: 000001A4C2204E00: libcurl info message cache 7 (Could not resolve host: api.epicgames.dev)
[2025.09.15-21.01.17:489][108]LogEOSSDK: Warning: LogHttp: 000001A4C2204E00: libcurl info message cache 8 (Closing connection)
[2025.09.15-21.01.17:489][108]LogEOSSDK: Warning: LogHttp: 000001A4C2202A00: invalid HTTP response code received. URL: https://api.epicgames.dev/telemetry/data/datarouter/api/v1/public/data?SessionID=%7BAA94F0FC-4565-B32B-14BB-738C928B6A54%7D&AppID=EOSSDK.PhaseRelease.ReleaseBuild&AppVersion=1.17.0-41373641%20-%20%2B%2BEOSSDK%2BRelease-1.17.0-***********&UserID=&AppEnvironment=Production&UploadType=sdkevents, HTTP code: 0, content length: 0, actual payload size: 0
[2025.09.15-21.01.17:489][108]LogEOSSDK: Warning: LogHttp: 000001A4C2202A00: request failed, libcurl error: 6 (Couldn't resolve host name)
[2025.09.15-21.01.17:489][108]LogEOSSDK: Warning: LogHttp: 000001A4C2202A00: libcurl info message cache 0 (Could not resolve host: api.epicgames.dev)
[2025.09.15-21.01.17:489][109]LogEOSSDK: Warning: LogHttp: 000001A4C2202A00: libcurl info message cache 1 (Closing connection)
[2025.09.15-21.01.17:489][109]LogEOSSDK: Warning: LogHttp: Retry exhausted on https://api.epicgames.dev/telemetry/data/datarouter/api/v1/public/data?SessionID=%7BAA94F0FC-4565-B32B-14BB-738C928B6A54%7D&AppID=EOSSDK.PhaseRelease.ReleaseBuild&AppVersion=1.17.0-41373641%20-%20%2B%2BEOSSDK%2BRelease-1.17.0-***********&UserID=&AppEnvironment=Production&UploadType=sdkevents
[2025.09.15-21.01.17:489][109]LogEOSSDK: Warning: LogHttp: Lockout of 1.000000s on https://api.epicgames.dev/auth/v1/oauth/token
[2025.09.15-21.01.17:636][109]LogEOSSDK: Warning: LogHttp: 000001A4C2200C00: invalid HTTP response code received. URL: https://api.epicgames.dev/datarouter/api/v1/public/data/clients?AppID=86f32f1151354e7cb39c12f8ab2c22a3&AppVersion=1.17.0-41373641&AppEnvironment=********************************&UploadType=eteventstream&SessionID=B1251CEF4B3DFC2D92681799A2CE73DC, HTTP code: 0, content length: 0, actual payload size: 0
[2025.09.15-21.01.17:636][109]LogEOSSDK: Warning: LogHttp: 000001A4C2200C00: request failed, libcurl error: 6 (Couldn't resolve host name)
[2025.09.15-21.01.17:636][109]LogEOSSDK: Warning: LogHttp: 000001A4C2200C00: libcurl info message cache 0 (Could not resolve host: api.epicgames.dev)
[2025.09.15-21.01.17:636][109]LogEOSSDK: Warning: LogHttp: 000001A4C2200C00: libcurl info message cache 1 (Closing connection)
[2025.09.15-21.01.17:636][109]LogEOSSDK: Warning: LogHttp: Lockout of 1.000000s on https://api.epicgames.dev/datarouter/api/v1/public/data/clients?AppID=86f32f1151354e7cb39c12f8ab2c22a3&AppVersion=1.17.0-41373641&AppEnvironment=********************************&UploadType=eteventstream&SessionID=B1251CEF4B3DFC2D92681799A2CE73DC
[2025.09.15-21.01.18:630][112]LogEOSSDK: Warning: LogHttp: Retry 4 on https://api.epicgames.dev/auth/v1/oauth/token
[2025.09.15-21.01.18:964][113]LogEOSSDK: Warning: LogHttp: 000001A4C2204E00: invalid HTTP response code received. URL: https://api.epicgames.dev/auth/v1/oauth/token, HTTP code: 0, content length: 0, actual payload size: 0
[2025.09.15-21.01.18:964][113]LogEOSSDK: Warning: LogHttp: 000001A4C2204E00: request failed, libcurl error: 6 (Couldn't resolve host name)
[2025.09.15-21.01.18:964][113]LogEOSSDK: Warning: LogHttp: 000001A4C2204E00: libcurl info message cache 9 (Could not resolve host: api.epicgames.dev)
[2025.09.15-21.01.18:964][113]LogEOSSDK: Warning: LogHttp: 000001A4C2204E00: libcurl info message cache 10 (Closing connection)
[2025.09.15-21.01.18:964][113]LogEOSSDK: Warning: LogHttp: Retry exhausted on https://api.epicgames.dev/auth/v1/oauth/token
[2025.09.15-21.01.18:964][113]LogEOSSDK: Warning: LogEOS: Failed to connect to the backend. ServiceName=[EOSAuth], OperationName=[TokenGrant], Url=[<Redacted>]
[2025.09.15-21.01.18:964][113]LogEOSSDK: Warning: LogHttp: Retry 1 on https://api.epicgames.dev/datarouter/api/v1/public/data/clients?AppID=86f32f1151354e7cb39c12f8ab2c22a3&AppVersion=1.17.0-41373641&AppEnvironment=********************************&UploadType=eteventstream&SessionID=B1251CEF4B3DFC2D92681799A2CE73DC
[2025.09.15-21.01.19:088][113]LogUdpMessaging: Display: Unicast socket bound to '0.0.0.0:63109'.
[2025.09.15-21.01.19:098][113]Could not fetch the local adapter addresses
[2025.09.15-21.01.19:098][113]LogUdpMessaging: Warning: Failed to join multicast group '*********:6666' on detected local interface '0.0.0.0'
[2025.09.15-21.01.19:139][113]LogUdpMessaging: Display: Using asynchronous task graph for message deserialization.
[2025.09.15-21.01.19:289][114]LogUdpMessaging: Warning: UDP messaging encountered an error. Auto repair routine started for reinitialization
[2025.09.15-21.01.19:299][114]LogEOSSDK: Warning: LogHttp: 000001A4C2200C00: invalid HTTP response code received. URL: https://api.epicgames.dev/datarouter/api/v1/public/data/clients?AppID=86f32f1151354e7cb39c12f8ab2c22a3&AppVersion=1.17.0-41373641&AppEnvironment=********************************&UploadType=eteventstream&SessionID=B1251CEF4B3DFC2D92681799A2CE73DC, HTTP code: 0, content length: 0, actual payload size: 0
[2025.09.15-21.01.19:299][114]LogEOSSDK: Warning: LogHttp: 000001A4C2200C00: request failed, libcurl error: 6 (Couldn't resolve host name)
[2025.09.15-21.01.19:299][114]LogEOSSDK: Warning: LogHttp: 000001A4C2200C00: libcurl info message cache 2 (Could not resolve host: api.epicgames.dev)
[2025.09.15-21.01.19:299][114]LogEOSSDK: Warning: LogHttp: 000001A4C2200C00: libcurl info message cache 3 (Closing connection)
[2025.09.15-21.01.19:299][114]LogEOSSDK: Warning: LogHttp: Lockout of 1.000000s on https://api.epicgames.dev/datarouter/api/v1/public/data/clients?AppID=86f32f1151354e7cb39c12f8ab2c22a3&AppVersion=1.17.0-41373641&AppEnvironment=********************************&UploadType=eteventstream&SessionID=B1251CEF4B3DFC2D92681799A2CE73DC
[2025.09.15-21.01.19:631][115]LogEOSSDK: Warning: LogEOSAnalytics: Unable to get the client auth token. Last result: EOS_NoConnection.
[2025.09.15-21.01.19:631][115]LogEOSSDK: Warning: LogEOS: SDK Config Product Update Request Failed, Result Code: EOS_NoConnection, Retrying after 51.530197 seconds
[2025.09.15-21.01.19:964][116]LogEOSSDK: Warning: LogHttp: 000001A4C2207800: invalid HTTP response code received. URL: https://api.epicgames.dev/auth/v1/oauth/token, HTTP code: 0, content length: 0, actual payload size: 0
[2025.09.15-21.01.19:964][117]LogEOSSDK: Warning: LogHttp: 000001A4C2207800: request failed, libcurl error: 6 (Couldn't resolve host name)
[2025.09.15-21.01.19:964][117]LogEOSSDK: Warning: LogHttp: 000001A4C2207800: libcurl info message cache 0 (Could not resolve host: api.epicgames.dev)
[2025.09.15-21.01.19:964][117]LogEOSSDK: Warning: LogHttp: 000001A4C2207800: libcurl info message cache 1 (Closing connection)
[2025.09.15-21.01.19:964][117]LogEOSSDK: Warning: LogHttp: Lockout of 1.000000s on https://api.epicgames.dev/auth/v1/oauth/token
[2025.09.15-21.01.20:630][118]LogEOSSDK: Warning: LogHttp: Retry 2 on https://api.epicgames.dev/datarouter/api/v1/public/data/clients?AppID=86f32f1151354e7cb39c12f8ab2c22a3&AppVersion=1.17.0-41373641&AppEnvironment=********************************&UploadType=eteventstream&SessionID=B1251CEF4B3DFC2D92681799A2CE73DC
[2025.09.15-21.01.20:964][119]LogEOSSDK: Warning: LogHttp: Retry 1 on https://api.epicgames.dev/auth/v1/oauth/token
[2025.09.15-21.01.21:925][119]LogUdpMessaging: Display: Unicast socket bound to '0.0.0.0:59555'.
[2025.09.15-21.01.21:929][119]LogUdpMessaging: Display: Added local interface '***********' to multicast group '*********:6666'
[2025.09.15-21.01.21:937][119]LogUdpMessaging: Display: Using asynchronous task graph for message deserialization.
[2025.09.15-21.01.21:954][120]LogEOSSDK: Warning: LogHttp: Success on https://api.epicgames.dev/datarouter/api/v1/public/data/clients?AppID=86f32f1151354e7cb39c12f8ab2c22a3&AppVersion=1.17.0-41373641&AppEnvironment=********************************&UploadType=eteventstream&SessionID=B1251CEF4B3DFC2D92681799A2CE73DC
[2025.09.15-21.01.21:954][120]LogEOSSDK: Warning: LogHttp: Success on https://api.epicgames.dev/auth/v1/oauth/token
[2025.09.15-21.02.10:964][559]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 14622.612305
[2025.09.15-21.02.11:320][565]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.09.15-21.02.11:320][565]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 14622.889648, Update Interval: 353.928040
[2025.09.15-21.03.40:472][426]LogDebuggerCommands: Repeating last play command: Janela de visualização selecionada
[2025.09.15-21.03.40:478][426]LogPlayLevel: PlayLevel: No blueprints needed recompiling
[2025.09.15-21.03.40:479][426]LogPlayLevel: Creating play world package: /Game/Maps/RealLeague/UEDPIE_0_RealLeague
[2025.09.15-21.03.40:854][426]LogPlayLevel: PIE: StaticDuplicateObject took: (0.375708s)
[2025.09.15-21.03.40:855][426]LogPlayLevel: PIE: Created PIE world by copying editor world from /Game/Maps/RealLeague/RealLeague.RealLeague to /Game/Maps/RealLeague/UEDPIE_0_RealLeague.RealLeague (0.375774s)
[2025.09.15-21.03.40:880][426]LogUObjectHash: Compacting FUObjectHashTables data took   0.64ms
[2025.09.15-21.03.40:882][426]LogChaosDD: Creating Chaos Debug Draw Scene for world RealLeague
[2025.09.15-21.03.40:885][426]LogPlayLevel: PIE: World Init took: (0.002983s)
[2025.09.15-21.03.40:885][426]LogAudio: Display: Creating Audio Device:                 Id: 3, Scope: Unique, Realtime: True
[2025.09.15-21.03.40:886][426]LogAudioMixer: Display: Audio Mixer Platform Settings:
[2025.09.15-21.03.40:886][426]LogAudioMixer: Display: 	Sample Rate:						  48000
[2025.09.15-21.03.40:886][426]LogAudioMixer: Display: 	Callback Buffer Frame Size Requested: 1024
[2025.09.15-21.03.40:886][426]LogAudioMixer: Display: 	Callback Buffer Frame Size To Use:	  1024
[2025.09.15-21.03.40:886][426]LogAudioMixer: Display: 	Number of buffers to queue:			  1
[2025.09.15-21.03.40:886][426]LogAudioMixer: Display: 	Max Channels (voices):				  32
[2025.09.15-21.03.40:886][426]LogAudioMixer: Display: 	Number of Async Source Workers:		  4
[2025.09.15-21.03.40:886][426]LogAudio: Display: AudioDevice MaxSources: 32
[2025.09.15-21.03.40:886][426]LogAudio: Display: Audio Spatialization Plugin: None (built-in).
[2025.09.15-21.03.40:886][426]LogAudio: Display: Audio Reverb Plugin: None (built-in).
[2025.09.15-21.03.40:886][426]LogAudio: Display: Audio Occlusion Plugin: None (built-in).
[2025.09.15-21.03.40:888][426]LogAudioMixer: Display: Initializing audio mixer using platform API: 'XAudio2'
[2025.09.15-21.03.40:916][426]LogAudioMixer: Display: Using Audio Hardware Device Colunas (2- Realtek(R) Audio)
[2025.09.15-21.03.40:917][426]LogAudioMixer: Display: Initializing Sound Submixes...
[2025.09.15-21.03.40:917][426]LogAudioMixer: Display: Creating Master Submix 'MasterSubmixDefault'
[2025.09.15-21.03.40:917][426]LogAudioMixer: Display: Creating Master Submix 'MasterReverbSubmixDefault'
[2025.09.15-21.03.40:917][426]LogAudioMixer: FMixerPlatformXAudio2::StartAudioStream() called. InstanceID=3
[2025.09.15-21.03.40:917][426]LogAudioMixer: Display: Output buffers initialized: Frames=1024, Channels=2, Samples=2048, InstanceID=3
[2025.09.15-21.03.40:921][426]LogAudioMixer: Display: Starting AudioMixerPlatformInterface::RunInternal(), InstanceID=3
[2025.09.15-21.03.40:921][426]LogAudioMixer: Display: FMixerPlatformXAudio2::SubmitBuffer() called for the first time. InstanceID=3
[2025.09.15-21.03.40:921][426]LogInit: FAudioDevice initialized with ID 3.
[2025.09.15-21.03.40:921][426]LogAudio: Display: Audio Device (ID: 3) registered with world 'RealLeague'.
[2025.09.15-21.03.40:921][426]LogAudioMixer: Initializing Audio Bus Subsystem for audio device with ID 3
[2025.09.15-21.03.40:925][426]LogLoad: Game class is 'FirstPersonGameMode_C'
[2025.09.15-21.03.41:048][426]LogWorld: Bringing World /Game/Maps/RealLeague/UEDPIE_0_RealLeague.RealLeague up for play (max tick rate 60) at 2025.09.15-18.03.41
[2025.09.15-21.03.41:049][426]LogWorld: Bringing up level for play took: 0.097347
[2025.09.15-21.03.41:052][426]LogOnline: OSS: Created online subsystem instance for: :Context_5
[2025.09.15-21.03.41:057][426]PIE: Servidor conectado
[2025.09.15-21.03.41:058][426]PIE: Tempo inicial total de reprodução no editor: 0,581 s
[2025.09.15-21.03.41:078][426]LogEngine: Error: Requested reflection capture cube size of 1024 with 2 elements results in too large a resource for host machine, limiting reflection capture cube size to 512
[2025.09.15-21.04.17:044][785]LogShaderCompilers: Display: ================================================
[2025.09.15-21.04.17:044][785]LogShaderCompilers: Display: === FShaderJobCache stats ===
[2025.09.15-21.04.17:044][785]LogShaderCompilers: Display: Total job queries 458, among them cache hits 71 (15.50%), DDC hits 319 (69.65%), Duplicates 61 (13.32%)
[2025.09.15-21.04.17:044][785]LogShaderCompilers: Display: Tracking 326 distinct input hashes that result in 219 distinct outputs (67.18%)
[2025.09.15-21.04.17:044][785]LogShaderCompilers: Display: RAM used: 776,56 KiB of 1,60 GiB budget. Usage: 0.05%
[2025.09.15-21.04.17:044][785]LogShaderCompilers: Display: === Shader Compilation stats ===
[2025.09.15-21.04.17:045][785]LogShaderCompilers: Display: Shaders Compiled: 7
[2025.09.15-21.04.17:045][785]LogShaderCompilers: Display: Jobs assigned 7, completed 7 (100%)
[2025.09.15-21.04.17:045][785]LogShaderCompilers: Display: Average time worker was idle: 2914.06 s
[2025.09.15-21.04.17:045][785]LogShaderCompilers: Display: Time job spent in pending queue: average 0.05 s, longest 0.10 s
[2025.09.15-21.04.17:045][785]LogShaderCompilers: Display: Job execution time: average 2.28 s, max 5.25 s
[2025.09.15-21.04.17:045][785]LogShaderCompilers: Display: Job life time (pending + execution): average 2.33 s, max 5.34
[2025.09.15-21.04.17:045][785]LogShaderCompilers: Display: Shader code size: total 149,273 KiB, numShaders 8, average 18,659 KiB, min 2,336 KiB, max 58,375 KiB
[2025.09.15-21.04.17:045][785]LogShaderCompilers: Display: Time at least one job was in flight (either pending or executed): 15.58 s
[2025.09.15-21.04.17:045][785]LogShaderCompilers: Display: Mutex wait stall in FShaderJobCache::SubmitJobs:  0.10%
[2025.09.15-21.04.17:045][785]LogShaderCompilers: Display: Jobs were issued in 7 batches (only local compilation was used), average 1.00 jobs/batch
[2025.09.15-21.04.17:045][785]LogShaderCompilers: Display: Average processing rate: 0.45 jobs/sec
[2025.09.15-21.04.17:045][785]LogShaderCompilers: Display: Total thread time: 14,462 s
[2025.09.15-21.04.17:045][785]LogShaderCompilers: Display: Total thread preprocess time: 7,807 s
[2025.09.15-21.04.17:045][785]LogShaderCompilers: Display: Percentage time preprocessing: 53.99%
[2025.09.15-21.04.17:045][785]LogShaderCompilers: Display: Effective parallelization: 0.93 (times faster than compiling all shaders on one thread). Compare with number of workers: 8 - 0.116005
[2025.09.15-21.04.17:045][785]LogShaderCompilers: Display: Top 5 most expensive shader types by average time:
[2025.09.15-21.04.17:045][785]LogShaderCompilers: Display:      TBasePassPSFPrecomputedVolumetricLightmapLightingPolicy (compiled    1 times, average 4.93 sec, max 4.93 sec, min 4.93 sec)
[2025.09.15-21.04.17:045][785]LogShaderCompilers: Display:                                 TBasePassPSFNoLightMapPolicy (compiled    4 times, average 1.88 sec, max 4.96 sec, min 0.74 sec)
[2025.09.15-21.04.17:045][785]LogShaderCompilers: Display:                                                  FVelocityPS (compiled    1 times, average 0.91 sec, max 0.91 sec, min 0.91 sec)
[2025.09.15-21.04.17:045][785]LogShaderCompilers: Display:                                                  FVelocityVS (compiled    1 times, average 0.58 sec, max 0.58 sec, min 0.58 sec)
[2025.09.15-21.04.17:045][785]LogShaderCompilers: Display:                                 TBasePassVSFNoLightMapPolicy (compiled    1 times, average 0.52 sec, max 0.52 sec, min 0.52 sec)
[2025.09.15-21.04.17:045][785]LogShaderCompilers: Display: Top 5 shader types by total compile time:
[2025.09.15-21.04.17:045][785]LogShaderCompilers: Display:                                 TBasePassPSFNoLightMapPolicy - 52.00% of total time (compiled    4 times, average 1.88 sec, max 4.96 sec, min 0.74 sec)
[2025.09.15-21.04.17:045][785]LogShaderCompilers: Display:      TBasePassPSFPrecomputedVolumetricLightmapLightingPolicy - 34.09% of total time (compiled    1 times, average 4.93 sec, max 4.93 sec, min 4.93 sec)
[2025.09.15-21.04.17:045][785]LogShaderCompilers: Display:                                                  FVelocityPS - 6.28% of total time (compiled    1 times, average 0.91 sec, max 0.91 sec, min 0.91 sec)
[2025.09.15-21.04.17:045][785]LogShaderCompilers: Display:                                                  FVelocityVS - 4.03% of total time (compiled    1 times, average 0.58 sec, max 0.58 sec, min 0.58 sec)
[2025.09.15-21.04.17:045][785]LogShaderCompilers: Display:                                 TBasePassVSFNoLightMapPolicy - 3.61% of total time (compiled    1 times, average 0.52 sec, max 0.52 sec, min 0.52 sec)
[2025.09.15-21.04.17:045][785]LogShaderCompilers: Display: === Material stats ===
[2025.09.15-21.04.17:045][785]LogShaderCompilers: Display: Materials Cooked:        0
[2025.09.15-21.04.17:045][785]LogShaderCompilers: Display: Materials Translated:    177
[2025.09.15-21.04.17:045][785]LogShaderCompilers: Display: Material Total Translate Time: 0.21 s
[2025.09.15-21.04.17:045][785]LogShaderCompilers: Display: Material Translation Only: 0.06 s (29%)
[2025.09.15-21.04.17:045][785]LogShaderCompilers: Display: Material DDC Serialization Only: 0.00 s (1%)
[2025.09.15-21.04.17:045][785]LogShaderCompilers: Display: Material Cache Hits: 41 (23%)
[2025.09.15-21.04.17:045][785]LogShaderCompilers: Display: ================================================
[2025.09.15-21.04.35:818][977]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.09.15-21.04.35:818][977]LogWorld: BeginTearingDown for /Game/Maps/RealLeague/UEDPIE_0_RealLeague
[2025.09.15-21.04.35:821][977]LogWorld: UWorld::CleanupWorld for RealLeague, bSessionEnded=true, bCleanupResources=true
[2025.09.15-21.04.35:832][977]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.09.15-21.04.35:920][977]LogPlayLevel: Display: Shutting down PIE online subsystems
[2025.09.15-21.04.35:926][977]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.09.15-21.04.35:957][977]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.09.15-21.04.35:964][977]LogAudioMixer: Deinitializing Audio Bus Subsystem for audio device with ID 3
[2025.09.15-21.04.35:964][977]LogAudioMixer: Display: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=3, StreamState=4
[2025.09.15-21.04.35:968][977]LogAudioMixer: Display: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=3, StreamState=2
[2025.09.15-21.04.35:994][977]LogUObjectHash: Compacting FUObjectHashTables data took   1.51ms
[2025.09.15-21.04.36:122][978]LogPlayLevel: Display: Destroying online subsystem :Context_5
[2025.09.15-21.04.36:146][978]LogUObjectHash: Compacting FUObjectHashTables data took   0.34ms
[2025.09.15-21.08.53:903][513]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 15025.535156
[2025.09.15-21.08.54:313][517]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.09.15-21.08.54:313][517]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 15025.839844, Update Interval: 327.831055
[2025.09.15-21.14.54:725][ 57]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 15386.353516
[2025.09.15-21.14.55:132][ 61]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.09.15-21.14.55:132][ 61]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 15386.657227, Update Interval: 311.909546
[2025.09.15-21.18.22:096][ 62]Cmd: DELETE
[2025.09.15-21.18.22:097][ 62]Cmd: ACTOR DELETE
[2025.09.15-21.18.22:230][ 62]LogEditorActor: Deleted Actor: StaticMeshActor
[2025.09.15-21.18.22:426][ 62]LogUObjectHash: Compacting FUObjectHashTables data took   0.79ms
[2025.09.15-21.18.22:431][ 62]LogEditorActor: Deleted 1 Actors (0.325 secs)
[2025.09.15-21.20.52:999][525]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 15744.624023
[2025.09.15-21.20.53:422][529]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.09.15-21.20.53:422][529]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 15744.938477, Update Interval: 331.392548
[2025.09.15-21.21.26:452][871]LogDebuggerCommands: Repeating last play command: Janela de visualização selecionada
[2025.09.15-21.21.26:467][871]LogPlayLevel: PlayLevel: No blueprints needed recompiling
[2025.09.15-21.21.26:467][871]LogPlayLevel: Creating play world package: /Game/Maps/RealLeague/UEDPIE_0_RealLeague
[2025.09.15-21.21.27:091][871]LogPlayLevel: PIE: StaticDuplicateObject took: (0.624034s)
[2025.09.15-21.21.27:091][871]LogPlayLevel: PIE: Created PIE world by copying editor world from /Game/Maps/RealLeague/RealLeague.RealLeague to /Game/Maps/RealLeague/UEDPIE_0_RealLeague.RealLeague (0.624104s)
[2025.09.15-21.21.27:116][871]LogUObjectHash: Compacting FUObjectHashTables data took   0.58ms
[2025.09.15-21.21.27:118][871]LogChaosDD: Creating Chaos Debug Draw Scene for world RealLeague
[2025.09.15-21.21.27:120][871]LogPlayLevel: PIE: World Init took: (0.002738s)
[2025.09.15-21.21.27:121][871]LogAudio: Display: Creating Audio Device:                 Id: 4, Scope: Unique, Realtime: True
[2025.09.15-21.21.27:121][871]LogAudioMixer: Display: Audio Mixer Platform Settings:
[2025.09.15-21.21.27:121][871]LogAudioMixer: Display: 	Sample Rate:						  48000
[2025.09.15-21.21.27:121][871]LogAudioMixer: Display: 	Callback Buffer Frame Size Requested: 1024
[2025.09.15-21.21.27:121][871]LogAudioMixer: Display: 	Callback Buffer Frame Size To Use:	  1024
[2025.09.15-21.21.27:121][871]LogAudioMixer: Display: 	Number of buffers to queue:			  1
[2025.09.15-21.21.27:121][871]LogAudioMixer: Display: 	Max Channels (voices):				  32
[2025.09.15-21.21.27:121][871]LogAudioMixer: Display: 	Number of Async Source Workers:		  4
[2025.09.15-21.21.27:121][871]LogAudio: Display: AudioDevice MaxSources: 32
[2025.09.15-21.21.27:121][871]LogAudio: Display: Audio Spatialization Plugin: None (built-in).
[2025.09.15-21.21.27:122][871]LogAudio: Display: Audio Reverb Plugin: None (built-in).
[2025.09.15-21.21.27:122][871]LogAudio: Display: Audio Occlusion Plugin: None (built-in).
[2025.09.15-21.21.27:124][871]LogAudioMixer: Display: Initializing audio mixer using platform API: 'XAudio2'
[2025.09.15-21.21.27:159][871]LogAudioMixer: Display: Using Audio Hardware Device Colunas (2- Realtek(R) Audio)
[2025.09.15-21.21.27:159][871]LogAudioMixer: Display: Initializing Sound Submixes...
[2025.09.15-21.21.27:159][871]LogAudioMixer: Display: Creating Master Submix 'MasterSubmixDefault'
[2025.09.15-21.21.27:160][871]LogAudioMixer: Display: Creating Master Submix 'MasterReverbSubmixDefault'
[2025.09.15-21.21.27:160][871]LogAudioMixer: FMixerPlatformXAudio2::StartAudioStream() called. InstanceID=4
[2025.09.15-21.21.27:160][871]LogAudioMixer: Display: Output buffers initialized: Frames=1024, Channels=2, Samples=2048, InstanceID=4
[2025.09.15-21.21.27:163][871]LogAudioMixer: Display: Starting AudioMixerPlatformInterface::RunInternal(), InstanceID=4
[2025.09.15-21.21.27:163][871]LogAudioMixer: Display: FMixerPlatformXAudio2::SubmitBuffer() called for the first time. InstanceID=4
[2025.09.15-21.21.27:163][871]LogInit: FAudioDevice initialized with ID 4.
[2025.09.15-21.21.27:163][871]LogAudio: Display: Audio Device (ID: 4) registered with world 'RealLeague'.
[2025.09.15-21.21.27:163][871]LogAudioMixer: Initializing Audio Bus Subsystem for audio device with ID 4
[2025.09.15-21.21.27:168][871]LogLoad: Game class is 'FirstPersonGameMode_C'
[2025.09.15-21.21.27:317][871]LogWorld: Bringing World /Game/Maps/RealLeague/UEDPIE_0_RealLeague.RealLeague up for play (max tick rate 60) at 2025.09.15-18.21.27
[2025.09.15-21.21.27:319][871]LogWorld: Bringing up level for play took: 0.119156
[2025.09.15-21.21.27:321][871]LogOnline: OSS: Created online subsystem instance for: :Context_6
[2025.09.15-21.21.27:328][871]PIE: Servidor conectado
[2025.09.15-21.21.27:330][871]PIE: Tempo inicial total de reprodução no editor: 0,868 s
[2025.09.15-21.21.27:350][871]LogEngine: Error: Requested reflection capture cube size of 1024 with 2 elements results in too large a resource for host machine, limiting reflection capture cube size to 512
[2025.09.15-21.23.04:441][884]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.09.15-21.23.04:441][884]LogWorld: BeginTearingDown for /Game/Maps/RealLeague/UEDPIE_0_RealLeague
[2025.09.15-21.23.04:445][884]LogWorld: UWorld::CleanupWorld for RealLeague, bSessionEnded=true, bCleanupResources=true
[2025.09.15-21.23.04:469][884]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.09.15-21.23.04:550][884]LogPlayLevel: Display: Shutting down PIE online subsystems
[2025.09.15-21.23.04:555][884]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.09.15-21.23.04:608][884]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.09.15-21.23.04:621][884]LogAudioMixer: Deinitializing Audio Bus Subsystem for audio device with ID 4
[2025.09.15-21.23.04:621][884]LogAudioMixer: Display: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=4, StreamState=4
[2025.09.15-21.23.04:626][884]LogAudioMixer: Display: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=4, StreamState=2
[2025.09.15-21.23.04:667][884]LogUObjectHash: Compacting FUObjectHashTables data took   2.05ms
[2025.09.15-21.23.04:760][885]LogPlayLevel: Display: Destroying online subsystem :Context_6
[2025.09.15-21.23.04:795][885]LogUObjectHash: Compacting FUObjectHashTables data took   0.88ms
[2025.09.15-21.23.07:153][915]Cmd: TRANSACTION UNDO
[2025.09.15-21.23.07:153][915]LogEditorTransaction: Undo Clicando em Plano de Fundo
[2025.09.15-21.23.10:359][950]LogUObjectHash: Compacting FUObjectHashTables data took   0.45ms
[2025.09.15-21.23.12:559][950]LogSlate: Window 'Salvar conteúdo' being destroyed
[2025.09.15-21.23.20:016][ 33]Cmd: TRANSACTION UNDO
[2025.09.15-21.23.20:016][ 33]LogEditorTransaction: Undo Clicar em elementos
[2025.09.15-21.23.24:353][ 74]Cmd: TRANSACTION UNDO
[2025.09.15-21.23.24:353][ 74]LogEditorTransaction: Undo Clicando em Plano de Fundo
[2025.09.15-21.23.28:786][114]Cmd: TRANSACTION UNDO
[2025.09.15-21.23.28:786][114]LogEditorTransaction: Undo Clicar em elementos
[2025.09.15-21.23.31:492][138]LogUObjectHash: Compacting FUObjectHashTables data took   0.67ms
[2025.09.15-21.23.33:196][138]LogSlate: Window 'Salvar conteúdo' being destroyed
[2025.09.15-21.23.59:245][388]LogUObjectHash: Compacting FUObjectHashTables data took   0.46ms
[2025.09.15-21.23.59:252][388]Cmd: OBJ SAVEPACKAGE PACKAGE="/Game/Maps/RealLeague/RealLeague" FILE="../../../../../../Auracron/Saved/Autosaves/Game/Maps/RealLeague/RealLeague_Auto1.umap" SILENT=true AUTOSAVING=true KEEPDIRTY=false
[2025.09.15-21.24.00:263][388]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/Maps/RealLeague/RealLeague_Auto1
[2025.09.15-21.24.00:263][388]LogSavePackage: Moving '../../../../../../Auracron/Saved/RealLeague_Auto13CB7C27C4E49B0F0B7D900AAD84012E2.tmp' to '../../../../../../Auracron/Saved/Autosaves/Game/Maps/RealLeague/RealLeague_Auto1.umap'
[2025.09.15-21.24.00:272][388]LogFileHelpers: Editor autosave (incl. external actors) for '/Game/Maps/RealLeague/RealLeague' took 1.055
[2025.09.15-21.24.00:272][388]LogFileHelpers: Editor autosave (incl. sublevels & external actors) for all levels took 1.055
[2025.09.15-21.27.13:341][253]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 16124.952148
[2025.09.15-21.27.13:752][257]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.09.15-21.27.13:752][257]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 16125.262695, Update Interval: 309.888000
[2025.09.15-21.33.13:439][746]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 16485.074219
[2025.09.15-21.33.14:438][749]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.09.15-21.33.14:438][749]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 16485.742188, Update Interval: 330.205994
[2025.09.15-21.37.07:734][449]LogDerivedDataCache: C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: Maintenance finished in +00:00:00.000 and deleted 0 files with total size 0 MiB and 0 empty folders. Scanned 0 files in 1 folders with total size 0 MiB.
[2025.09.15-21.39.34:168][888]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 16866.027344
[2025.09.15-21.39.35:168][892]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.09.15-21.39.35:168][892]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 16866.693359, Update Interval: 359.767456
[2025.09.15-21.46.12:237][ 83]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 17264.351562
[2025.09.15-21.46.13:238][ 85]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.09.15-21.46.13:239][ 85]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 17265.019531, Update Interval: 317.977844
[2025.09.15-21.52.26:971][206]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 17639.302734
[2025.09.15-21.52.27:971][209]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.09.15-21.52.27:971][209]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 17639.970703, Update Interval: 337.404083
[2025.09.15-21.58.37:025][316]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 18009.589844
[2025.09.15-21.58.38:025][319]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.09.15-21.58.38:025][319]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 18010.257812, Update Interval: 347.594238
[2025.09.15-22.05.20:099][525]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 18412.750000
[2025.09.15-22.05.21:102][528]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.09.15-22.05.21:102][528]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 18413.416016, Update Interval: 357.359528
[2025.09.15-22.10.57:406][778]LogUObjectHash: Compacting FUObjectHashTables data took   0.46ms
[2025.09.15-22.11.02:078][778]LogSlate: Window 'Salvar conteúdo' being destroyed
[2025.09.15-22.11.02:123][778]LogStall: Shutdown...
[2025.09.15-22.11.02:123][778]LogStall: Shutdown complete.
[2025.09.15-22.11.02:156][778]LogSlate: Window 'EnvDesigns — Unreal Editor' being destroyed
[2025.09.15-22.11.02:477][779]Cmd: QUIT_EDITOR
[2025.09.15-22.11.02:477][779]LogCore: Engine exit requested (reason: UUnrealEdEngine::CloseEditor())
[2025.09.15-22.11.02:490][779]LogCore: Engine exit requested (reason: EngineExit() was called; note: exit was already requested)
[2025.09.15-22.11.02:492][779]LogStaticMesh: Abandoning remaining async distance field tasks for shutdown
[2025.09.15-22.11.02:492][779]LogStaticMesh: Abandoning remaining async card representation tasks for shutdown
[2025.09.15-22.11.02:735][779]LogWorld: UWorld::CleanupWorld for RealLeague, bSessionEnded=true, bCleanupResources=true
[2025.09.15-22.11.02:739][779]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.09.15-22.11.02:743][779]LogWorld: UWorld::CleanupWorld for RealLeague_Towers, bSessionEnded=true, bCleanupResources=true
[2025.09.15-22.11.02:743][779]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.09.15-22.11.02:743][779]LogWorld: UWorld::CleanupWorld for RealLeague_Normal, bSessionEnded=true, bCleanupResources=true
[2025.09.15-22.11.02:743][779]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.09.15-22.11.02:744][779]LogWorld: UWorld::CleanupWorld for RealLeague_Light1_LUT, bSessionEnded=true, bCleanupResources=true
[2025.09.15-22.11.02:744][779]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.09.15-22.11.02:744][779]LogWorld: UWorld::CleanupWorld for RealLeague_Normal_LUT, bSessionEnded=true, bCleanupResources=true
[2025.09.15-22.11.02:744][779]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.09.15-22.11.02:744][779]LogWorld: UWorld::CleanupWorld for RealLeague_Light2, bSessionEnded=true, bCleanupResources=true
[2025.09.15-22.11.02:744][779]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.09.15-22.11.02:744][779]LogWorld: UWorld::CleanupWorld for RealLeague_Cameras, bSessionEnded=true, bCleanupResources=true
[2025.09.15-22.11.02:744][779]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.09.15-22.11.02:744][779]LogWorld: UWorld::CleanupWorld for RealLeague_Light1, bSessionEnded=true, bCleanupResources=true
[2025.09.15-22.11.02:744][779]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.09.15-22.11.02:765][779]LogStylusInput: Shutting down StylusInput subsystem.
[2025.09.15-22.11.02:766][779]LogTedsSettings: UTedsSettingsEditorSubsystem::Deinitialize
[2025.09.15-22.11.02:769][779]LogLevelSequenceEditor: LevelSequenceEditor subsystem deinitialized.
[2025.09.15-22.11.02:792][779]LogWorld: UWorld::CleanupWorld for World_0, bSessionEnded=true, bCleanupResources=true
[2025.09.15-22.11.02:792][779]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.09.15-22.11.02:794][779]LogWorld: UWorld::CleanupWorld for World_1, bSessionEnded=true, bCleanupResources=true
[2025.09.15-22.11.02:794][779]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.09.15-22.11.02:794][779]LogWorld: UWorld::CleanupWorld for World_2, bSessionEnded=true, bCleanupResources=true
[2025.09.15-22.11.02:794][779]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.09.15-22.11.02:799][779]LogRuntimeTelemetry: Recording EnginePreExit events
[2025.09.15-22.11.02:799][779]LogStudioTelemetry: Ended StudioTelemetry Session
[2025.09.15-22.11.02:802][779]LogAnalytics: Display: [UEEditor.Rocket.Release] AnalyticsET::EndSession
[2025.09.15-22.11.03:696][779]LogGameFeatures: Shutting down game features subsystem
[2025.09.15-22.11.03:697][779]LogAudio: Display: Beginning Audio Device Manager Shutdown (Module: AudioMixerXAudio2)...
[2025.09.15-22.11.03:697][779]LogAudio: Display: Destroying 1 Remaining Audio Device(s)...
[2025.09.15-22.11.03:697][779]LogAudio: Display: Audio Device unregistered from world 'RealLeague'.
[2025.09.15-22.11.03:698][779]LogAudio: Display: Shutting down audio device while 1 references to it are still alive. For more information, compile with INSTRUMENT_AUDIODEVICE_HANDLES.
[2025.09.15-22.11.03:698][779]LogAudioMixer: Display: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=1, StreamState=4
[2025.09.15-22.11.03:701][779]LogAudioMixer: Display: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=1, StreamState=2
[2025.09.15-22.11.03:711][779]LogAudioMixer: Deinitializing Audio Bus Subsystem for audio device with ID -1
[2025.09.15-22.11.03:712][779]LogAudio: Display: Audio Device Manager Shutdown
[2025.09.15-22.11.03:714][779]LogWindowsTextInputMethodSystem: Activated input method: Português (Brasil) - (Keyboard).
[2025.09.15-22.11.03:718][779]LogSlate: Slate User Destroyed.  User Index 0, Is Virtual User: 0
[2025.09.15-22.11.03:720][779]LogExit: Preparing to exit.
[2025.09.15-22.11.03:808][779]LogUObjectHash: Compacting FUObjectHashTables data took   1.47ms
[2025.09.15-22.11.04:390][779]LogEditorDataStorage: Deinitializing
[2025.09.15-22.11.04:661][779]LogDemo: Cleaned up 0 splitscreen connections, owner deletion: enabled
[2025.09.15-22.11.04:749][779]LogExit: Editor shut down
[2025.09.15-22.11.04:766][779]LogExit: Transaction tracking system shut down
[2025.09.15-22.11.05:643][779]LogExit: Object subsystem successfully closed.
[2025.09.15-22.11.05:656][779]LogShaderCompilers: Display: Shaders left to compile 0
[2025.09.15-22.11.06:373][779]LogMemoryProfiler: Shutdown
[2025.09.15-22.11.06:374][779]LogNetworkingProfiler: Shutdown
[2025.09.15-22.11.06:374][779]LoadingProfiler: Shutdown
[2025.09.15-22.11.06:374][779]LogTimingProfiler: Shutdown
[2025.09.15-22.11.06:378][779]LogChaosVDEditor: [FChaosVDExtensionsManager::UnRegisterExtension] UnRegistering CVD Extension [FChaosVDGenericDebugDrawExtension] ...
[2025.09.15-22.11.06:378][779]LogChaosVDEditor: [FChaosVDExtensionsManager::UnRegisterExtension] UnRegistering CVD Extension [FChaosVDAccelerationStructuresExtension] ...
[2025.09.15-22.11.06:911][779]LogChaosDD: Chaos Debug Draw Shutdown
[2025.09.15-22.11.06:912][779]LogNFORDenoise: NFORDenoise function shutting down
[2025.09.15-22.11.06:914][779]RenderDocPlugin: plugin has been unloaded.
[2025.09.15-22.11.06:938][779]LogHttp: Warning: [FHttpManager::Shutdown] Unbinding delegates for 1 outstanding Http Requests:
[2025.09.15-22.11.06:938][779]LogHttp: Warning: 	verb=[POST] url=[https://datarouter.ol.epicgames.com/datarouter/api/v1/public/data?SessionID=%7B59FAE209-4637-E8F8-B07F-8196EE858B5A%7D&AppID=UEEditor.Rocket.Release&AppVersion=5.6.1-44394996%2B%2B%2BUE5%2BRelease-5.6&UserID=b4f508ed4bd797d4247ed9a748e6dcab%7C6460d6b6163c4887bdafe06ad4082897%7C3ff58bd0-1eef-4049-8d1e-6ac30b330d7e&AppEnvironment=datacollector-binary&UploadType=eteventstream] refs=[2] status=Processing
[2025.09.15-22.11.07:517][779]LogEOSShared: FEOSSDKManager::Shutdown EOS_Shutdown Result=[EOS_Success]
[2025.09.15-22.11.07:545][779]LogIris: FNetObjectFactoryRegistry::UnregisterFactory is unregistering factory: None name: NetActorFactory id: 0
[2025.09.15-22.11.07:545][779]LogIris: FNetObjectFactoryRegistry::UnregisterFactory is unregistering factory: None name: NetSubObjectFactory id: 1
[2025.09.15-22.11.07:545][779]LogPakFile: Destroying PakPlatformFile
[2025.09.15-22.11.09:593][779]LogD3D12RHI: ~FD3D12DynamicRHI
[2025.09.15-22.11.09:739][779]LogExit: Exiting.
[2025.09.15-22.11.09:799][779]Log file closed, 09/15/25 19:11:09
