﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 16
VisualStudioVersion = 16.0.28315.86
MinimumVisualStudioVersion = 10.0.40219.1
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Engine", "Engine", "{233774A8-CC9D-3FA9-86D1-90573E92B704}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Games", "Games", "{DE1F8B53-6C02-3C13-9101-A7C8D96F3FF6}"
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "UE5", "Intermediate\ProjectFiles\UE5.vcxproj", "{D6EF321F-CABB-3A41-A83D-FFC1CA6CE999}"
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "EnvDesigns", "Intermediate\ProjectFiles\EnvDesigns.vcxproj", "{320801AF-805A-3FC6-A733-C5A27D7F366E}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Visualizers", "Visualizers", "{1CCEC849-CC72-4C59-8C36-2F7C38706D4C}"
	ProjectSection(SolutionItems) = preProject
		C:\_GAMES\EpicLauncher\UE_5.1\Engine\Extras\VisualStudioDebugging\Unreal.natvis = C:\_GAMES\EpicLauncher\UE_5.1\Engine\Extras\VisualStudioDebugging\Unreal.natvis
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		DebugGame Editor|Win64 = DebugGame Editor|Win64
		DebugGame|Win64 = DebugGame|Win64
		Development Editor|Win64 = Development Editor|Win64
		Development|Win64 = Development|Win64
		Shipping|Win64 = Shipping|Win64
	EndGlobalSection
	# UnrealVS Section
	GlobalSection(ddbf523f-7eb6-4887-bd51-85a714ff87eb) = preSolution
		AvailablePlatforms=Win64
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{D6EF321F-CABB-3A41-A83D-FFC1CA6CE999}.DebugGame Editor|Win64.ActiveCfg = BuiltWithUnrealBuildTool|Win64
		{D6EF321F-CABB-3A41-A83D-FFC1CA6CE999}.DebugGame|Win64.ActiveCfg = BuiltWithUnrealBuildTool|Win64
		{D6EF321F-CABB-3A41-A83D-FFC1CA6CE999}.Development Editor|Win64.ActiveCfg = BuiltWithUnrealBuildTool|Win64
		{D6EF321F-CABB-3A41-A83D-FFC1CA6CE999}.Development|Win64.ActiveCfg = BuiltWithUnrealBuildTool|Win64
		{D6EF321F-CABB-3A41-A83D-FFC1CA6CE999}.Shipping|Win64.ActiveCfg = BuiltWithUnrealBuildTool|Win64
		{320801AF-805A-3FC6-A733-C5A27D7F366E}.DebugGame Editor|Win64.ActiveCfg = DebugGame_Editor|x64
		{320801AF-805A-3FC6-A733-C5A27D7F366E}.DebugGame Editor|Win64.Build.0 = DebugGame_Editor|x64
		{320801AF-805A-3FC6-A733-C5A27D7F366E}.DebugGame|Win64.ActiveCfg = DebugGame|x64
		{320801AF-805A-3FC6-A733-C5A27D7F366E}.DebugGame|Win64.Build.0 = DebugGame|x64
		{320801AF-805A-3FC6-A733-C5A27D7F366E}.Development Editor|Win64.ActiveCfg = Development_Editor|x64
		{320801AF-805A-3FC6-A733-C5A27D7F366E}.Development Editor|Win64.Build.0 = Development_Editor|x64
		{320801AF-805A-3FC6-A733-C5A27D7F366E}.Development|Win64.ActiveCfg = Development|x64
		{320801AF-805A-3FC6-A733-C5A27D7F366E}.Development|Win64.Build.0 = Development|x64
		{320801AF-805A-3FC6-A733-C5A27D7F366E}.Shipping|Win64.ActiveCfg = Shipping|x64
		{320801AF-805A-3FC6-A733-C5A27D7F366E}.Shipping|Win64.Build.0 = Shipping|x64
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{D6EF321F-CABB-3A41-A83D-FFC1CA6CE999} = {233774A8-CC9D-3FA9-86D1-90573E92B704}
		{320801AF-805A-3FC6-A733-C5A27D7F366E} = {DE1F8B53-6C02-3C13-9101-A7C8D96F3FF6}
	EndGlobalSection
EndGlobal
