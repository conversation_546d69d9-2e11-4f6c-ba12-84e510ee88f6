// Copyright Epic Games, Inc. All Rights Reserved.

#include "EnvDesignsGameMode.h"
#include "EnvDesignsCharacter.h"
#include "UObject/ConstructorHelpers.h"

AEnvDesignsGameMode::AEnvDesignsGameMode()
{
	// set default pawn class to our Blueprinted character
	static ConstructorHelpers::FClassFinder<APawn> PlayerPawnBPClass(TEXT("/Game/ThirdPerson/Blueprints/BP_ThirdPersonCharacter"));
	if (PlayerPawnBPClass.Class != NULL)
	{
		DefaultPawnClass = PlayerPawnBPClass.Class;
	}
}
