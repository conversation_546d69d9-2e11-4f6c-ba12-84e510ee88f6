;METADATA=(Diff=true, UseCommands=true)
[/Script/UnrealEd.EditorPerProjectUserSettings]
bDisplayDocumentationLink=False
bDisplayActionListItemRefIds=False
bAlwaysGatherBehaviorTreeDebuggerData=False
bDisplayBlackboardKeysInAlphabeticalOrder=False
bUseSimplygonSwarm=False
SimplygonServerIP=127.0.0.1
bEnableSwarmDebugging=False
SimplygonSwarmDelay=5000
SwarmNumOfConcurrentJobs=16
SwarmMaxUploadChunkSizeInMB=100
SwarmIntermediateFolder=C:/Auracron/Intermediate/Simplygon/
bShowCompilerLogOnCompileError=False
DataSourceFolder=(Path="")
bAnimationReimportWarnings=False
bConfirmEditorClose=False
bSCSEditorShowFloor=False
bAlwaysBuildUAT=True
SCSViewportCameraSpeed=4
bShowSelectionSubcomponents=True
AssetViewerProfileName=
PreviewFeatureLevel=3
PreviewPlatformName=None
PreviewShaderFormatName=None
PreviewShaderPlatformName=None
bPreviewFeatureLevelActive=False
bPreviewFeatureLevelWasDefault=True
PreviewDeviceProfileName=None

[/Script/UnrealEd.EditorStyleSettings]
XAxisColor=(R=0.594000,G=0.019700,B=0.000000,A=1.000000)
YAxisColor=(R=0.134900,G=0.395900,B=0.000000,A=1.000000)
ZAxisColor=(R=0.025100,G=0.207000,B=0.850000,A=1.000000)
ApplicationScale=1.000000
bColorVisionDeficiencyCorrection=False
bColorVisionDeficiencyCorrectionPreviewWithDeficiency=False
SelectionColor=(R=0.828000,G=0.364000,B=0.003000,A=1.000000)
AdditionalSelectionColors[0]=(R=0.019382,G=0.496933,B=1.000000,A=1.000000)
AdditionalSelectionColors[1]=(R=0.356400,G=0.040915,B=0.520996,A=1.000000)
AdditionalSelectionColors[2]=(R=1.000000,G=0.168269,B=0.332452,A=1.000000)
AdditionalSelectionColors[3]=(R=1.000000,G=0.051269,B=0.051269,A=1.000000)
AdditionalSelectionColors[4]=(R=1.000000,G=0.715693,B=0.010330,A=1.000000)
AdditionalSelectionColors[5]=(R=0.258183,G=0.539479,B=0.068478,A=1.000000)
ViewportToolOverlayColor=(R=1.000000,G=1.000000,B=1.000000,A=1.000000)
bEnableEditorWindowBackgroundColor=False
EditorWindowBackgroundColor=(R=1.000000,G=1.000000,B=1.000000,A=1.000000)
MenuSearchFieldVisibilityThreshold=10
bUseGrid=True
bAntiAliasGrid=True
RegularColor=(R=0.024000,G=0.024000,B=0.024000,A=1.000000)
RuleColor=(R=0.010000,G=0.010000,B=0.010000,A=1.000000)
CenterColor=(R=0.005000,G=0.005000,B=0.005000,A=1.000000)
GridSnapSize=16
GraphBackgroundBrush=(TintColor=(SpecifiedColor=(R=1.000000,G=1.000000,B=1.000000,A=1.000000),ColorUseRule=UseColor_Specified),DrawAs=Image,Tiling=NoTile,Mirroring=NoMirror,ImageType=NoImage,ImageSize=(X=32.000000,Y=32.000000),Margin=(Left=0.000000,Top=0.000000,Right=0.000000,Bottom=0.000000),ResourceObject=None,OutlineSettings=(CornerRadii=(X=0.000000,Y=0.000000,Z=0.000000,W=0.000000),Color=(SpecifiedColor=(R=0.000000,G=0.000000,B=0.000000,A=0.000000),ColorUseRule=UseColor_Specified),Width=0.000000,RoundingType=HalfHeightRadius,bUseBrushTransparency=False),UVRegion=(Min=(X=0.000000,Y=0.000000),Max=(X=0.000000,Y=0.000000),bIsValid=False),bIsDynamicallyLoaded=False,ResourceName="")
bShowNativeComponentNames=True
AssetEditorOpenLocation=Default
bEnableColorizedEditorTabs=True
CurrentAppliedTheme=134380265FBB4A9CA00A1DC9770217B8
bEnableMiddleEllipsis=True

[/Script/UnrealEd.LevelEditorPlaySettings]
LaptopScreenResolutions=(Description="Apple MacBook Air 11",Width=1366,Height=768,AspectRatio="16:9",bCanSwapAspectRatio=True,ProfileName="")
LaptopScreenResolutions=(Description="Apple MacBook Air 13\"",Width=1440,Height=900,AspectRatio="16:10",bCanSwapAspectRatio=True,ProfileName="")
LaptopScreenResolutions=(Description="Apple MacBook Pro 13\"",Width=1280,Height=800,AspectRatio="16:10",bCanSwapAspectRatio=True,ProfileName="")
LaptopScreenResolutions=(Description="Apple MacBook Pro 13\" (Retina)",Width=2560,Height=1600,AspectRatio="16:10",bCanSwapAspectRatio=True,ProfileName="")
LaptopScreenResolutions=(Description="Apple MacBook Pro 15\"",Width=1440,Height=900,AspectRatio="16:10",bCanSwapAspectRatio=True,ProfileName="")
LaptopScreenResolutions=(Description="Apple MacBook Pro 15\" (Retina)",Width=2880,Height=1800,AspectRatio="16:10",bCanSwapAspectRatio=True,ProfileName="")
LaptopScreenResolutions=(Description="Generic 14-15.6\" Notebook",Width=1366,Height=768,AspectRatio="16:9",bCanSwapAspectRatio=True,ProfileName="")
MonitorScreenResolutions=(Description="19\" monitor",Width=1440,Height=900,AspectRatio="16:10",bCanSwapAspectRatio=True,ProfileName="")
MonitorScreenResolutions=(Description="20\" monitor",Width=1600,Height=900,AspectRatio="16:9",bCanSwapAspectRatio=True,ProfileName="")
MonitorScreenResolutions=(Description="22\" monitor",Width=1680,Height=1050,AspectRatio="16:10",bCanSwapAspectRatio=True,ProfileName="")
MonitorScreenResolutions=(Description="21.5-24\" monitor",Width=1920,Height=1080,AspectRatio="16:9",bCanSwapAspectRatio=True,ProfileName="")
MonitorScreenResolutions=(Description="27\" monitor",Width=2560,Height=1440,AspectRatio="16:9",bCanSwapAspectRatio=True,ProfileName="")
TabletScreenResolutions=(Description="iPad Pro 12.9-inch (3rd gen.)",Width=1024,Height=1366,AspectRatio="~3:4",bCanSwapAspectRatio=True,ProfileName="iPadPro3_129")
TabletScreenResolutions=(Description="iPad Pro 12.9-inch (2nd gen.)",Width=1024,Height=1366,AspectRatio="~3:4",bCanSwapAspectRatio=True,ProfileName="iPadPro2_129")
TabletScreenResolutions=(Description="iPad Pro 11-inch",Width=834,Height=1194,AspectRatio="5:7",bCanSwapAspectRatio=True,ProfileName="iPadPro11")
TabletScreenResolutions=(Description="iPad Pro 10.5-inch",Width=834,Height=1112,AspectRatio="3:4",bCanSwapAspectRatio=True,ProfileName="iPadPro105")
TabletScreenResolutions=(Description="iPad Pro 12.9-inch",Width=1024,Height=1366,AspectRatio="3:4",bCanSwapAspectRatio=True,ProfileName="iPadPro129")
TabletScreenResolutions=(Description="iPad Pro 9.7-inch",Width=768,Height=1024,AspectRatio="3:4",bCanSwapAspectRatio=True,ProfileName="iPadPro97")
TabletScreenResolutions=(Description="iPad (6th gen.)",Width=768,Height=1024,AspectRatio="3:4",bCanSwapAspectRatio=True,ProfileName="iPad6")
TabletScreenResolutions=(Description="iPad (5th gen.)",Width=768,Height=1024,AspectRatio="3:4",bCanSwapAspectRatio=True,ProfileName="iPad5")
TabletScreenResolutions=(Description="iPad Air 3",Width=768,Height=1024,AspectRatio="3:4",bCanSwapAspectRatio=True,ProfileName="iPadAir3")
TabletScreenResolutions=(Description="iPad Air 2",Width=768,Height=1024,AspectRatio="3:4",bCanSwapAspectRatio=True,ProfileName="iPadAir2")
TabletScreenResolutions=(Description="iPad Mini 5",Width=768,Height=1024,AspectRatio="3:4",bCanSwapAspectRatio=True,ProfileName="iPadMini5")
TabletScreenResolutions=(Description="iPad Mini 4",Width=768,Height=1024,AspectRatio="3:4",bCanSwapAspectRatio=True,ProfileName="iPadMini4")
TabletScreenResolutions=(Description="LG G Pad X 8.0",Width=768,Height=1366,AspectRatio="9:16",bCanSwapAspectRatio=True,ProfileName="")
TabletScreenResolutions=(Description="Asus Zenpad 3s 10",Width=768,Height=1366,AspectRatio="9:16",bCanSwapAspectRatio=True,ProfileName="")
TabletScreenResolutions=(Description="Huawei MediaPad M3",Width=768,Height=1366,AspectRatio="9:16",bCanSwapAspectRatio=True,ProfileName="")
TabletScreenResolutions=(Description="Microsoft Surface RT",Width=768,Height=1366,AspectRatio="9:16",bCanSwapAspectRatio=True,ProfileName="")
TabletScreenResolutions=(Description="Microsoft Surface Pro",Width=1080,Height=1920,AspectRatio="9:16",bCanSwapAspectRatio=True,ProfileName="")
TelevisionScreenResolutions=(Description="720p (HDTV, Blu-ray)",Width=1280,Height=720,AspectRatio="16:9",bCanSwapAspectRatio=True,ProfileName="")
TelevisionScreenResolutions=(Description="1080i, 1080p (HDTV, Blu-ray)",Width=1920,Height=1080,AspectRatio="16:9",bCanSwapAspectRatio=True,ProfileName="")
TelevisionScreenResolutions=(Description="4K Ultra HD",Width=3840,Height=2160,AspectRatio="16:9",bCanSwapAspectRatio=True,ProfileName="")
TelevisionScreenResolutions=(Description="4K Digital Cinema",Width=4096,Height=2160,AspectRatio="1.90:1",bCanSwapAspectRatio=True,ProfileName="")
GameGetsMouseControl=False
UseMouseForTouch=False
MouseControlLabelPosition=LabelAnchorMode_TopLeft
ViewportGetsHMDControl=False
bShouldMinimizeEditorOnNonVRPIE=False
bEmulateStereo=False
SoloAudioInFirstPIEClient=False
EnablePIEEnterAndExitSounds=False
PlayInEditorSoundQualityLevel=0
bUseNonRealtimeAudioDevice=False
bPreferToStreamLevelsInPIE=False
bPromoteOutputLogWarningsDuringPIE=False
NewWindowPosition=(X=-1,Y=-1)
PIEAlwaysOnTop=False
DisableStandaloneSound=False
AdditionalLaunchParameters=
BuildGameBeforeLaunch=PlayOnBuild_Default
LaunchConfiguration=LaunchConfig_Default
PackFilesForLaunch=NoPak
bAutoCompileBlueprintsOnLaunch=True
bLaunchSeparateServer=False
PlayNetMode=PIE_Standalone
RunUnderOneProcess=True
PlayNumberOfClients=1
PrimaryPIEClientIndex=0
ServerPort=17777
ClientWindowWidth=640
RouteGamepadToSecondWindow=False
CreateAudioDeviceForEveryPlayer=False
ClientWindowHeight=480
ServerMapNameOverride=
AdditionalServerGameOptions=
bShowServerDebugDrawingByDefault=True
ServerDebugDrawingColorTintStrength=0.000000
ServerDebugDrawingColorTint=(R=0.000000,G=0.000000,B=0.000000,A=1.000000)
bHMDForPrimaryProcessOnly=True
AdditionalServerLaunchParameters=
ServerFixedFPS=0
NetworkEmulationSettings=(bIsNetworkEmulationEnabled=False,EmulationTarget=Server,CurrentProfile="Custom",OutPackets=(MinLatency=0,MaxLatency=0,PacketLossPercentage=0),InPackets=(MinLatency=0,MaxLatency=0,PacketLossPercentage=0))
LastSize=(X=0,Y=0)
LastExecutedLaunchDevice=Windows@TKT
LastExecutedLaunchName=TKT
LastExecutedPIEPreviewDevice=
DeviceToEmulate=
PIESafeZoneOverride=(Left=0.000000,Top=0.000000,Right=0.000000,Bottom=0.000000)

[/Script/UnrealEd.LevelEditorViewportSettings]
FlightCameraControlExperimentalNavigation=False
MinimumOrthographicZoom=250.000000
bAllowArcballRotate=False
bAllowScreenRotate=False
bShowActorEditorContext=True
bShowBrushMarkerPolys=False
bAllowEditWidgetAxisDisplay=True
bUseLegacyCameraMovementNotifications=False
SnapToSurface=(bEnabled=False,SnapOffsetExtent=0.000000,bSnapRotation=True)
bEnableLayerSnap=False
ActiveSnapLayerIndex=0
PreserveNonUniformScale=True
bUseLODViewLocking=False
PreviewMeshes=/Engine/EditorMeshes/ColorCalibrator/SM_ColorCalibrator.SM_ColorCalibrator
BillboardScale=1.000000
bSaveEngineStats=False
MeasuringToolUnits=MeasureUnits_Centimeters
SelectedSplinePointSizeAdjustment=0.000000
SplineLineThicknessAdjustment=0.000000
SplineTangentHandleSizeAdjustment=0.000000
SplineTangentScale=0.500000
LastInViewportMenuLocation=(X=0.000000,Y=0.000000)
MaterialForDroppedTextures=None
MaterialParamsForDroppedTextures=()
EditorViews=(("/Game/FirstPersonBP/Maps/FirstPersonExampleMap.FirstPersonExampleMap", (LevelViewportsInfo=((CamPosition=(X=-1451.932617,Y=139.440521,Z=979.150757),CamOrthoZoom=2841.781494),(CamPosition=(X=-1451.932617,Y=139.440521,Z=979.150757),CamOrthoZoom=2841.781494,CamUpdated=True),(CamPosition=(X=-1451.932617,Y=139.440521,Z=979.150757),CamOrthoZoom=2841.781494,CamUpdated=True),(CamPosition=(X=-3696.574416,Y=-2520.875959,Z=3695.013146),CamRotation=(Pitch=-53.399900,Yaw=-333.526111,Roll=0.000000)),(CamUpdated=True),(CamUpdated=True),(CamPosition=(X=-1451.932617,Y=139.440521,Z=979.150757),CamOrthoZoom=2841.781494),(CamPosition=(X=-1451.932617,Y=139.440521,Z=979.150757),CamOrthoZoom=2841.781494)))),("/Game/Maps/RealLeague/RealLeague.RealLeague", (LevelViewportsInfo=((CamPosition=(X=-1986.793701,Y=363.633301,Z=266.861328),CamOrthoZoom=91210.796875),(CamPosition=(X=-1986.793701,Y=363.633301,Z=266.861328),CamOrthoZoom=91210.796875,CamUpdated=True),(CamPosition=(X=-1986.793701,Y=363.633301,Z=266.861328),CamOrthoZoom=91210.796875,CamUpdated=True),(CamPosition=(X=5030.244611,Y=-7377.738424,Z=7070.890062),CamRotation=(Pitch=-57.387475,Yaw=116.757657,Roll=0.000000)),(CamUpdated=True),(CamUpdated=True),(CamPosition=(X=-1986.793701,Y=363.633301,Z=266.861328),CamOrthoZoom=91210.796875),(CamPosition=(X=-1986.793701,Y=363.633301,Z=266.861328),CamOrthoZoom=91210.796875)))))
PropertyColorationColorForMatchingObjects=(B=0,G=0,R=255,A=255)
PerInstanceSettings=(ConfigName="FourPanes2x2.Viewport 1.Viewport0",ConfigSettings=(ViewportType=LVT_OrthoNegativeYZ,PerspViewModeIndex=VMI_Lit,OrthoViewModeIndex=VMI_BrushWireframe,EditorShowFlagsString="PostProcessing=0,Bloom=1,LocalExposure=1,Tonemapper=1,AntiAliasing=1,TemporalAA=1,AmbientCubemap=1,EyeAdaptation=1,VisualizeHDR=0,VisualizeSkyLightIlluminance=0,VisualizeLocalFogVolumes=0,VisualizeLocalExposure=0,LensFlares=1,LensDistortion=1,GlobalIllumination=1,Vignette=1,Grain=1,AmbientOcclusion=1,Decals=1,CameraImperfections=1,OnScreenDebug=1,OverrideDiffuseAndSpecular=0,LightingOnlyOverride=0,ReflectionOverride=0,MaterialNormal=1,MaterialAmbientOcclusion=1,VisualizeBuffer=0,VisualizeNanite=0,VisualizeLumen=0,VisualizeSubstrate=0,VisualizeGroom=0,VisualizeVirtualShadowMap=0,DirectLighting=1,DirectionalLights=1,PointLights=1,SpotLights=1,RectLights=1,ColorGrading=1,VectorFields=0,DepthOfField=1,GBufferHints=0,MotionBlur=0,CompositeDebugPrimitives=1,CompositeEditorPrimitives=1,OpaqueCompositeEditorPrimitives=0,TestImage=0,VisualizeDOF=0,VertexColors=0,PhysicalMaterialMasks=0,Refraction=1,CameraInterpolation=1,SceneColorFringe=1,ToneCurve=1,SeparateTranslucency=1,ScreenPercentage=0,VisualizeMotionBlur=0,VisualizeMotionVectors=0,VisualizeReprojection=0,VisualizeTemporalUpscaler=0,VisualizeTSR=0,MegaLights=1,ReflectionEnvironment=1,VisualizeOutOfBoundsPixels=0,Diffuse=1,Specular=1,SelectionOutline=1,ScreenSpaceReflections=1,LumenReflections=1,ContactShadows=1,RayTracedDistanceFieldShadows=1,CapsuleShadows=1,SubsurfaceScattering=1,VisualizeSSS=0,VolumetricLightmap=1,IndirectLightingCache=1,DebugAI=0,VisLog=1,Navigation=0,GameplayDebug=1,TexturedLightProfiles=1,LightFunctions=1,InstancedStaticMeshes=1,InstancedFoliage=1,HISMCOcclusionBounds=0,HISMCClusterTree=0,VisualizeInstanceUpdates=0,InstancedGrass=1,DynamicShadows=1,Particles=1,Niagara=1,HeterogeneousVolumes=1,SkeletalMeshes=1,BuilderBrush=1,Translucency=1,BillboardSprites=1,LOD=1,LightComplexity=0,ShaderComplexity=0,StationaryLightOverlap=0,LightMapDensity=0,StreamingBounds=0,Constraints=0,MassProperties=0,CameraFrustums=0,AudioRadius=1,ForceFeedbackRadius=1,BSPSplit=0,Brushes=1,Lighting=1,UnlitRichView=1,UnlitViewmode=1,DeferredLighting=1,Editor=1,BSPTriangles=0,LargeVertices=0,Grid=1,Snap=0,MeshEdges=0,Cover=0,Splines=1,Selection=1,VisualizeLevelInstanceEditing=1,ModeWidgets=1,Bounds=0,HitProxies=0,LightInfluences=0,Pivot=1,ShadowFrustums=0,Wireframe=1,Materials=1,StaticMeshes=1,Landscape=1,LightRadius=1,Fog=1,Volumes=1,Game=0,BSP=1,Collision=0,CollisionVisibility=0,CollisionPawn=0,LightShafts=1,PostProcessMaterial=1,Atmosphere=1,Cloud=1,CameraAspectRatioBars=0,CameraSafeFrames=0,TextRender=1,Rendering=1,HMDDistortion=0,StereoRendering=0,DistanceCulledPrimitives=1,VisualizeLightCulling=0,PrecomputedVisibility=1,SkyLighting=1,PreviewShadowsIndicator=1,PrecomputedVisibilityCells=0,VisualizeVolumetricLightmap=0,VolumeLightingSamples=0,Paper2DSprites=1,VisualizeDistanceFieldAO=0,VisualizeMeshDistanceFields=0,PhysicsField=0,VisualizeGlobalDistanceField=0,VisualizeLightingOnProbes=0,ScreenSpaceAO=1,DistanceFieldAO=1,LumenGlobalIllumination=1,VolumetricFog=1,VisualizeSSR=0,VisualizeShadingModels=0,VisualizeSenses=1,LODColoration=0,HLODColoration=0,QuadOverdraw=0,ShaderComplexityWithQuadOverdraw=0,PrimitiveDistanceAccuracy=0,MeshUVDensityAccuracy=0,MaterialTextureScaleAccuracy=0,OutputMaterialTextureScales=0,RequiredTextureResolution=0,VisualizeVirtualTexture=0,WidgetComponents=1,Bones=0,ServerDrawDebug=0,MediaPlanes=1,VREditing=0,OcclusionMeshes=0,VisualizeInstanceOcclusionQueries=0,DisableOcclusionQueries=0,PathTracing=0,RayTracingDebug=0,VisualizeSkyAtmosphere=0,VisualizeLightFunctionAtlas=0,VisualizeCalibrationColor=0,VisualizeCalibrationGrayscale=0,VisualizeCalibrationCustom=0,VisualizePostProcessStack=0,VirtualTexturePrimitives=0,VisualizeVolumetricCloudConservativeDensity=0,VisualizeVolumetricCloudEmptySpaceSkipping=0,VirtualShadowMapPersistentData=1,DebugDrawDistantVirtualSMLights=0,VirtualTextureResidency=1,InputDebugVisualizer=1,LumenScreenTraces=1,LumenDetailTraces=1,LumenGlobalTraces=1,LumenFarFieldTraces=1,LumenSecondaryBounces=1,LumenShortRangeAmbientOcclusion=1,NaniteMeshes=1,NaniteStreamingGeometry=1,VisualizeGPUSkinCache=0,VisualizeLWCComplexity=0,ShaderPrint=1,SceneCaptureCopySceneDepth=1,Cameras=1,Hair=1,AllowPrimitiveAlphaHoldout=1,AlphaInvert=0,SmartObjects=0,GameplayCameras=1,PCGDebug=1",GameShowFlagsString="PostProcessing=0,Bloom=1,LocalExposure=1,Tonemapper=1,AntiAliasing=1,TemporalAA=1,AmbientCubemap=1,EyeAdaptation=1,VisualizeHDR=0,VisualizeSkyLightIlluminance=0,VisualizeLocalFogVolumes=0,VisualizeLocalExposure=0,LensFlares=1,LensDistortion=1,GlobalIllumination=1,Vignette=1,Grain=1,AmbientOcclusion=1,Decals=1,CameraImperfections=1,OnScreenDebug=1,OverrideDiffuseAndSpecular=0,LightingOnlyOverride=0,ReflectionOverride=0,MaterialNormal=1,MaterialAmbientOcclusion=1,VisualizeBuffer=0,VisualizeNanite=0,VisualizeLumen=0,VisualizeSubstrate=0,VisualizeGroom=0,VisualizeVirtualShadowMap=0,DirectLighting=1,DirectionalLights=1,PointLights=1,SpotLights=1,RectLights=1,ColorGrading=1,VectorFields=0,DepthOfField=1,GBufferHints=0,MotionBlur=1,CompositeDebugPrimitives=1,CompositeEditorPrimitives=0,OpaqueCompositeEditorPrimitives=0,TestImage=0,VisualizeDOF=0,VertexColors=0,PhysicalMaterialMasks=0,Refraction=1,CameraInterpolation=1,SceneColorFringe=1,ToneCurve=1,SeparateTranslucency=1,ScreenPercentage=1,VisualizeMotionBlur=0,VisualizeMotionVectors=0,VisualizeReprojection=0,VisualizeTemporalUpscaler=0,VisualizeTSR=0,MegaLights=1,ReflectionEnvironment=1,VisualizeOutOfBoundsPixels=0,Diffuse=1,Specular=1,SelectionOutline=0,ScreenSpaceReflections=1,LumenReflections=1,ContactShadows=1,RayTracedDistanceFieldShadows=1,CapsuleShadows=1,SubsurfaceScattering=1,VisualizeSSS=0,VolumetricLightmap=1,IndirectLightingCache=1,DebugAI=0,VisLog=1,Navigation=0,GameplayDebug=1,TexturedLightProfiles=1,LightFunctions=1,InstancedStaticMeshes=1,InstancedFoliage=1,HISMCOcclusionBounds=0,HISMCClusterTree=0,VisualizeInstanceUpdates=0,InstancedGrass=1,DynamicShadows=1,Particles=1,Niagara=1,HeterogeneousVolumes=1,SkeletalMeshes=1,BuilderBrush=1,Translucency=1,BillboardSprites=1,LOD=1,LightComplexity=0,ShaderComplexity=0,StationaryLightOverlap=0,LightMapDensity=0,StreamingBounds=0,Constraints=0,MassProperties=0,CameraFrustums=0,AudioRadius=0,ForceFeedbackRadius=1,BSPSplit=0,Brushes=1,Lighting=1,UnlitRichView=1,UnlitViewmode=1,DeferredLighting=1,Editor=0,BSPTriangles=0,LargeVertices=0,Grid=0,Snap=0,MeshEdges=0,Cover=0,Splines=0,Selection=0,VisualizeLevelInstanceEditing=1,ModeWidgets=0,Bounds=0,HitProxies=0,LightInfluences=0,Pivot=0,ShadowFrustums=0,Wireframe=1,Materials=1,StaticMeshes=1,Landscape=1,LightRadius=0,Fog=1,Volumes=0,Game=1,BSP=1,Collision=0,CollisionVisibility=0,CollisionPawn=0,LightShafts=1,PostProcessMaterial=1,Atmosphere=1,Cloud=1,CameraAspectRatioBars=0,CameraSafeFrames=0,TextRender=1,Rendering=1,HMDDistortion=0,StereoRendering=0,DistanceCulledPrimitives=0,VisualizeLightCulling=0,PrecomputedVisibility=1,SkyLighting=1,PreviewShadowsIndicator=1,PrecomputedVisibilityCells=0,VisualizeVolumetricLightmap=0,VolumeLightingSamples=0,Paper2DSprites=1,VisualizeDistanceFieldAO=0,VisualizeMeshDistanceFields=0,PhysicsField=0,VisualizeGlobalDistanceField=0,VisualizeLightingOnProbes=0,ScreenSpaceAO=1,DistanceFieldAO=1,LumenGlobalIllumination=1,VolumetricFog=1,VisualizeSSR=0,VisualizeShadingModels=0,VisualizeSenses=1,LODColoration=0,HLODColoration=0,QuadOverdraw=0,ShaderComplexityWithQuadOverdraw=0,PrimitiveDistanceAccuracy=0,MeshUVDensityAccuracy=0,MaterialTextureScaleAccuracy=0,OutputMaterialTextureScales=0,RequiredTextureResolution=0,VisualizeVirtualTexture=0,WidgetComponents=1,Bones=0,ServerDrawDebug=0,MediaPlanes=1,VREditing=0,OcclusionMeshes=0,VisualizeInstanceOcclusionQueries=0,DisableOcclusionQueries=0,PathTracing=0,RayTracingDebug=0,VisualizeSkyAtmosphere=0,VisualizeLightFunctionAtlas=0,VisualizeCalibrationColor=0,VisualizeCalibrationGrayscale=0,VisualizeCalibrationCustom=0,VisualizePostProcessStack=0,VirtualTexturePrimitives=0,VisualizeVolumetricCloudConservativeDensity=0,VisualizeVolumetricCloudEmptySpaceSkipping=0,VirtualShadowMapPersistentData=1,DebugDrawDistantVirtualSMLights=0,VirtualTextureResidency=1,InputDebugVisualizer=1,LumenScreenTraces=1,LumenDetailTraces=1,LumenGlobalTraces=1,LumenFarFieldTraces=1,LumenSecondaryBounces=1,LumenShortRangeAmbientOcclusion=1,NaniteMeshes=1,NaniteStreamingGeometry=1,VisualizeGPUSkinCache=0,VisualizeLWCComplexity=0,ShaderPrint=1,SceneCaptureCopySceneDepth=1,Cameras=1,Hair=1,AllowPrimitiveAlphaHoldout=1,AlphaInvert=0,SmartObjects=0,GameplayCameras=1,PCGDebug=1",BufferVisualizationMode="",NaniteVisualizationMode="",LumenVisualizationMode="",SubstrateVisualizationMode="",GroomVisualizationMode="",VirtualShadowMapVisualizationMode="",VirtualTextureVisualizationMode="",RayTracingDebugVisualizationMode="",GPUSkinCacheVisualizationMode="",ExposureSettings=(FixedEV100=1.000000,bFixed=False),FOVAngle=90.000000,FarViewPlane=0.000000,bIsRealtime=False,bShowOnScreenStats=True,EnabledStats=,bShowFullToolbar=True,bAllowCinematicControl=True))
PerInstanceSettings=(ConfigName="FourPanes2x2.Viewport 1.Viewport1",ConfigSettings=(ViewportType=LVT_Perspective,PerspViewModeIndex=VMI_Lit,OrthoViewModeIndex=VMI_BrushWireframe,EditorShowFlagsString="PostProcessing=1,Bloom=1,LocalExposure=1,Tonemapper=1,AntiAliasing=1,TemporalAA=1,AmbientCubemap=1,EyeAdaptation=1,VisualizeHDR=0,VisualizeSkyLightIlluminance=0,VisualizeLocalFogVolumes=0,VisualizeLocalExposure=0,LensFlares=1,LensDistortion=1,GlobalIllumination=1,Vignette=1,Grain=1,AmbientOcclusion=1,Decals=1,CameraImperfections=1,OnScreenDebug=1,OverrideDiffuseAndSpecular=0,LightingOnlyOverride=0,ReflectionOverride=0,MaterialNormal=1,MaterialAmbientOcclusion=1,VisualizeBuffer=0,VisualizeNanite=0,VisualizeLumen=0,VisualizeSubstrate=0,VisualizeGroom=0,VisualizeVirtualShadowMap=0,DirectLighting=1,DirectionalLights=1,PointLights=1,SpotLights=1,RectLights=1,ColorGrading=1,VectorFields=0,DepthOfField=1,GBufferHints=0,MotionBlur=0,CompositeDebugPrimitives=1,CompositeEditorPrimitives=1,OpaqueCompositeEditorPrimitives=0,TestImage=0,VisualizeDOF=0,VertexColors=0,PhysicalMaterialMasks=0,Refraction=1,CameraInterpolation=1,SceneColorFringe=1,ToneCurve=1,SeparateTranslucency=1,ScreenPercentage=0,VisualizeMotionBlur=0,VisualizeMotionVectors=0,VisualizeReprojection=0,VisualizeTemporalUpscaler=0,VisualizeTSR=0,MegaLights=1,ReflectionEnvironment=1,VisualizeOutOfBoundsPixels=0,Diffuse=1,Specular=1,SelectionOutline=1,ScreenSpaceReflections=1,LumenReflections=1,ContactShadows=1,RayTracedDistanceFieldShadows=1,CapsuleShadows=1,SubsurfaceScattering=1,VisualizeSSS=0,VolumetricLightmap=1,IndirectLightingCache=1,DebugAI=0,VisLog=1,Navigation=0,GameplayDebug=1,TexturedLightProfiles=1,LightFunctions=1,InstancedStaticMeshes=1,InstancedFoliage=1,HISMCOcclusionBounds=0,HISMCClusterTree=0,VisualizeInstanceUpdates=0,InstancedGrass=1,DynamicShadows=1,Particles=1,Niagara=1,HeterogeneousVolumes=1,SkeletalMeshes=1,BuilderBrush=1,Translucency=1,BillboardSprites=1,LOD=1,LightComplexity=0,ShaderComplexity=0,StationaryLightOverlap=0,LightMapDensity=0,StreamingBounds=0,Constraints=0,MassProperties=0,CameraFrustums=0,AudioRadius=1,ForceFeedbackRadius=1,BSPSplit=0,Brushes=0,Lighting=1,UnlitRichView=1,UnlitViewmode=1,DeferredLighting=1,Editor=1,BSPTriangles=1,LargeVertices=0,Grid=1,Snap=0,MeshEdges=0,Cover=0,Splines=1,Selection=1,VisualizeLevelInstanceEditing=1,ModeWidgets=1,Bounds=0,HitProxies=0,LightInfluences=0,Pivot=1,ShadowFrustums=0,Wireframe=0,Materials=1,StaticMeshes=1,Landscape=1,LightRadius=1,Fog=1,Volumes=1,Game=0,BSP=1,Collision=0,CollisionVisibility=0,CollisionPawn=0,LightShafts=1,PostProcessMaterial=1,Atmosphere=1,Cloud=1,CameraAspectRatioBars=0,CameraSafeFrames=0,TextRender=1,Rendering=1,HMDDistortion=0,StereoRendering=0,DistanceCulledPrimitives=1,VisualizeLightCulling=0,PrecomputedVisibility=1,SkyLighting=1,PreviewShadowsIndicator=1,PrecomputedVisibilityCells=0,VisualizeVolumetricLightmap=0,VolumeLightingSamples=0,Paper2DSprites=1,VisualizeDistanceFieldAO=0,VisualizeMeshDistanceFields=0,PhysicsField=0,VisualizeGlobalDistanceField=0,VisualizeLightingOnProbes=0,ScreenSpaceAO=1,DistanceFieldAO=1,LumenGlobalIllumination=1,VolumetricFog=1,VisualizeSSR=0,VisualizeShadingModels=0,VisualizeSenses=1,LODColoration=0,HLODColoration=0,QuadOverdraw=0,ShaderComplexityWithQuadOverdraw=0,PrimitiveDistanceAccuracy=0,MeshUVDensityAccuracy=0,MaterialTextureScaleAccuracy=0,OutputMaterialTextureScales=0,RequiredTextureResolution=0,VisualizeVirtualTexture=0,WidgetComponents=1,Bones=0,ServerDrawDebug=0,MediaPlanes=1,VREditing=0,OcclusionMeshes=0,VisualizeInstanceOcclusionQueries=0,DisableOcclusionQueries=0,PathTracing=0,RayTracingDebug=0,VisualizeSkyAtmosphere=0,VisualizeLightFunctionAtlas=0,VisualizeCalibrationColor=0,VisualizeCalibrationGrayscale=0,VisualizeCalibrationCustom=0,VisualizePostProcessStack=0,VirtualTexturePrimitives=0,VisualizeVolumetricCloudConservativeDensity=0,VisualizeVolumetricCloudEmptySpaceSkipping=0,VirtualShadowMapPersistentData=1,DebugDrawDistantVirtualSMLights=0,VirtualTextureResidency=1,InputDebugVisualizer=1,LumenScreenTraces=1,LumenDetailTraces=1,LumenGlobalTraces=1,LumenFarFieldTraces=1,LumenSecondaryBounces=1,LumenShortRangeAmbientOcclusion=1,NaniteMeshes=1,NaniteStreamingGeometry=1,VisualizeGPUSkinCache=0,VisualizeLWCComplexity=0,ShaderPrint=1,SceneCaptureCopySceneDepth=1,Cameras=1,Hair=1,AllowPrimitiveAlphaHoldout=1,AlphaInvert=0,SmartObjects=0,GameplayCameras=1,PCGDebug=1",GameShowFlagsString="PostProcessing=1,Bloom=1,LocalExposure=1,Tonemapper=1,AntiAliasing=1,TemporalAA=1,AmbientCubemap=1,EyeAdaptation=1,VisualizeHDR=0,VisualizeSkyLightIlluminance=0,VisualizeLocalFogVolumes=0,VisualizeLocalExposure=0,LensFlares=1,LensDistortion=1,GlobalIllumination=1,Vignette=1,Grain=1,AmbientOcclusion=1,Decals=1,CameraImperfections=1,OnScreenDebug=1,OverrideDiffuseAndSpecular=0,LightingOnlyOverride=0,ReflectionOverride=0,MaterialNormal=1,MaterialAmbientOcclusion=1,VisualizeBuffer=0,VisualizeNanite=0,VisualizeLumen=0,VisualizeSubstrate=0,VisualizeGroom=0,VisualizeVirtualShadowMap=0,DirectLighting=1,DirectionalLights=1,PointLights=1,SpotLights=1,RectLights=1,ColorGrading=1,VectorFields=0,DepthOfField=1,GBufferHints=0,MotionBlur=1,CompositeDebugPrimitives=1,CompositeEditorPrimitives=0,OpaqueCompositeEditorPrimitives=0,TestImage=0,VisualizeDOF=0,VertexColors=0,PhysicalMaterialMasks=0,Refraction=1,CameraInterpolation=1,SceneColorFringe=1,ToneCurve=1,SeparateTranslucency=1,ScreenPercentage=1,VisualizeMotionBlur=0,VisualizeMotionVectors=0,VisualizeReprojection=0,VisualizeTemporalUpscaler=0,VisualizeTSR=0,MegaLights=1,ReflectionEnvironment=1,VisualizeOutOfBoundsPixels=0,Diffuse=1,Specular=1,SelectionOutline=0,ScreenSpaceReflections=1,LumenReflections=1,ContactShadows=1,RayTracedDistanceFieldShadows=1,CapsuleShadows=1,SubsurfaceScattering=1,VisualizeSSS=0,VolumetricLightmap=1,IndirectLightingCache=1,DebugAI=0,VisLog=1,Navigation=0,GameplayDebug=1,TexturedLightProfiles=1,LightFunctions=1,InstancedStaticMeshes=1,InstancedFoliage=1,HISMCOcclusionBounds=0,HISMCClusterTree=0,VisualizeInstanceUpdates=0,InstancedGrass=1,DynamicShadows=1,Particles=1,Niagara=1,HeterogeneousVolumes=1,SkeletalMeshes=1,BuilderBrush=1,Translucency=1,BillboardSprites=1,LOD=1,LightComplexity=0,ShaderComplexity=0,StationaryLightOverlap=0,LightMapDensity=0,StreamingBounds=0,Constraints=0,MassProperties=0,CameraFrustums=0,AudioRadius=0,ForceFeedbackRadius=1,BSPSplit=0,Brushes=0,Lighting=1,UnlitRichView=1,UnlitViewmode=1,DeferredLighting=1,Editor=0,BSPTriangles=1,LargeVertices=0,Grid=0,Snap=0,MeshEdges=0,Cover=0,Splines=0,Selection=0,VisualizeLevelInstanceEditing=1,ModeWidgets=0,Bounds=0,HitProxies=0,LightInfluences=0,Pivot=0,ShadowFrustums=0,Wireframe=0,Materials=1,StaticMeshes=1,Landscape=1,LightRadius=0,Fog=1,Volumes=0,Game=1,BSP=1,Collision=0,CollisionVisibility=0,CollisionPawn=0,LightShafts=1,PostProcessMaterial=1,Atmosphere=1,Cloud=1,CameraAspectRatioBars=0,CameraSafeFrames=0,TextRender=1,Rendering=1,HMDDistortion=0,StereoRendering=0,DistanceCulledPrimitives=0,VisualizeLightCulling=0,PrecomputedVisibility=1,SkyLighting=1,PreviewShadowsIndicator=1,PrecomputedVisibilityCells=0,VisualizeVolumetricLightmap=0,VolumeLightingSamples=0,Paper2DSprites=1,VisualizeDistanceFieldAO=0,VisualizeMeshDistanceFields=0,PhysicsField=0,VisualizeGlobalDistanceField=0,VisualizeLightingOnProbes=0,ScreenSpaceAO=1,DistanceFieldAO=1,LumenGlobalIllumination=1,VolumetricFog=1,VisualizeSSR=0,VisualizeShadingModels=0,VisualizeSenses=1,LODColoration=0,HLODColoration=0,QuadOverdraw=0,ShaderComplexityWithQuadOverdraw=0,PrimitiveDistanceAccuracy=0,MeshUVDensityAccuracy=0,MaterialTextureScaleAccuracy=0,OutputMaterialTextureScales=0,RequiredTextureResolution=0,VisualizeVirtualTexture=0,WidgetComponents=1,Bones=0,ServerDrawDebug=0,MediaPlanes=1,VREditing=0,OcclusionMeshes=0,VisualizeInstanceOcclusionQueries=0,DisableOcclusionQueries=0,PathTracing=0,RayTracingDebug=0,VisualizeSkyAtmosphere=0,VisualizeLightFunctionAtlas=0,VisualizeCalibrationColor=0,VisualizeCalibrationGrayscale=0,VisualizeCalibrationCustom=0,VisualizePostProcessStack=0,VirtualTexturePrimitives=0,VisualizeVolumetricCloudConservativeDensity=0,VisualizeVolumetricCloudEmptySpaceSkipping=0,VirtualShadowMapPersistentData=1,DebugDrawDistantVirtualSMLights=0,VirtualTextureResidency=1,InputDebugVisualizer=1,LumenScreenTraces=1,LumenDetailTraces=1,LumenGlobalTraces=1,LumenFarFieldTraces=1,LumenSecondaryBounces=1,LumenShortRangeAmbientOcclusion=1,NaniteMeshes=1,NaniteStreamingGeometry=1,VisualizeGPUSkinCache=0,VisualizeLWCComplexity=0,ShaderPrint=1,SceneCaptureCopySceneDepth=1,Cameras=1,Hair=1,AllowPrimitiveAlphaHoldout=1,AlphaInvert=0,SmartObjects=0,GameplayCameras=1,PCGDebug=1",BufferVisualizationMode="",NaniteVisualizationMode="",LumenVisualizationMode="",SubstrateVisualizationMode="",GroomVisualizationMode="",VirtualShadowMapVisualizationMode="",VirtualTextureVisualizationMode="",RayTracingDebugVisualizationMode="",GPUSkinCacheVisualizationMode="",ExposureSettings=(FixedEV100=1.000000,bFixed=False),FOVAngle=90.000000,FarViewPlane=0.000000,bIsRealtime=True,bShowOnScreenStats=False,EnabledStats=,bShowFullToolbar=True,bAllowCinematicControl=True))
PerInstanceSettings=(ConfigName="FourPanes2x2.Viewport 1.Viewport2",ConfigSettings=(ViewportType=LVT_OrthoNegativeXZ,PerspViewModeIndex=VMI_Lit,OrthoViewModeIndex=VMI_BrushWireframe,EditorShowFlagsString="PostProcessing=0,Bloom=1,LocalExposure=1,Tonemapper=1,AntiAliasing=1,TemporalAA=1,AmbientCubemap=1,EyeAdaptation=1,VisualizeHDR=0,VisualizeSkyLightIlluminance=0,VisualizeLocalFogVolumes=0,VisualizeLocalExposure=0,LensFlares=1,LensDistortion=1,GlobalIllumination=1,Vignette=1,Grain=1,AmbientOcclusion=1,Decals=1,CameraImperfections=1,OnScreenDebug=1,OverrideDiffuseAndSpecular=0,LightingOnlyOverride=0,ReflectionOverride=0,MaterialNormal=1,MaterialAmbientOcclusion=1,VisualizeBuffer=0,VisualizeNanite=0,VisualizeLumen=0,VisualizeSubstrate=0,VisualizeGroom=0,VisualizeVirtualShadowMap=0,DirectLighting=1,DirectionalLights=1,PointLights=1,SpotLights=1,RectLights=1,ColorGrading=1,VectorFields=0,DepthOfField=1,GBufferHints=0,MotionBlur=0,CompositeDebugPrimitives=1,CompositeEditorPrimitives=1,OpaqueCompositeEditorPrimitives=0,TestImage=0,VisualizeDOF=0,VertexColors=0,PhysicalMaterialMasks=0,Refraction=1,CameraInterpolation=1,SceneColorFringe=1,ToneCurve=1,SeparateTranslucency=1,ScreenPercentage=0,VisualizeMotionBlur=0,VisualizeMotionVectors=0,VisualizeReprojection=0,VisualizeTemporalUpscaler=0,VisualizeTSR=0,MegaLights=1,ReflectionEnvironment=1,VisualizeOutOfBoundsPixels=0,Diffuse=1,Specular=1,SelectionOutline=1,ScreenSpaceReflections=1,LumenReflections=1,ContactShadows=1,RayTracedDistanceFieldShadows=1,CapsuleShadows=1,SubsurfaceScattering=1,VisualizeSSS=0,VolumetricLightmap=1,IndirectLightingCache=1,DebugAI=0,VisLog=1,Navigation=0,GameplayDebug=1,TexturedLightProfiles=1,LightFunctions=1,InstancedStaticMeshes=1,InstancedFoliage=1,HISMCOcclusionBounds=0,HISMCClusterTree=0,VisualizeInstanceUpdates=0,InstancedGrass=1,DynamicShadows=1,Particles=1,Niagara=1,HeterogeneousVolumes=1,SkeletalMeshes=1,BuilderBrush=1,Translucency=1,BillboardSprites=1,LOD=1,LightComplexity=0,ShaderComplexity=0,StationaryLightOverlap=0,LightMapDensity=0,StreamingBounds=0,Constraints=0,MassProperties=0,CameraFrustums=0,AudioRadius=1,ForceFeedbackRadius=1,BSPSplit=0,Brushes=1,Lighting=1,UnlitRichView=1,UnlitViewmode=1,DeferredLighting=1,Editor=1,BSPTriangles=0,LargeVertices=0,Grid=1,Snap=0,MeshEdges=0,Cover=0,Splines=1,Selection=1,VisualizeLevelInstanceEditing=1,ModeWidgets=1,Bounds=0,HitProxies=0,LightInfluences=0,Pivot=1,ShadowFrustums=0,Wireframe=1,Materials=1,StaticMeshes=1,Landscape=1,LightRadius=1,Fog=1,Volumes=1,Game=0,BSP=1,Collision=0,CollisionVisibility=0,CollisionPawn=0,LightShafts=1,PostProcessMaterial=1,Atmosphere=1,Cloud=1,CameraAspectRatioBars=0,CameraSafeFrames=0,TextRender=1,Rendering=1,HMDDistortion=0,StereoRendering=0,DistanceCulledPrimitives=1,VisualizeLightCulling=0,PrecomputedVisibility=1,SkyLighting=1,PreviewShadowsIndicator=1,PrecomputedVisibilityCells=0,VisualizeVolumetricLightmap=0,VolumeLightingSamples=0,Paper2DSprites=1,VisualizeDistanceFieldAO=0,VisualizeMeshDistanceFields=0,PhysicsField=0,VisualizeGlobalDistanceField=0,VisualizeLightingOnProbes=0,ScreenSpaceAO=1,DistanceFieldAO=1,LumenGlobalIllumination=1,VolumetricFog=1,VisualizeSSR=0,VisualizeShadingModels=0,VisualizeSenses=1,LODColoration=0,HLODColoration=0,QuadOverdraw=0,ShaderComplexityWithQuadOverdraw=0,PrimitiveDistanceAccuracy=0,MeshUVDensityAccuracy=0,MaterialTextureScaleAccuracy=0,OutputMaterialTextureScales=0,RequiredTextureResolution=0,VisualizeVirtualTexture=0,WidgetComponents=1,Bones=0,ServerDrawDebug=0,MediaPlanes=1,VREditing=0,OcclusionMeshes=0,VisualizeInstanceOcclusionQueries=0,DisableOcclusionQueries=0,PathTracing=0,RayTracingDebug=0,VisualizeSkyAtmosphere=0,VisualizeLightFunctionAtlas=0,VisualizeCalibrationColor=0,VisualizeCalibrationGrayscale=0,VisualizeCalibrationCustom=0,VisualizePostProcessStack=0,VirtualTexturePrimitives=0,VisualizeVolumetricCloudConservativeDensity=0,VisualizeVolumetricCloudEmptySpaceSkipping=0,VirtualShadowMapPersistentData=1,DebugDrawDistantVirtualSMLights=0,VirtualTextureResidency=1,InputDebugVisualizer=1,LumenScreenTraces=1,LumenDetailTraces=1,LumenGlobalTraces=1,LumenFarFieldTraces=1,LumenSecondaryBounces=1,LumenShortRangeAmbientOcclusion=1,NaniteMeshes=1,NaniteStreamingGeometry=1,VisualizeGPUSkinCache=0,VisualizeLWCComplexity=0,ShaderPrint=1,SceneCaptureCopySceneDepth=1,Cameras=1,Hair=1,AllowPrimitiveAlphaHoldout=1,AlphaInvert=0,SmartObjects=0,GameplayCameras=1,PCGDebug=1",GameShowFlagsString="PostProcessing=0,Bloom=1,LocalExposure=1,Tonemapper=1,AntiAliasing=1,TemporalAA=1,AmbientCubemap=1,EyeAdaptation=1,VisualizeHDR=0,VisualizeSkyLightIlluminance=0,VisualizeLocalFogVolumes=0,VisualizeLocalExposure=0,LensFlares=1,LensDistortion=1,GlobalIllumination=1,Vignette=1,Grain=1,AmbientOcclusion=1,Decals=1,CameraImperfections=1,OnScreenDebug=1,OverrideDiffuseAndSpecular=0,LightingOnlyOverride=0,ReflectionOverride=0,MaterialNormal=1,MaterialAmbientOcclusion=1,VisualizeBuffer=0,VisualizeNanite=0,VisualizeLumen=0,VisualizeSubstrate=0,VisualizeGroom=0,VisualizeVirtualShadowMap=0,DirectLighting=1,DirectionalLights=1,PointLights=1,SpotLights=1,RectLights=1,ColorGrading=1,VectorFields=0,DepthOfField=1,GBufferHints=0,MotionBlur=1,CompositeDebugPrimitives=1,CompositeEditorPrimitives=0,OpaqueCompositeEditorPrimitives=0,TestImage=0,VisualizeDOF=0,VertexColors=0,PhysicalMaterialMasks=0,Refraction=1,CameraInterpolation=1,SceneColorFringe=1,ToneCurve=1,SeparateTranslucency=1,ScreenPercentage=1,VisualizeMotionBlur=0,VisualizeMotionVectors=0,VisualizeReprojection=0,VisualizeTemporalUpscaler=0,VisualizeTSR=0,MegaLights=1,ReflectionEnvironment=1,VisualizeOutOfBoundsPixels=0,Diffuse=1,Specular=1,SelectionOutline=0,ScreenSpaceReflections=1,LumenReflections=1,ContactShadows=1,RayTracedDistanceFieldShadows=1,CapsuleShadows=1,SubsurfaceScattering=1,VisualizeSSS=0,VolumetricLightmap=1,IndirectLightingCache=1,DebugAI=0,VisLog=1,Navigation=0,GameplayDebug=1,TexturedLightProfiles=1,LightFunctions=1,InstancedStaticMeshes=1,InstancedFoliage=1,HISMCOcclusionBounds=0,HISMCClusterTree=0,VisualizeInstanceUpdates=0,InstancedGrass=1,DynamicShadows=1,Particles=1,Niagara=1,HeterogeneousVolumes=1,SkeletalMeshes=1,BuilderBrush=1,Translucency=1,BillboardSprites=1,LOD=1,LightComplexity=0,ShaderComplexity=0,StationaryLightOverlap=0,LightMapDensity=0,StreamingBounds=0,Constraints=0,MassProperties=0,CameraFrustums=0,AudioRadius=0,ForceFeedbackRadius=1,BSPSplit=0,Brushes=1,Lighting=1,UnlitRichView=1,UnlitViewmode=1,DeferredLighting=1,Editor=0,BSPTriangles=0,LargeVertices=0,Grid=0,Snap=0,MeshEdges=0,Cover=0,Splines=0,Selection=0,VisualizeLevelInstanceEditing=1,ModeWidgets=0,Bounds=0,HitProxies=0,LightInfluences=0,Pivot=0,ShadowFrustums=0,Wireframe=1,Materials=1,StaticMeshes=1,Landscape=1,LightRadius=0,Fog=1,Volumes=0,Game=1,BSP=1,Collision=0,CollisionVisibility=0,CollisionPawn=0,LightShafts=1,PostProcessMaterial=1,Atmosphere=1,Cloud=1,CameraAspectRatioBars=0,CameraSafeFrames=0,TextRender=1,Rendering=1,HMDDistortion=0,StereoRendering=0,DistanceCulledPrimitives=0,VisualizeLightCulling=0,PrecomputedVisibility=1,SkyLighting=1,PreviewShadowsIndicator=1,PrecomputedVisibilityCells=0,VisualizeVolumetricLightmap=0,VolumeLightingSamples=0,Paper2DSprites=1,VisualizeDistanceFieldAO=0,VisualizeMeshDistanceFields=0,PhysicsField=0,VisualizeGlobalDistanceField=0,VisualizeLightingOnProbes=0,ScreenSpaceAO=1,DistanceFieldAO=1,LumenGlobalIllumination=1,VolumetricFog=1,VisualizeSSR=0,VisualizeShadingModels=0,VisualizeSenses=1,LODColoration=0,HLODColoration=0,QuadOverdraw=0,ShaderComplexityWithQuadOverdraw=0,PrimitiveDistanceAccuracy=0,MeshUVDensityAccuracy=0,MaterialTextureScaleAccuracy=0,OutputMaterialTextureScales=0,RequiredTextureResolution=0,VisualizeVirtualTexture=0,WidgetComponents=1,Bones=0,ServerDrawDebug=0,MediaPlanes=1,VREditing=0,OcclusionMeshes=0,VisualizeInstanceOcclusionQueries=0,DisableOcclusionQueries=0,PathTracing=0,RayTracingDebug=0,VisualizeSkyAtmosphere=0,VisualizeLightFunctionAtlas=0,VisualizeCalibrationColor=0,VisualizeCalibrationGrayscale=0,VisualizeCalibrationCustom=0,VisualizePostProcessStack=0,VirtualTexturePrimitives=0,VisualizeVolumetricCloudConservativeDensity=0,VisualizeVolumetricCloudEmptySpaceSkipping=0,VirtualShadowMapPersistentData=1,DebugDrawDistantVirtualSMLights=0,VirtualTextureResidency=1,InputDebugVisualizer=1,LumenScreenTraces=1,LumenDetailTraces=1,LumenGlobalTraces=1,LumenFarFieldTraces=1,LumenSecondaryBounces=1,LumenShortRangeAmbientOcclusion=1,NaniteMeshes=1,NaniteStreamingGeometry=1,VisualizeGPUSkinCache=0,VisualizeLWCComplexity=0,ShaderPrint=1,SceneCaptureCopySceneDepth=1,Cameras=1,Hair=1,AllowPrimitiveAlphaHoldout=1,AlphaInvert=0,SmartObjects=0,GameplayCameras=1,PCGDebug=1",BufferVisualizationMode="",NaniteVisualizationMode="",LumenVisualizationMode="",SubstrateVisualizationMode="",GroomVisualizationMode="",VirtualShadowMapVisualizationMode="",VirtualTextureVisualizationMode="",RayTracingDebugVisualizationMode="",GPUSkinCacheVisualizationMode="",ExposureSettings=(FixedEV100=1.000000,bFixed=False),FOVAngle=90.000000,FarViewPlane=0.000000,bIsRealtime=False,bShowOnScreenStats=True,EnabledStats=,bShowFullToolbar=True,bAllowCinematicControl=True))
PerInstanceSettings=(ConfigName="FourPanes2x2.Viewport 1.Viewport3",ConfigSettings=(ViewportType=LVT_OrthoXY,PerspViewModeIndex=VMI_Lit,OrthoViewModeIndex=VMI_BrushWireframe,EditorShowFlagsString="PostProcessing=0,Bloom=1,LocalExposure=1,Tonemapper=1,AntiAliasing=1,TemporalAA=1,AmbientCubemap=1,EyeAdaptation=1,VisualizeHDR=0,VisualizeSkyLightIlluminance=0,VisualizeLocalFogVolumes=0,VisualizeLocalExposure=0,LensFlares=1,LensDistortion=1,GlobalIllumination=1,Vignette=1,Grain=1,AmbientOcclusion=1,Decals=1,CameraImperfections=1,OnScreenDebug=1,OverrideDiffuseAndSpecular=0,LightingOnlyOverride=0,ReflectionOverride=0,MaterialNormal=1,MaterialAmbientOcclusion=1,VisualizeBuffer=0,VisualizeNanite=0,VisualizeLumen=0,VisualizeSubstrate=0,VisualizeGroom=0,VisualizeVirtualShadowMap=0,DirectLighting=1,DirectionalLights=1,PointLights=1,SpotLights=1,RectLights=1,ColorGrading=1,VectorFields=0,DepthOfField=1,GBufferHints=0,MotionBlur=0,CompositeDebugPrimitives=1,CompositeEditorPrimitives=1,OpaqueCompositeEditorPrimitives=0,TestImage=0,VisualizeDOF=0,VertexColors=0,PhysicalMaterialMasks=0,Refraction=1,CameraInterpolation=1,SceneColorFringe=1,ToneCurve=1,SeparateTranslucency=1,ScreenPercentage=0,VisualizeMotionBlur=0,VisualizeMotionVectors=0,VisualizeReprojection=0,VisualizeTemporalUpscaler=0,VisualizeTSR=0,MegaLights=1,ReflectionEnvironment=1,VisualizeOutOfBoundsPixels=0,Diffuse=1,Specular=1,SelectionOutline=1,ScreenSpaceReflections=1,LumenReflections=1,ContactShadows=1,RayTracedDistanceFieldShadows=1,CapsuleShadows=1,SubsurfaceScattering=1,VisualizeSSS=0,VolumetricLightmap=1,IndirectLightingCache=1,DebugAI=0,VisLog=1,Navigation=0,GameplayDebug=1,TexturedLightProfiles=1,LightFunctions=1,InstancedStaticMeshes=1,InstancedFoliage=1,HISMCOcclusionBounds=0,HISMCClusterTree=0,VisualizeInstanceUpdates=0,InstancedGrass=1,DynamicShadows=1,Particles=1,Niagara=1,HeterogeneousVolumes=1,SkeletalMeshes=1,BuilderBrush=1,Translucency=1,BillboardSprites=1,LOD=1,LightComplexity=0,ShaderComplexity=0,StationaryLightOverlap=0,LightMapDensity=0,StreamingBounds=0,Constraints=0,MassProperties=0,CameraFrustums=0,AudioRadius=1,ForceFeedbackRadius=1,BSPSplit=0,Brushes=1,Lighting=1,UnlitRichView=1,UnlitViewmode=1,DeferredLighting=1,Editor=1,BSPTriangles=0,LargeVertices=0,Grid=1,Snap=0,MeshEdges=0,Cover=0,Splines=1,Selection=1,VisualizeLevelInstanceEditing=1,ModeWidgets=1,Bounds=0,HitProxies=0,LightInfluences=0,Pivot=1,ShadowFrustums=0,Wireframe=1,Materials=1,StaticMeshes=1,Landscape=1,LightRadius=1,Fog=1,Volumes=1,Game=0,BSP=1,Collision=0,CollisionVisibility=0,CollisionPawn=0,LightShafts=1,PostProcessMaterial=1,Atmosphere=1,Cloud=1,CameraAspectRatioBars=0,CameraSafeFrames=0,TextRender=1,Rendering=1,HMDDistortion=0,StereoRendering=0,DistanceCulledPrimitives=1,VisualizeLightCulling=0,PrecomputedVisibility=1,SkyLighting=1,PreviewShadowsIndicator=1,PrecomputedVisibilityCells=0,VisualizeVolumetricLightmap=0,VolumeLightingSamples=0,Paper2DSprites=1,VisualizeDistanceFieldAO=0,VisualizeMeshDistanceFields=0,PhysicsField=0,VisualizeGlobalDistanceField=0,VisualizeLightingOnProbes=0,ScreenSpaceAO=1,DistanceFieldAO=1,LumenGlobalIllumination=1,VolumetricFog=1,VisualizeSSR=0,VisualizeShadingModels=0,VisualizeSenses=1,LODColoration=0,HLODColoration=0,QuadOverdraw=0,ShaderComplexityWithQuadOverdraw=0,PrimitiveDistanceAccuracy=0,MeshUVDensityAccuracy=0,MaterialTextureScaleAccuracy=0,OutputMaterialTextureScales=0,RequiredTextureResolution=0,VisualizeVirtualTexture=0,WidgetComponents=1,Bones=0,ServerDrawDebug=0,MediaPlanes=1,VREditing=0,OcclusionMeshes=0,VisualizeInstanceOcclusionQueries=0,DisableOcclusionQueries=0,PathTracing=0,RayTracingDebug=0,VisualizeSkyAtmosphere=0,VisualizeLightFunctionAtlas=0,VisualizeCalibrationColor=0,VisualizeCalibrationGrayscale=0,VisualizeCalibrationCustom=0,VisualizePostProcessStack=0,VirtualTexturePrimitives=0,VisualizeVolumetricCloudConservativeDensity=0,VisualizeVolumetricCloudEmptySpaceSkipping=0,VirtualShadowMapPersistentData=1,DebugDrawDistantVirtualSMLights=0,VirtualTextureResidency=1,InputDebugVisualizer=1,LumenScreenTraces=1,LumenDetailTraces=1,LumenGlobalTraces=1,LumenFarFieldTraces=1,LumenSecondaryBounces=1,LumenShortRangeAmbientOcclusion=1,NaniteMeshes=1,NaniteStreamingGeometry=1,VisualizeGPUSkinCache=0,VisualizeLWCComplexity=0,ShaderPrint=1,SceneCaptureCopySceneDepth=1,Cameras=1,Hair=1,AllowPrimitiveAlphaHoldout=1,AlphaInvert=0,SmartObjects=0,GameplayCameras=1,PCGDebug=1",GameShowFlagsString="PostProcessing=0,Bloom=1,LocalExposure=1,Tonemapper=1,AntiAliasing=1,TemporalAA=1,AmbientCubemap=1,EyeAdaptation=1,VisualizeHDR=0,VisualizeSkyLightIlluminance=0,VisualizeLocalFogVolumes=0,VisualizeLocalExposure=0,LensFlares=1,LensDistortion=1,GlobalIllumination=1,Vignette=1,Grain=1,AmbientOcclusion=1,Decals=1,CameraImperfections=1,OnScreenDebug=1,OverrideDiffuseAndSpecular=0,LightingOnlyOverride=0,ReflectionOverride=0,MaterialNormal=1,MaterialAmbientOcclusion=1,VisualizeBuffer=0,VisualizeNanite=0,VisualizeLumen=0,VisualizeSubstrate=0,VisualizeGroom=0,VisualizeVirtualShadowMap=0,DirectLighting=1,DirectionalLights=1,PointLights=1,SpotLights=1,RectLights=1,ColorGrading=1,VectorFields=0,DepthOfField=1,GBufferHints=0,MotionBlur=1,CompositeDebugPrimitives=1,CompositeEditorPrimitives=0,OpaqueCompositeEditorPrimitives=0,TestImage=0,VisualizeDOF=0,VertexColors=0,PhysicalMaterialMasks=0,Refraction=1,CameraInterpolation=1,SceneColorFringe=1,ToneCurve=1,SeparateTranslucency=1,ScreenPercentage=1,VisualizeMotionBlur=0,VisualizeMotionVectors=0,VisualizeReprojection=0,VisualizeTemporalUpscaler=0,VisualizeTSR=0,MegaLights=1,ReflectionEnvironment=1,VisualizeOutOfBoundsPixels=0,Diffuse=1,Specular=1,SelectionOutline=0,ScreenSpaceReflections=1,LumenReflections=1,ContactShadows=1,RayTracedDistanceFieldShadows=1,CapsuleShadows=1,SubsurfaceScattering=1,VisualizeSSS=0,VolumetricLightmap=1,IndirectLightingCache=1,DebugAI=0,VisLog=1,Navigation=0,GameplayDebug=1,TexturedLightProfiles=1,LightFunctions=1,InstancedStaticMeshes=1,InstancedFoliage=1,HISMCOcclusionBounds=0,HISMCClusterTree=0,VisualizeInstanceUpdates=0,InstancedGrass=1,DynamicShadows=1,Particles=1,Niagara=1,HeterogeneousVolumes=1,SkeletalMeshes=1,BuilderBrush=1,Translucency=1,BillboardSprites=1,LOD=1,LightComplexity=0,ShaderComplexity=0,StationaryLightOverlap=0,LightMapDensity=0,StreamingBounds=0,Constraints=0,MassProperties=0,CameraFrustums=0,AudioRadius=0,ForceFeedbackRadius=1,BSPSplit=0,Brushes=1,Lighting=1,UnlitRichView=1,UnlitViewmode=1,DeferredLighting=1,Editor=0,BSPTriangles=0,LargeVertices=0,Grid=0,Snap=0,MeshEdges=0,Cover=0,Splines=0,Selection=0,VisualizeLevelInstanceEditing=1,ModeWidgets=0,Bounds=0,HitProxies=0,LightInfluences=0,Pivot=0,ShadowFrustums=0,Wireframe=1,Materials=1,StaticMeshes=1,Landscape=1,LightRadius=0,Fog=1,Volumes=0,Game=1,BSP=1,Collision=0,CollisionVisibility=0,CollisionPawn=0,LightShafts=1,PostProcessMaterial=1,Atmosphere=1,Cloud=1,CameraAspectRatioBars=0,CameraSafeFrames=0,TextRender=1,Rendering=1,HMDDistortion=0,StereoRendering=0,DistanceCulledPrimitives=0,VisualizeLightCulling=0,PrecomputedVisibility=1,SkyLighting=1,PreviewShadowsIndicator=1,PrecomputedVisibilityCells=0,VisualizeVolumetricLightmap=0,VolumeLightingSamples=0,Paper2DSprites=1,VisualizeDistanceFieldAO=0,VisualizeMeshDistanceFields=0,PhysicsField=0,VisualizeGlobalDistanceField=0,VisualizeLightingOnProbes=0,ScreenSpaceAO=1,DistanceFieldAO=1,LumenGlobalIllumination=1,VolumetricFog=1,VisualizeSSR=0,VisualizeShadingModels=0,VisualizeSenses=1,LODColoration=0,HLODColoration=0,QuadOverdraw=0,ShaderComplexityWithQuadOverdraw=0,PrimitiveDistanceAccuracy=0,MeshUVDensityAccuracy=0,MaterialTextureScaleAccuracy=0,OutputMaterialTextureScales=0,RequiredTextureResolution=0,VisualizeVirtualTexture=0,WidgetComponents=1,Bones=0,ServerDrawDebug=0,MediaPlanes=1,VREditing=0,OcclusionMeshes=0,VisualizeInstanceOcclusionQueries=0,DisableOcclusionQueries=0,PathTracing=0,RayTracingDebug=0,VisualizeSkyAtmosphere=0,VisualizeLightFunctionAtlas=0,VisualizeCalibrationColor=0,VisualizeCalibrationGrayscale=0,VisualizeCalibrationCustom=0,VisualizePostProcessStack=0,VirtualTexturePrimitives=0,VisualizeVolumetricCloudConservativeDensity=0,VisualizeVolumetricCloudEmptySpaceSkipping=0,VirtualShadowMapPersistentData=1,DebugDrawDistantVirtualSMLights=0,VirtualTextureResidency=1,InputDebugVisualizer=1,LumenScreenTraces=1,LumenDetailTraces=1,LumenGlobalTraces=1,LumenFarFieldTraces=1,LumenSecondaryBounces=1,LumenShortRangeAmbientOcclusion=1,NaniteMeshes=1,NaniteStreamingGeometry=1,VisualizeGPUSkinCache=0,VisualizeLWCComplexity=0,ShaderPrint=1,SceneCaptureCopySceneDepth=1,Cameras=1,Hair=1,AllowPrimitiveAlphaHoldout=1,AlphaInvert=0,SmartObjects=0,GameplayCameras=1,PCGDebug=1",BufferVisualizationMode="",NaniteVisualizationMode="",LumenVisualizationMode="",SubstrateVisualizationMode="",GroomVisualizationMode="",VirtualShadowMapVisualizationMode="",VirtualTextureVisualizationMode="",RayTracingDebugVisualizationMode="",GPUSkinCacheVisualizationMode="",ExposureSettings=(FixedEV100=1.000000,bFixed=False),FOVAngle=90.000000,FarViewPlane=0.000000,bIsRealtime=False,bShowOnScreenStats=True,EnabledStats=,bShowFullToolbar=True,bAllowCinematicControl=True))

[MRU]
MRUItem0=/Game/Maps/RealLeague/RealLeague

[ContentBrowser]
ContentBrowserDrawer.SelectedPaths=/Game
ContentBrowserDrawer.PluginFilters=
ContentBrowserDrawer.SourcesExpanded=True
ContentBrowserDrawer.IsLocked=False
ContentBrowserDrawer.FavoritesAreaExpanded=False
ContentBrowserDrawer.FavoritesSearchAreaExpanded=False
ContentBrowserDrawer.PathAreaExpanded=True
ContentBrowserDrawer.PathSearchAreaExpanded=False
ContentBrowserDrawer.VerticalSplitter.FixedSlotSize0=230
ContentBrowserDrawer.VerticalSplitter.SlotSize1=1
ContentBrowserDrawer.VerticalSplitter.SlotSize2=1
ContentBrowserDrawer.FavoriteSplitter.SlotSize0=0.200000003
ContentBrowserDrawer.FavoriteSplitter.SlotSize1=0.800000012
ContentBrowserDrawer.FavoriteSplitter.SlotSize2=0.400000006
ContentBrowserDrawer.Favorites.SelectedPaths=
FavoritePaths=
ContentBrowserDrawer.SelectedCollections=
ContentBrowserDrawer.ExpandedCollections=
ContentBrowserDrawer.CollectionAreaExpanded=False
ContentBrowserDrawer.CollectionSearchAreaExpanded=False
ContentBrowserDrawer.ThumbnailSizeGrid=2
ContentBrowserDrawer.ThumbnailSizeList=2
ContentBrowserDrawer.ThumbnailSizeCustom=2
ContentBrowserDrawer.ThumbnailSizeColumn=0
ContentBrowserDrawer.CurrentViewType=1
ContentBrowserDrawer.ZoomScale=0
ContentBrowserDrawer.ListViewColumnsManuallyChangedOnce=False
ContentBrowserDrawer.ColumnViewColumnsManuallyChangedOnce=False
ContentBrowserDrawer.JumpMRU=/All/Game
ContentBrowserDrawer.JumpMRU=/All/Game/Scripts
ContentBrowserDrawer.JumpMRU=/All/Game/MaterialSubroutines
ContentBrowserDrawer.JumpMRU=/All/Game/Env/RockArea/Materials
ContentBrowserDrawer.JumpMRU=/All/Game/Env/Tools
ContentBrowserDrawer.JumpMRU=/All/Game/Env/RealLeague/Structures
ContentBrowserDrawer.JumpMRU=/All/Game/Env/RealLeague/Sequences
ContentBrowserDrawer.JumpMRU=/All/Game/Maps/RealLeague
ContentBrowserDrawer.JumpMRU=/All/Game/Maps
AssetDialog.ThumbnailSizeGrid=3
AssetDialog.ThumbnailSizeList=3
AssetDialog.ThumbnailSizeCustom=3
AssetDialog.ThumbnailSizeColumn=0
AssetDialog.CurrentViewType=1
AssetDialog.ZoomScale=0
AssetDialog.ListViewColumnsManuallyChangedOnce=False
AssetDialog.ColumnViewColumnsManuallyChangedOnce=False
AssetDialog.ListHiddenColumns=Class

[EditorStartup]
LastLevel=/Game/Maps/RealLeague/RealLeague

[DetailCategories]
WorldSettings.GameMode=True
WorldSettings.Lightmass=True
WorldSettings.World=True
WorldSettings.Physics=True
WorldSettings.VR=True
WorldSettings.PrecomputedVisibility=True
WorldSettings.Broadphase=True
WorldSettings.HLODSystem=True
WorldSettings.WorldPartitionSetup=True
WorldSettings.Network=True
WorldSettings.Foliage=True
WorldSettings.Landscape=True
WorldSettings.Navigation=True
WorldSettings.Rendering=True
WorldSettings.Nanite=True
WorldSettings.Audio=True
WorldSettings.Tick=True
WorldSettings.Bookmark=True
WorldSettings.Networking=True
FirstPersonHUD_C.Tick=True
FirstPersonHUD_C.Padrão=True
FirstPersonHUD_C.HUD=True
FirstPersonHUD_C.HLOD=True
FirstPersonHUD_C.Collision=True
FirstPersonHUD_C.Physics=True
FirstPersonHUD_C.Events=True
LevelSequenceActor.Actor=True
LevelSequenceActor.Networking=True
LevelSequenceActor.Collision=True
LevelSequenceActor.InstanceData=True
LevelSequenceActor.BindingOverrides=True
LevelSequenceActor.Replication=True
LevelSequenceActor.Aspect Ratio=True
LevelSequenceActor.BurnInOptions=True
LevelSequenceActor.Cinematic=True
LevelSequenceActor.Playback=True
LevelSequenceActor.General=True
LevelSequenceActor.TransformCommon=True
StaticMeshComponent.Navigation=True
StaticMeshComponent.Cooking=True
StaticMeshComponent.Tags=True
StaticMeshComponent.VirtualTexture=True
StaticMeshComponent.HLOD=True
StaticMeshComponent.Rendering=True
StaticMeshComponent.Mesh Painting=True
StaticMeshComponent.Lighting=True
StaticMeshComponent.Collision=True
StaticMeshComponent.Physics=True
StaticMeshComponent.StaticMesh=True
StaticMeshComponent.TransformCommon=True
StaticMeshComponent.Materials=True
Landscape.TransformCommon=True
Landscape.Information=True
Landscape.VirtualTexture=True
Landscape.Landscape=True
Landscape.Rendering=True
Landscape.Nanite=True
Landscape.LOD=True
Landscape.LOD Distribution=True
Landscape.Lighting=True
Landscape.Lightmass=True
Landscape.Collision=True
Landscape.Navigation=True
Landscape.HLOD=True
Landscape.Target Layers=True
Landscape.Replication=True
Landscape.Networking=True
Landscape.Input=True
Landscape.Actor=True
StaticMeshActor.TransformCommon=True
StaticMeshActor.StaticMesh=True
StaticMeshActor.Materials=True
StaticMeshActor.Physics=True
StaticMeshActor.Collision=True
StaticMeshActor.Lighting=True
StaticMeshActor.Mesh Painting=True
StaticMeshActor.Rendering=True
StaticMeshActor.HLOD=True
StaticMeshActor.VirtualTexture=True
StaticMeshActor.Tags=True
StaticMeshActor.Cooking=True
StaticMeshActor.Navigation=True
StaticMeshActor.Replication=True
StaticMeshActor.Networking=True
StaticMeshActor.Actor=True
InstancedFoliageActor.HLOD=True
InstancedFoliageActor.TransformCommon=True
InstancedFoliageActor.Replication=True
InstancedFoliageActor.Collision=True
InstancedFoliageActor.Physics=True
InstancedFoliageActor.Networking=True
InstancedFoliageActor.Input=True
InstancedFoliageActor.Actor=True
SceneComponent.TransformCommon=True
SceneComponent.Rendering=True
SceneComponent.Tags=True
SceneComponent.Activation=True
SceneComponent.Cooking=True
BP_MeshSplines_C.HLOD=True
BP_MeshSplines_C.TransformCommon=True
BP_MeshSplines_C.Padrão=True
BP_MeshSplines_C.Rendering=True
BP_MeshSplines_C.Default=True
BP_MeshSplines_C.Replication=True
BP_MeshSplines_C.Collision=True
BP_MeshSplines_C.Physics=True
BP_MeshSplines_C.Networking=True
BP_MeshSplines_C.Input=True
BP_MeshSplines_C.Actor=True
CullDistanceVolume.TransformCommon=True
CullDistanceVolume.CullDistanceVolume=True
CullDistanceVolume.BrushSettings=True
CullDistanceVolume.HLOD=True
CullDistanceVolume.Replication=True
CullDistanceVolume.Networking=True
CullDistanceVolume.Actor=True
SplineComponent.TransformCommon=True
SplineComponent.HLOD=True
SplineComponent.Tags=True
SplineComponent.Activation=True
SplineComponent.Cooking=True
SplineComponent.Points=True
SplineComponent.Spline=True
SplineComponent.Editor=True
SplineComponent.Navigation=True
SplineComponent.Selected Points=True

[MessageLog]
LastLogListing=AssetCheck

[AssetEditorSubsystem]
CleanShutdown=True
DebuggerAttached=False
RecentAssetEditors=BlueprintEditor
RecentAssetEditors=BlueprintEditor
RecentAssetEditors=

[WindowsEditor]
SuppressMissingAdvancedRenderingRequirementsNotification=True

[DetailPropertyExpansion]
StaticMeshActor="\"Object.StaticMeshActor.StaticMeshComponent.Object.Collision.BodyInstance\" "
PostProcessVolume="\"Object.BrushBuilder.BrushBuilder.Object.BrushSettings\" "
Volume="\"Object.BrushBuilder.BrushBuilder.Object.BrushSettings\" "
Brush="\"Object.BrushBuilder.BrushBuilder.Object.BrushSettings\" "
StaticMeshComponent="\"Object.Collision.BodyInstance\" "
MeshComponent="\"Object.Collision.BodyInstance\" "
PrimitiveComponent=
SceneComponent=
ActorComponent=
Object=
CullDistanceVolume="\"Object.BrushBuilder.BrushBuilder.Object.BrushSettings\" "

[AssetEditorSubsystemRecents]
MRUItem0=/Game/FirstPersonBP/Blueprints/FirstPersonHUD
MRUItem1=/Game/FirstPersonBP/Blueprints/FirstPersonCharacter
MRUItem2=/Game/Maps/RealLeague/RealLeague

[AssetEditorToolkitTabLocation]
/Game/FirstPersonBP/Blueprints/FirstPersonCharacter.FirstPersonCharacter=1
/Game/FirstPersonBP/Blueprints/FirstPersonHUD.FirstPersonHUD=1

[/Script/BlueprintGraph.BlueprintEditorSettings]
bDrawMidpointArrowsInBlueprints=False
bShowGraphInstructionText=True
bHideUnrelatedNodes=False
bShowShortTooltips=True
bShowFunctionParameterIcon=True
bShowFunctionLocalVariableIcon=True
bEnableInputTriggerSupportWarnings=False
bSplitContextTargetSettings=True
bExposeAllMemberComponentFunctions=True
bShowContextualFavorites=False
bExposeDeprecatedFunctions=False
bCompactCallOnMemberNodes=False
bFlattenFavoritesMenus=True
bAutoCastObjectConnections=False
bShowViewportOnSimulate=False
bSpawnDefaultBlueprintNodes=True
bHideConstructionScriptComponentsInDetailsView=True
bHostFindInBlueprintsInGlobalTab=True
bNavigateToNativeFunctionsFromCallNodes=True
bDoubleClickNavigatesToParent=True
bEnableTypePromotion=True
bShowPanelContextMenuForIncompatibleConnections=True
TypePromotionPinDenyList=string
TypePromotionPinDenyList=text
BreakpointReloadMethod=RestoreAll
bEnablePinValueInspectionTooltips=True
bEnableNamespaceEditorFeatures=True
bEnableContextMenuTimeSlicing=True
ContextMenuTimeSlicingThresholdMs=50
bIncludeActionsForSelectedAssetsInContextMenu=False
bLimitAssetActionBindingToSingleSelectionOnly=False
bLoadSelectedAssetsForContextMenuActionBinding=True
bDoNotMarkAllInstancesDirtyOnDefaultValueChange=True
bFavorPureCastNodes=False
SaveOnCompile=SoC_Never
bJumpToNodeErrors=False
bAllowExplicitImpureNodeDisabling=False
bShowActionMenuItemSignatures=False
bBlueprintNodeUniqueNames=False
NodeTemplateCacheCapMB=20.000000
AllowIndexAllBlueprints=LoadOnly
bShowInheritedVariables=False
bAlwaysShowInterfacesInOverrides=True
bShowParentClassInOverrides=True
bShowEmptySections=True
bShowAccessSpecifier=False
Bookmarks=()
PerBlueprintSettings=()
bIncludeCommentNodesInBookmarksTab=True
bShowBookmarksForCurrentDocumentOnlyInTab=False
GraphEditorQuickJumps=()

[RootWindow]
ScreenPosition=X=320.000 Y=156.000
WindowSize=X=1280.000 Y=720.000
InitiallyMaximized=True

[SlateAdditionalLayoutConfig]
Viewport 1.LayoutType=FourPanes2x2
FourPanes2x2.Viewport 1.Percentages0=X=0.500 Y=0.500
FourPanes2x2.Viewport 1.Percentages1=X=0.500 Y=0.500
FourPanes2x2.Viewport 1.Percentages2=X=0.500 Y=0.500
FourPanes2x2.Viewport 1.Percentages3=X=0.500 Y=0.500
FourPanes2x2.Viewport 1.Viewport0.TypeWithinLayout=Default
FourPanes2x2.Viewport 1.Viewport1.TypeWithinLayout=Default
FourPanes2x2.Viewport 1.Viewport2.TypeWithinLayout=Default
FourPanes2x2.Viewport 1.Viewport3.TypeWithinLayout=Default
FourPanes2x2.Viewport 1.bIsMaximized=True
FourPanes2x2.Viewport 1.MaximizedViewport=FourPanes2x2.Viewport 1.Viewport1

[Directories2]
UNR=../../../../../../Auracron/Content/Maps/RealLeague
BRUSH=../../../../../../Auracron/Content/
FBX=../../../../../../Auracron/Content/
FBXAnim=../../../../../../Auracron/Content/
GenericImport=../../../../../../Auracron/Content/
GenericExport=../../../../../../Auracron/Content/
GenericOpen=../../../../../../Auracron/Content/
GenericSave=../../../../../../Auracron/Content/
MeshImportExport=../../../../../../Auracron/Content/
WorldRoot=../../../../../../Auracron/Content/
Level=../../../../../../Auracron/Content/Maps/RealLeague
Project=C:/Program Files/Epic Games/UE_5.6/

[WorldBrowser]
DisplayPaths=False
DisplayActorsCount=False

[/Script/RewindDebuggerVLog.RewindDebuggerVLogSettings]
DisplayVerbosity=4
DisplayCategories=()

[/Script/RewindDebugger.RewindDebuggerSettings]
CameraMode=Replay
bShouldAutoEject=False
bShouldAutoRecordOnPIE=False
PlaybackRate=1.000000
bShowEmptyObjectTracks=False
DebugTargetActor=

[ModuleFileTracking]
StorageServerClient.TimeStamp=2025.09.13-04.03.32
StorageServerClient.LastCompileMethod=Unknown
CookOnTheFly.TimeStamp=2025.09.13-04.03.27
CookOnTheFly.LastCompileMethod=Unknown
StreamingFile.TimeStamp=2025.09.13-04.03.32
StreamingFile.LastCompileMethod=Unknown
NetworkFile.TimeStamp=2025.09.13-04.03.31
NetworkFile.LastCompileMethod=Unknown
PakFile.TimeStamp=2025.09.13-04.03.31
PakFile.LastCompileMethod=Unknown
RSA.TimeStamp=2025.09.13-04.03.31
RSA.LastCompileMethod=Unknown
SandboxFile.TimeStamp=2025.09.13-04.03.31
SandboxFile.LastCompileMethod=Unknown
CoreUObject.TimeStamp=2025.09.13-04.03.28
CoreUObject.LastCompileMethod=Unknown
Engine.TimeStamp=2025.09.13-04.03.29
Engine.LastCompileMethod=Unknown
UniversalObjectLocator.TimeStamp=2025.09.13-04.03.32
UniversalObjectLocator.LastCompileMethod=Unknown
Renderer.TimeStamp=2025.09.13-04.03.31
Renderer.LastCompileMethod=Unknown
AnimGraphRuntime.TimeStamp=2025.09.13-04.03.27
AnimGraphRuntime.LastCompileMethod=Unknown
SlateRHIRenderer.TimeStamp=2025.09.13-04.03.32
SlateRHIRenderer.LastCompileMethod=Unknown
Landscape.TimeStamp=2025.09.13-04.03.30
Landscape.LastCompileMethod=Unknown
RHICore.TimeStamp=2025.09.13-04.03.31
RHICore.LastCompileMethod=Unknown
RenderCore.TimeStamp=2025.09.13-04.03.31
RenderCore.LastCompileMethod=Unknown
TextureCompressor.TimeStamp=2025.09.13-04.03.32
TextureCompressor.LastCompileMethod=Unknown
OpenColorIOWrapper.TimeStamp=2025.09.13-04.03.31
OpenColorIOWrapper.LastCompileMethod=Unknown
Virtualization.TimeStamp=2025.09.13-04.03.33
Virtualization.LastCompileMethod=Unknown
MessageLog.TimeStamp=2025.09.13-04.03.30
MessageLog.LastCompileMethod=Unknown
AudioEditor.TimeStamp=2025.09.13-04.03.27
AudioEditor.LastCompileMethod=Unknown
PropertyEditor.TimeStamp=2025.09.13-04.03.31
PropertyEditor.LastCompileMethod=Unknown
AudioExtensions.TimeStamp=2025.09.13-04.03.27
AudioExtensions.LastCompileMethod=Unknown
AndroidAudioFeatures.TimeStamp=
AndroidAudioFeatures.LastCompileMethod=Unknown
IOSAudioFeatures.TimeStamp=
IOSAudioFeatures.LastCompileMethod=Unknown
LinuxAudioFeatures.TimeStamp=
LinuxAudioFeatures.LastCompileMethod=Unknown
MacAudioFeatures.TimeStamp=
MacAudioFeatures.LastCompileMethod=Unknown
TVOSAudioFeatures.TimeStamp=
TVOSAudioFeatures.LastCompileMethod=Unknown
VisionOSAudioFeatures.TimeStamp=
VisionOSAudioFeatures.LastCompileMethod=Unknown
WindowsAudioFeatures.TimeStamp=
WindowsAudioFeatures.LastCompileMethod=Unknown
SignalProcessing.TimeStamp=2025.09.13-04.03.31
SignalProcessing.LastCompileMethod=Unknown
AudioMixerCore.TimeStamp=2025.09.13-04.03.27
AudioMixerCore.LastCompileMethod=Unknown
AnimationModifiers.TimeStamp=2025.09.13-04.03.27
AnimationModifiers.LastCompileMethod=Unknown
IoStoreOnDemandCore.TimeStamp=2025.09.13-04.03.29
IoStoreOnDemandCore.LastCompileMethod=Unknown
OpusAudioDecoder.TimeStamp=2025.09.13-04.03.31
OpusAudioDecoder.LastCompileMethod=Unknown
VorbisAudioDecoder.TimeStamp=2025.09.13-04.03.33
VorbisAudioDecoder.LastCompileMethod=Unknown
AdpcmAudioDecoder.TimeStamp=2025.09.13-04.03.27
AdpcmAudioDecoder.LastCompileMethod=Unknown
BinkAudioDecoder.TimeStamp=2025.09.13-04.03.27
BinkAudioDecoder.LastCompileMethod=Unknown
RadAudioDecoder.TimeStamp=2025.09.13-04.03.31
RadAudioDecoder.LastCompileMethod=Unknown
TelemetryUtils.TimeStamp=2025.09.13-04.03.32
TelemetryUtils.LastCompileMethod=Unknown
FastBuildController.TimeStamp=2025.09.13-04.06.02
FastBuildController.LastCompileMethod=Unknown
UbaController.TimeStamp=2025.09.13-04.07.10
UbaController.LastCompileMethod=Unknown
XGEController.TimeStamp=2025.09.13-04.07.23
XGEController.LastCompileMethod=Unknown
PerforceSourceControl.TimeStamp=2025.09.13-04.05.19
PerforceSourceControl.LastCompileMethod=Unknown
SourceControl.TimeStamp=2025.09.13-04.03.32
SourceControl.LastCompileMethod=Unknown
PlatformCrypto.TimeStamp=2025.09.13-04.05.51
PlatformCrypto.LastCompileMethod=Unknown
PlatformCryptoTypes.TimeStamp=2025.09.13-04.05.51
PlatformCryptoTypes.LastCompileMethod=Unknown
PlatformCryptoContext.TimeStamp=2025.09.13-04.05.51
PlatformCryptoContext.LastCompileMethod=Unknown
PythonScriptPluginPreload.TimeStamp=2025.09.13-04.05.52
PythonScriptPluginPreload.LastCompileMethod=Unknown
DesktopPlatform.TimeStamp=2025.09.13-04.03.28
DesktopPlatform.LastCompileMethod=Unknown
ChaosCloth.TimeStamp=2025.09.13-04.05.16
ChaosCloth.LastCompileMethod=Unknown
PCGCompute.TimeStamp=2025.09.13-04.06.45
PCGCompute.LastCompileMethod=Unknown
EOSShared.TimeStamp=2025.09.13-04.06.43
EOSShared.LastCompileMethod=Unknown
OnlineServicesInterface.TimeStamp=2025.09.13-04.06.43
OnlineServicesInterface.LastCompileMethod=Unknown
OnlineServicesCommon.TimeStamp=2025.09.13-04.06.43
OnlineServicesCommon.LastCompileMethod=Unknown
OnlineServicesCommonEngineUtils.TimeStamp=2025.09.13-04.06.43
OnlineServicesCommonEngineUtils.LastCompileMethod=Unknown
OnlineSubsystem.TimeStamp=2025.09.13-04.06.44
OnlineSubsystem.LastCompileMethod=Unknown
HTTP.TimeStamp=2025.09.13-04.03.29
HTTP.LastCompileMethod=Unknown
SSL.TimeStamp=2025.09.13-04.03.32
SSL.LastCompileMethod=Unknown
XMPP.TimeStamp=2025.09.13-04.03.33
XMPP.LastCompileMethod=Unknown
WebSockets.TimeStamp=2025.09.13-04.03.33
WebSockets.LastCompileMethod=Unknown
OnlineSubsystemNULL.TimeStamp=2025.09.13-04.06.44
OnlineSubsystemNULL.LastCompileMethod=Unknown
Sockets.TimeStamp=2025.09.13-04.03.32
Sockets.LastCompileMethod=Unknown
OnlineSubsystemUtils.TimeStamp=2025.09.13-04.06.44
OnlineSubsystemUtils.LastCompileMethod=Unknown
OnlineBlueprintSupport.TimeStamp=2025.09.13-04.06.44
OnlineBlueprintSupport.LastCompileMethod=Unknown
AISupportModule.TimeStamp=2025.09.13-04.05.07
AISupportModule.LastCompileMethod=Unknown
ACLPlugin.TimeStamp=2025.09.13-04.05.08
ACLPlugin.LastCompileMethod=Unknown
OptimusSettings.TimeStamp=2025.09.13-04.05.13
OptimusSettings.LastCompileMethod=Unknown
OpenColorIO.TimeStamp=2025.09.13-04.05.17
OpenColorIO.LastCompileMethod=Unknown
PixWinPlugin.TimeStamp=2025.09.13-04.05.19
PixWinPlugin.LastCompileMethod=Unknown
RenderDocPlugin.TimeStamp=2025.09.13-04.05.19
RenderDocPlugin.LastCompileMethod=Unknown
GLTFExporter.TimeStamp=2025.09.13-04.05.25
GLTFExporter.LastCompileMethod=Unknown
VariantManagerContent.TimeStamp=2025.09.13-04.05.26
VariantManagerContent.LastCompileMethod=Unknown
EditorTelemetry.TimeStamp=2025.09.13-04.05.35
EditorTelemetry.LastCompileMethod=Unknown
Analytics.TimeStamp=2025.09.13-04.03.27
Analytics.LastCompileMethod=Unknown
EditorPerformance.TimeStamp=2025.09.13-04.05.35
EditorPerformance.LastCompileMethod=Unknown
StallLogSubsystem.TimeStamp=2025.09.13-04.05.35
StallLogSubsystem.LastCompileMethod=Unknown
NFORDenoise.TimeStamp=2025.09.13-04.05.45
NFORDenoise.LastCompileMethod=Unknown
RuntimeTelemetry.TimeStamp=2025.09.13-04.05.52
RuntimeTelemetry.LastCompileMethod=Unknown
Water.TimeStamp=2025.09.13-04.05.57
Water.LastCompileMethod=Unknown
NiagaraShader.TimeStamp=2025.09.13-04.06.02
NiagaraShader.LastCompileMethod=Unknown
NiagaraVertexFactories.TimeStamp=2025.09.13-04.06.02
NiagaraVertexFactories.LastCompileMethod=Unknown
InterchangeAssets.TimeStamp=2025.09.13-04.06.10
InterchangeAssets.LastCompileMethod=Unknown
ExrReaderGpu.TimeStamp=2025.09.13-04.06.13
ExrReaderGpu.LastCompileMethod=Unknown
WmfMedia.TimeStamp=2025.09.13-04.06.15
WmfMedia.LastCompileMethod=Unknown
Media.TimeStamp=2025.09.13-04.03.30
Media.LastCompileMethod=Unknown
NNEDenoiserShaders.TimeStamp=2025.09.13-04.06.42
NNEDenoiserShaders.LastCompileMethod=Unknown
LauncherChunkInstaller.TimeStamp=2025.09.13-04.06.47
LauncherChunkInstaller.LastCompileMethod=Unknown
ChunkDownloader.TimeStamp=2025.09.13-04.06.48
ChunkDownloader.LastCompileMethod=Unknown
ComputeFramework.TimeStamp=2025.09.13-04.06.48
ComputeFramework.LastCompileMethod=Unknown
ExampleDeviceProfileSelector.TimeStamp=2025.09.13-04.06.48
ExampleDeviceProfileSelector.LastCompileMethod=Unknown
HairStrandsCore.TimeStamp=2025.09.13-04.06.53
HairStrandsCore.LastCompileMethod=Unknown
WindowsDeviceProfileSelector.TimeStamp=2025.09.13-04.07.08
WindowsDeviceProfileSelector.LastCompileMethod=Unknown
CameraCalibrationCore.TimeStamp=2025.09.13-04.07.16
CameraCalibrationCore.LastCompileMethod=Unknown
CompositeCore.TimeStamp=2025.09.13-04.05.35
CompositeCore.LastCompileMethod=Unknown
D3D12RHI.TimeStamp=2025.09.13-04.03.28
D3D12RHI.LastCompileMethod=Unknown
WindowsPlatformFeatures.TimeStamp=2025.09.13-04.03.33
WindowsPlatformFeatures.LastCompileMethod=Unknown
GameplayMediaEncoder.TimeStamp=2025.09.13-04.03.29
GameplayMediaEncoder.LastCompileMethod=Unknown
AVEncoder.TimeStamp=2025.09.13-04.03.27
AVEncoder.LastCompileMethod=Unknown
Chaos.TimeStamp=2025.09.13-04.03.27
Chaos.LastCompileMethod=Unknown
GeometryCore.TimeStamp=2025.09.13-04.03.29
GeometryCore.LastCompileMethod=Unknown
ChaosSolverEngine.TimeStamp=2025.09.13-04.03.27
ChaosSolverEngine.LastCompileMethod=Unknown
DirectoryWatcher.TimeStamp=2025.09.13-04.03.28
DirectoryWatcher.LastCompileMethod=Unknown
Settings.TimeStamp=2025.09.13-04.03.31
Settings.LastCompileMethod=Unknown
InputCore.TimeStamp=2025.09.13-04.03.29
InputCore.LastCompileMethod=Unknown
TargetPlatform.TimeStamp=2025.09.13-04.03.32
TargetPlatform.LastCompileMethod=Unknown
TurnkeySupport.TimeStamp=2025.09.13-04.03.32
TurnkeySupport.LastCompileMethod=Unknown
TextureFormat.TimeStamp=2025.09.13-04.03.32
TextureFormat.LastCompileMethod=Unknown
TextureFormatASTC.TimeStamp=2025.09.13-04.03.32
TextureFormatASTC.LastCompileMethod=Unknown
TextureFormatDXT.TimeStamp=2025.09.13-04.03.32
TextureFormatDXT.LastCompileMethod=Unknown
TextureFormatETC2.TimeStamp=2025.09.13-04.03.32
TextureFormatETC2.LastCompileMethod=Unknown
TextureFormatIntelISPCTexComp.TimeStamp=2025.09.13-04.03.32
TextureFormatIntelISPCTexComp.LastCompileMethod=Unknown
TextureFormatUncompressed.TimeStamp=2025.09.13-04.03.32
TextureFormatUncompressed.LastCompileMethod=Unknown
TextureFormatOodle.TimeStamp=2025.09.13-04.05.19
TextureFormatOodle.LastCompileMethod=Unknown
AndroidTargetPlatform.TimeStamp=2025.09.13-04.03.21
AndroidTargetPlatform.LastCompileMethod=Unknown
AndroidTargetPlatformSettings.TimeStamp=2025.09.13-04.03.21
AndroidTargetPlatformSettings.LastCompileMethod=Unknown
AndroidTargetPlatformControls.TimeStamp=2025.09.13-04.03.21
AndroidTargetPlatformControls.LastCompileMethod=Unknown
IOSTargetPlatform.TimeStamp=2025.09.13-04.03.23
IOSTargetPlatform.LastCompileMethod=Unknown
IOSTargetPlatformSettings.TimeStamp=2025.09.13-04.03.23
IOSTargetPlatformSettings.LastCompileMethod=Unknown
IOSTargetPlatformControls.TimeStamp=2025.09.13-04.03.23
IOSTargetPlatformControls.LastCompileMethod=Unknown
LinuxTargetPlatform.TimeStamp=2025.09.13-04.03.23
LinuxTargetPlatform.LastCompileMethod=Unknown
LinuxTargetPlatformSettings.TimeStamp=2025.09.13-04.03.23
LinuxTargetPlatformSettings.LastCompileMethod=Unknown
LinuxTargetPlatformControls.TimeStamp=2025.09.13-04.03.23
LinuxTargetPlatformControls.LastCompileMethod=Unknown
MacTargetPlatform.TimeStamp=2025.09.13-04.03.30
MacTargetPlatform.LastCompileMethod=Unknown
MacTargetPlatformSettings.TimeStamp=2025.09.13-04.03.30
MacTargetPlatformSettings.LastCompileMethod=Unknown
MacTargetPlatformControls.TimeStamp=2025.09.13-04.03.30
MacTargetPlatformControls.LastCompileMethod=Unknown
TVOSTargetPlatform.TimeStamp=2025.09.13-04.03.23
TVOSTargetPlatform.LastCompileMethod=Unknown
TVOSTargetPlatformSettings.TimeStamp=2025.09.13-04.03.23
TVOSTargetPlatformSettings.LastCompileMethod=Unknown
TVOSTargetPlatformControls.TimeStamp=2025.09.13-04.03.23
TVOSTargetPlatformControls.LastCompileMethod=Unknown
WindowsTargetPlatform.TimeStamp=2025.09.13-04.03.33
WindowsTargetPlatform.LastCompileMethod=Unknown
WindowsTargetPlatformSettings.TimeStamp=2025.09.13-04.03.33
WindowsTargetPlatformSettings.LastCompileMethod=Unknown
WindowsTargetPlatformControls.TimeStamp=2025.09.13-04.03.33
WindowsTargetPlatformControls.LastCompileMethod=Unknown
AudioFormatOPUS.TimeStamp=2025.09.13-04.03.27
AudioFormatOPUS.LastCompileMethod=Unknown
AudioFormatOGG.TimeStamp=2025.09.13-04.03.27
AudioFormatOGG.LastCompileMethod=Unknown
AudioFormatADPCM.TimeStamp=2025.09.13-04.03.27
AudioFormatADPCM.LastCompileMethod=Unknown
AudioFormatBINK.TimeStamp=2025.09.13-04.03.27
AudioFormatBINK.LastCompileMethod=Unknown
AudioFormatRAD.TimeStamp=2025.09.13-04.03.27
AudioFormatRAD.LastCompileMethod=Unknown
ShaderFormatVectorVM.TimeStamp=2025.09.13-04.03.31
ShaderFormatVectorVM.LastCompileMethod=Unknown
ShaderFormatD3D.TimeStamp=2025.09.13-04.03.31
ShaderFormatD3D.LastCompileMethod=Unknown
ShaderFormatOpenGL.TimeStamp=2025.09.13-04.03.31
ShaderFormatOpenGL.LastCompileMethod=Unknown
VulkanShaderFormat.TimeStamp=2025.09.13-04.03.33
VulkanShaderFormat.LastCompileMethod=Unknown
MetalShaderFormat.TimeStamp=2025.09.13-04.03.30
MetalShaderFormat.LastCompileMethod=Unknown
DerivedDataCache.TimeStamp=2025.09.13-04.03.28
DerivedDataCache.LastCompileMethod=Unknown
ShaderPreprocessor.TimeStamp=2025.09.13-04.03.31
ShaderPreprocessor.LastCompileMethod=Unknown
ImageWrapper.TimeStamp=2025.09.13-04.03.29
ImageWrapper.LastCompileMethod=Unknown
NullInstallBundleManager.TimeStamp=2025.09.13-04.03.31
NullInstallBundleManager.LastCompileMethod=Unknown
AssetRegistry.TimeStamp=2025.09.13-04.03.27
AssetRegistry.LastCompileMethod=Unknown
TargetDeviceServices.TimeStamp=2025.09.13-04.03.32
TargetDeviceServices.LastCompileMethod=Unknown
MeshUtilities.TimeStamp=2025.09.13-04.03.30
MeshUtilities.LastCompileMethod=Unknown
MaterialBaking.TimeStamp=2025.09.13-04.03.30
MaterialBaking.LastCompileMethod=Unknown
MeshMergeUtilities.TimeStamp=2025.09.13-04.03.30
MeshMergeUtilities.LastCompileMethod=Unknown
MeshReductionInterface.TimeStamp=2025.09.13-04.03.30
MeshReductionInterface.LastCompileMethod=Unknown
QuadricMeshReduction.TimeStamp=2025.09.13-04.03.31
QuadricMeshReduction.LastCompileMethod=Unknown
ProxyLODMeshReduction.TimeStamp=2025.09.13-04.05.20
ProxyLODMeshReduction.LastCompileMethod=Unknown
SkeletalMeshReduction.TimeStamp=2025.09.13-04.05.52
SkeletalMeshReduction.LastCompileMethod=Unknown
MeshBoneReduction.TimeStamp=2025.09.13-04.03.30
MeshBoneReduction.LastCompileMethod=Unknown
StaticMeshDescription.TimeStamp=2025.09.13-04.03.32
StaticMeshDescription.LastCompileMethod=Unknown
GeometryProcessingInterfaces.TimeStamp=2025.09.13-04.03.29
GeometryProcessingInterfaces.LastCompileMethod=Unknown
NaniteBuilder.TimeStamp=2025.09.13-04.03.31
NaniteBuilder.LastCompileMethod=Unknown
MeshBuilder.TimeStamp=2025.09.13-04.03.30
MeshBuilder.LastCompileMethod=Unknown
KismetCompiler.TimeStamp=2025.09.13-04.03.30
KismetCompiler.LastCompileMethod=Unknown
MovieSceneTools.TimeStamp=2025.09.13-04.03.30
MovieSceneTools.LastCompileMethod=Unknown
Sequencer.TimeStamp=2025.09.13-04.03.31
Sequencer.LastCompileMethod=Unknown
CurveEditor.TimeStamp=2025.09.13-04.03.28
CurveEditor.LastCompileMethod=Unknown
SequencerCore.TimeStamp=2025.09.13-04.03.31
SequencerCore.LastCompileMethod=Unknown
AssetDefinition.TimeStamp=2025.09.13-04.03.27
AssetDefinition.LastCompileMethod=Unknown
Core.TimeStamp=2025.09.13-04.03.27
Core.LastCompileMethod=Unknown
Networking.TimeStamp=2025.09.13-04.03.31
Networking.LastCompileMethod=Unknown
LiveCoding.TimeStamp=2025.09.13-04.03.30
LiveCoding.LastCompileMethod=Unknown
HeadMountedDisplay.TimeStamp=2025.09.13-04.03.29
HeadMountedDisplay.LastCompileMethod=Unknown
SourceCodeAccess.TimeStamp=2025.09.13-04.03.32
SourceCodeAccess.LastCompileMethod=Unknown
Messaging.TimeStamp=2025.09.13-04.03.30
Messaging.LastCompileMethod=Unknown
MRMesh.TimeStamp=2025.09.13-04.03.31
MRMesh.LastCompileMethod=Unknown
UnrealEd.TimeStamp=2025.09.13-04.03.33
UnrealEd.LastCompileMethod=Unknown
LandscapeEditorUtilities.TimeStamp=2025.09.13-04.03.30
LandscapeEditorUtilities.LastCompileMethod=Unknown
SubobjectDataInterface.TimeStamp=2025.09.13-04.03.32
SubobjectDataInterface.LastCompileMethod=Unknown
SlateCore.TimeStamp=2025.09.13-04.03.31
SlateCore.LastCompileMethod=Unknown
Slate.TimeStamp=2025.09.13-04.03.31
Slate.LastCompileMethod=Unknown
SlateReflector.TimeStamp=2025.09.13-04.03.32
SlateReflector.LastCompileMethod=Unknown
EditorStyle.TimeStamp=2025.09.13-04.03.28
EditorStyle.LastCompileMethod=Unknown
UMG.TimeStamp=2025.09.13-04.03.32
UMG.LastCompileMethod=Unknown
UMGEditor.TimeStamp=2025.09.13-04.03.32
UMGEditor.LastCompileMethod=Unknown
AssetTools.TimeStamp=2025.09.13-04.03.27
AssetTools.LastCompileMethod=Unknown
ScriptableEditorWidgets.TimeStamp=2025.09.13-04.03.31
ScriptableEditorWidgets.LastCompileMethod=Unknown
CollisionAnalyzer.TimeStamp=2025.09.13-04.03.27
CollisionAnalyzer.LastCompileMethod=Unknown
WorkspaceMenuStructure.TimeStamp=2025.09.13-04.03.33
WorkspaceMenuStructure.LastCompileMethod=Unknown
FunctionalTesting.TimeStamp=2025.09.13-04.03.29
FunctionalTesting.LastCompileMethod=Unknown
BehaviorTreeEditor.TimeStamp=2025.09.13-04.03.27
BehaviorTreeEditor.LastCompileMethod=Unknown
GameplayTasksEditor.TimeStamp=2025.09.13-04.03.29
GameplayTasksEditor.LastCompileMethod=Unknown
StringTableEditor.TimeStamp=2025.09.13-04.03.32
StringTableEditor.LastCompileMethod=Unknown
VREditor.TimeStamp=2025.09.13-04.03.33
VREditor.LastCompileMethod=Unknown
MaterialEditor.TimeStamp=2025.09.13-04.03.30
MaterialEditor.LastCompileMethod=Unknown
Overlay.TimeStamp=2025.09.13-04.03.31
Overlay.LastCompileMethod=Unknown
OverlayEditor.TimeStamp=2025.09.13-04.03.31
OverlayEditor.LastCompileMethod=Unknown
MediaAssets.TimeStamp=2025.09.13-04.03.30
MediaAssets.LastCompileMethod=Unknown
ClothingSystemRuntimeNv.TimeStamp=2025.09.13-04.03.27
ClothingSystemRuntimeNv.LastCompileMethod=Unknown
ClothingSystemEditor.TimeStamp=2025.09.13-04.03.27
ClothingSystemEditor.LastCompileMethod=Unknown
AnimationDataController.TimeStamp=2025.09.13-04.03.27
AnimationDataController.LastCompileMethod=Unknown
TimeManagement.TimeStamp=2025.09.13-04.03.32
TimeManagement.LastCompileMethod=Unknown
AnimGraph.TimeStamp=2025.09.13-04.03.27
AnimGraph.LastCompileMethod=Unknown
WorldBookmark.TimeStamp=2025.09.13-04.03.33
WorldBookmark.LastCompileMethod=Unknown
WorldPartitionEditor.TimeStamp=2025.09.13-04.03.33
WorldPartitionEditor.LastCompileMethod=Unknown
PacketHandler.TimeStamp=2025.09.13-04.03.31
PacketHandler.LastCompileMethod=Unknown
NetworkReplayStreaming.TimeStamp=2025.09.13-04.03.31
NetworkReplayStreaming.LastCompileMethod=Unknown
MassEntity.TimeStamp=2025.09.13-04.03.30
MassEntity.LastCompileMethod=Unknown
MassEntityTestSuite.TimeStamp=2025.09.13-04.03.30
MassEntityTestSuite.LastCompileMethod=Unknown
BlueprintMaterialTextureNodes.TimeStamp=2025.09.13-04.05.20
BlueprintMaterialTextureNodes.LastCompileMethod=Unknown
WebMMoviePlayer.TimeStamp=2025.09.13-04.07.08
WebMMoviePlayer.LastCompileMethod=Unknown
WindowsMoviePlayer.TimeStamp=2025.09.13-04.07.08
WindowsMoviePlayer.LastCompileMethod=Unknown
EnhancedInput.TimeStamp=2025.09.13-04.05.25
EnhancedInput.LastCompileMethod=Unknown
InputBlueprintNodes.TimeStamp=2025.09.13-04.05.25
InputBlueprintNodes.LastCompileMethod=Unknown
BlueprintGraph.TimeStamp=2025.09.13-04.03.27
BlueprintGraph.LastCompileMethod=Unknown
EnvironmentQueryEditor.TimeStamp=2025.09.13-04.05.07
EnvironmentQueryEditor.LastCompileMethod=Unknown
Paper2D.TimeStamp=2025.09.13-04.05.07
Paper2D.LastCompileMethod=Unknown
AnimationData.TimeStamp=2025.09.13-04.05.08
AnimationData.LastCompileMethod=Unknown
AnimationModifierLibrary.TimeStamp=2025.09.13-04.05.08
AnimationModifierLibrary.LastCompileMethod=Unknown
ControlRig.TimeStamp=2025.09.13-04.05.08
ControlRig.LastCompileMethod=Unknown
Constraints.TimeStamp=2025.09.13-04.03.27
Constraints.LastCompileMethod=Unknown
ControlRigDeveloper.TimeStamp=2025.09.13-04.05.09
ControlRigDeveloper.LastCompileMethod=Unknown
OptimusCore.TimeStamp=2025.09.13-04.05.13
OptimusCore.LastCompileMethod=Unknown
OptimusDeveloper.TimeStamp=2025.09.13-04.05.13
OptimusDeveloper.LastCompileMethod=Unknown
IKRig.TimeStamp=2025.09.13-04.05.13
IKRig.LastCompileMethod=Unknown
IKRigDeveloper.TimeStamp=2025.09.13-04.05.14
IKRigDeveloper.LastCompileMethod=Unknown
RigLogicLib.TimeStamp=2025.09.13-04.05.15
RigLogicLib.LastCompileMethod=Unknown
RigLogicLibTest.TimeStamp=2025.09.13-04.05.15
RigLogicLibTest.LastCompileMethod=Unknown
RigLogicDeveloper.TimeStamp=2025.09.13-04.05.15
RigLogicDeveloper.LastCompileMethod=Unknown
GameplayCameras.TimeStamp=2025.09.13-04.05.16
GameplayCameras.LastCompileMethod=Unknown
EngineCameras.TimeStamp=2025.09.13-04.05.16
EngineCameras.LastCompileMethod=Unknown
OpenColorIOEditor.TimeStamp=2025.09.13-04.05.17
OpenColorIOEditor.LastCompileMethod=Unknown
ToolMenus.TimeStamp=2025.09.13-04.03.32
ToolMenus.LastCompileMethod=Unknown
AnimationSharing.TimeStamp=2025.09.13-04.05.17
AnimationSharing.LastCompileMethod=Unknown
PropertyAccessNode.TimeStamp=2025.09.13-04.05.19
PropertyAccessNode.LastCompileMethod=Unknown
AssetManagerEditor.TimeStamp=2025.09.13-04.05.20
AssetManagerEditor.LastCompileMethod=Unknown
TreeMap.TimeStamp=2025.09.13-04.03.32
TreeMap.LastCompileMethod=Unknown
ContentBrowser.TimeStamp=2025.09.13-04.03.27
ContentBrowser.LastCompileMethod=Unknown
ContentBrowserData.TimeStamp=2025.09.13-04.03.27
ContentBrowserData.LastCompileMethod=Unknown
LevelEditor.TimeStamp=2025.09.13-04.03.30
LevelEditor.LastCompileMethod=Unknown
MainFrame.TimeStamp=2025.09.13-04.03.30
MainFrame.LastCompileMethod=Unknown
HotReload.TimeStamp=2025.09.13-04.03.29
HotReload.LastCompileMethod=Unknown
CommonMenuExtensions.TimeStamp=2025.09.13-04.03.27
CommonMenuExtensions.LastCompileMethod=Unknown
PixelInspectorModule.TimeStamp=2025.09.13-04.03.31
PixelInspectorModule.LastCompileMethod=Unknown
DataValidation.TimeStamp=2025.09.13-04.05.20
DataValidation.LastCompileMethod=Unknown
GameplayTagsEditor.TimeStamp=2025.09.13-04.05.20
GameplayTagsEditor.LastCompileMethod=Unknown
FacialAnimation.TimeStamp=2025.09.13-04.05.20
FacialAnimation.LastCompileMethod=Unknown
FacialAnimationEditor.TimeStamp=2025.09.13-04.05.20
FacialAnimationEditor.LastCompileMethod=Unknown
ChaosCaching.TimeStamp=2025.09.13-04.05.31
ChaosCaching.LastCompileMethod=Unknown
ChaosCachingEditor.TimeStamp=2025.09.13-04.05.31
ChaosCachingEditor.LastCompileMethod=Unknown
TakeRecorder.TimeStamp=2025.09.13-04.07.21
TakeRecorder.LastCompileMethod=Unknown
FullBodyIK.TimeStamp=2025.09.13-04.05.35
FullBodyIK.LastCompileMethod=Unknown
PBIK.TimeStamp=2025.09.13-04.05.35
PBIK.LastCompileMethod=Unknown
PythonScriptPlugin.TimeStamp=2025.09.13-04.05.52
PythonScriptPlugin.LastCompileMethod=Unknown
SequenceNavigator.TimeStamp=2025.09.13-04.05.52
SequenceNavigator.LastCompileMethod=Unknown
NiagaraCore.TimeStamp=2025.09.13-04.06.02
NiagaraCore.LastCompileMethod=Unknown
Niagara.TimeStamp=2025.09.13-04.06.02
Niagara.LastCompileMethod=Unknown
NiagaraEditor.TimeStamp=2025.09.13-04.06.02
NiagaraEditor.LastCompileMethod=Unknown
LevelSequence.TimeStamp=2025.09.13-04.03.30
LevelSequence.LastCompileMethod=Unknown
NiagaraAnimNotifies.TimeStamp=2025.09.13-04.06.02
NiagaraAnimNotifies.LastCompileMethod=Unknown
NiagaraSimCaching.TimeStamp=2025.09.13-04.06.09
NiagaraSimCaching.LastCompileMethod=Unknown
NiagaraSimCachingEditor.TimeStamp=2025.09.13-04.06.09
NiagaraSimCachingEditor.LastCompileMethod=Unknown
InterchangeNodes.TimeStamp=2025.09.13-04.06.10
InterchangeNodes.LastCompileMethod=Unknown
InterchangeFactoryNodes.TimeStamp=2025.09.13-04.06.10
InterchangeFactoryNodes.LastCompileMethod=Unknown
InterchangeImport.TimeStamp=2025.09.13-04.06.10
InterchangeImport.LastCompileMethod=Unknown
InterchangePipelines.TimeStamp=2025.09.13-04.06.10
InterchangePipelines.LastCompileMethod=Unknown
ImgMediaEngine.TimeStamp=2025.09.13-04.06.13
ImgMediaEngine.LastCompileMethod=Unknown
UdpMessaging.TimeStamp=2025.09.13-04.06.15
UdpMessaging.LastCompileMethod=Unknown
TcpMessaging.TimeStamp=2025.09.13-04.06.15
TcpMessaging.LastCompileMethod=Unknown
ActorSequence.TimeStamp=2025.09.13-04.06.41
ActorSequence.LastCompileMethod=Unknown
NNERuntimeORT.TimeStamp=2025.09.13-04.06.43
NNERuntimeORT.LastCompileMethod=Unknown
NNEEditor.TimeStamp=2025.09.13-04.03.31
NNEEditor.LastCompileMethod=Unknown
AudioSynesthesiaCore.TimeStamp=2025.09.13-04.06.48
AudioSynesthesiaCore.LastCompileMethod=Unknown
AudioSynesthesia.TimeStamp=2025.09.13-04.06.48
AudioSynesthesia.LastCompileMethod=Unknown
AudioAnalyzer.TimeStamp=2025.09.13-04.03.27
AudioAnalyzer.LastCompileMethod=Unknown
CableComponent.TimeStamp=2025.09.13-04.06.48
CableComponent.LastCompileMethod=Unknown
CustomMeshComponent.TimeStamp=2025.09.13-04.06.48
CustomMeshComponent.LastCompileMethod=Unknown
DataRegistry.TimeStamp=2025.09.13-04.06.48
DataRegistry.LastCompileMethod=Unknown
DataRegistryEditor.TimeStamp=2025.09.13-04.06.48
DataRegistryEditor.LastCompileMethod=Unknown
GameFeatures.TimeStamp=2025.09.13-04.06.48
GameFeatures.LastCompileMethod=Unknown
GameplayAbilities.TimeStamp=2025.09.13-04.06.48
GameplayAbilities.LastCompileMethod=Unknown
GameplayDebugger.TimeStamp=2025.09.13-04.03.29
GameplayDebugger.LastCompileMethod=Unknown
GameplayAbilitiesEditor.TimeStamp=2025.09.13-04.06.48
GameplayAbilitiesEditor.LastCompileMethod=Unknown
GameplayStateTreeModule.TimeStamp=2025.09.13-04.06.49
GameplayStateTreeModule.LastCompileMethod=Unknown
GameplayInteractionsModule.TimeStamp=2025.09.13-04.06.49
GameplayInteractionsModule.LastCompileMethod=Unknown
MetasoundGraphCore.TimeStamp=2025.09.13-04.07.00
MetasoundGraphCore.LastCompileMethod=Unknown
MetasoundGenerator.TimeStamp=2025.09.13-04.07.00
MetasoundGenerator.LastCompileMethod=Unknown
MetasoundFrontend.TimeStamp=2025.09.13-04.07.00
MetasoundFrontend.LastCompileMethod=Unknown
MetasoundStandardNodes.TimeStamp=2025.09.13-04.07.00
MetasoundStandardNodes.LastCompileMethod=Unknown
MetasoundEngine.TimeStamp=2025.09.13-04.07.00
MetasoundEngine.LastCompileMethod=Unknown
WaveTable.TimeStamp=2025.09.13-04.07.08
WaveTable.LastCompileMethod=Unknown
MetasoundEngineTest.TimeStamp=2025.09.13-04.07.00
MetasoundEngineTest.LastCompileMethod=Unknown
MetasoundEditor.TimeStamp=2025.09.13-04.06.59
MetasoundEditor.LastCompileMethod=Unknown
AudioWidgets.TimeStamp=2025.09.13-04.06.48
AudioWidgets.LastCompileMethod=Unknown
AdvancedWidgets.TimeStamp=2025.09.13-04.03.27
AdvancedWidgets.LastCompileMethod=Unknown
ModularGameplay.TimeStamp=2025.09.13-04.07.01
ModularGameplay.LastCompileMethod=Unknown
NavCorridor.TimeStamp=2025.09.13-04.07.01
NavCorridor.LastCompileMethod=Unknown
MsQuicRuntime.TimeStamp=2025.09.13-04.07.01
MsQuicRuntime.LastCompileMethod=Unknown
ProceduralMeshComponent.TimeStamp=2025.09.13-04.07.05
ProceduralMeshComponent.LastCompileMethod=Unknown
PropertyAccessEditor.TimeStamp=2025.09.13-04.07.05
PropertyAccessEditor.LastCompileMethod=Unknown
SignificanceManager.TimeStamp=2025.09.13-04.07.06
SignificanceManager.LastCompileMethod=Unknown
RigVM.TimeStamp=2025.09.13-04.07.06
RigVM.LastCompileMethod=Unknown
RigVMDeveloper.TimeStamp=2025.09.13-04.07.06
RigVMDeveloper.LastCompileMethod=Unknown
ResonanceAudio.TimeStamp=2025.09.13-04.07.05
ResonanceAudio.LastCompileMethod=Unknown
PropertyBindingUtils.TimeStamp=2025.09.13-04.07.05
PropertyBindingUtils.LastCompileMethod=Unknown
PropertyBindingUtilsTestSuite.TimeStamp=2025.09.13-04.07.05
PropertyBindingUtilsTestSuite.LastCompileMethod=Unknown
SoundFields.TimeStamp=2025.09.13-04.07.06
SoundFields.LastCompileMethod=Unknown
SmartObjectsModule.TimeStamp=2025.09.13-04.07.06
SmartObjectsModule.LastCompileMethod=Unknown
SmartObjectsTestSuite.TimeStamp=2025.09.13-04.07.06
SmartObjectsTestSuite.LastCompileMethod=Unknown
StateTreeModule.TimeStamp=2025.09.13-04.07.06
StateTreeModule.LastCompileMethod=Unknown
TraceServices.TimeStamp=2025.09.13-04.03.32
TraceServices.LastCompileMethod=Unknown
TraceAnalysis.TimeStamp=2025.09.13-04.03.32
TraceAnalysis.LastCompileMethod=Unknown
StateTreeTestSuite.TimeStamp=2025.09.13-04.07.06
StateTreeTestSuite.LastCompileMethod=Unknown
Synthesis.TimeStamp=2025.09.13-04.07.07
Synthesis.LastCompileMethod=Unknown
SQLiteCore.TimeStamp=2025.09.13-04.06.48
SQLiteCore.LastCompileMethod=Unknown
Concert.TimeStamp=2025.09.13-04.05.18
Concert.LastCompileMethod=Unknown
ConcertClient.TimeStamp=2025.09.13-04.05.18
ConcertClient.LastCompileMethod=Unknown
ConcertTransport.TimeStamp=2025.09.13-04.05.18
ConcertTransport.LastCompileMethod=Unknown
ConcertServer.TimeStamp=2025.09.13-04.05.18
ConcertServer.LastCompileMethod=Unknown
ConcertSyncCore.TimeStamp=2025.09.13-04.05.19
ConcertSyncCore.LastCompileMethod=Unknown
ChaosClothEditor.TimeStamp=2025.09.13-04.05.16
ChaosClothEditor.LastCompileMethod=Unknown
ChaosClothAsset.TimeStamp=2025.09.13-04.05.17
ChaosClothAsset.LastCompileMethod=Unknown
ChaosClothAssetEngine.TimeStamp=2025.09.13-04.05.17
ChaosClothAssetEngine.LastCompileMethod=Unknown
ChaosInsightsAnalysis.TimeStamp=2025.09.13-04.05.17
ChaosInsightsAnalysis.LastCompileMethod=Unknown
ChaosInsightsUI.TimeStamp=2025.09.13-04.05.17
ChaosInsightsUI.LastCompileMethod=Unknown
ChaosVD.TimeStamp=2025.09.13-04.05.17
ChaosVD.LastCompileMethod=Unknown
ChaosVDBlueprint.TimeStamp=2025.09.13-04.05.17
ChaosVDBlueprint.LastCompileMethod=Unknown
ChaosVDBuiltInExtensions.TimeStamp=2025.09.13-04.05.17
ChaosVDBuiltInExtensions.LastCompileMethod=Unknown
InputEditor.TimeStamp=2025.09.13-04.05.25
InputEditor.LastCompileMethod=Unknown
IoStoreInsights.TimeStamp=2025.09.13-04.06.11
IoStoreInsights.LastCompileMethod=Unknown
TraceInsights.TimeStamp=2025.09.13-04.03.32
TraceInsights.LastCompileMethod=Unknown
TraceInsightsCore.TimeStamp=2025.09.13-04.03.32
TraceInsightsCore.LastCompileMethod=Unknown
JsonBlueprintUtilities.TimeStamp=2025.09.13-04.06.11
JsonBlueprintUtilities.LastCompileMethod=Unknown
JsonBlueprintGraph.TimeStamp=2025.09.13-04.06.11
JsonBlueprintGraph.LastCompileMethod=Unknown
MassInsightsAnalysis.TimeStamp=2025.09.13-04.06.11
MassInsightsAnalysis.LastCompileMethod=Unknown
MassInsightsUI.TimeStamp=2025.09.13-04.06.11
MassInsightsUI.LastCompileMethod=Unknown
MeshPaintEditorMode.TimeStamp=2025.09.13-04.06.15
MeshPaintEditorMode.LastCompileMethod=Unknown
MeshPaintingToolset.TimeStamp=2025.09.13-04.06.15
MeshPaintingToolset.LastCompileMethod=Unknown
PCG.TimeStamp=2025.09.13-04.06.45
PCG.LastCompileMethod=Unknown
PCGEditor.TimeStamp=2025.09.13-04.06.45
PCGEditor.LastCompileMethod=Unknown
RenderGraphInsights.TimeStamp=2025.09.13-04.06.47
RenderGraphInsights.LastCompileMethod=Unknown
TraceUtilities.TimeStamp=2025.09.13-04.07.10
TraceUtilities.LastCompileMethod=Unknown
EditorTraceUtilities.TimeStamp=2025.09.13-04.07.10
EditorTraceUtilities.LastCompileMethod=Unknown
TraceTools.TimeStamp=2025.09.13-04.03.32
TraceTools.LastCompileMethod=Unknown
WorldMetricsCore.TimeStamp=2025.09.13-04.07.23
WorldMetricsCore.LastCompileMethod=Unknown
WorldMetricsTest.TimeStamp=2025.09.13-04.07.23
WorldMetricsTest.LastCompileMethod=Unknown
CsvMetrics.TimeStamp=2025.09.13-04.07.23
CsvMetrics.LastCompileMethod=Unknown
OnlineBase.TimeStamp=2025.09.13-04.06.43
OnlineBase.LastCompileMethod=Unknown
Paper2DEditor.TimeStamp=2025.09.13-04.05.07
Paper2DEditor.LastCompileMethod=Unknown
PaperSpriteSheetImporter.TimeStamp=2025.09.13-04.05.07
PaperSpriteSheetImporter.LastCompileMethod=Unknown
PaperTiledImporter.TimeStamp=2025.09.13-04.05.07
PaperTiledImporter.LastCompileMethod=Unknown
ACLPluginEditor.TimeStamp=2025.09.13-04.05.08
ACLPluginEditor.LastCompileMethod=Unknown
BlendSpaceMotionAnalysis.TimeStamp=2025.09.13-04.05.08
BlendSpaceMotionAnalysis.LastCompileMethod=Unknown
ControlRigSpline.TimeStamp=2025.09.13-04.05.13
ControlRigSpline.LastCompileMethod=Unknown
GameplayInsights.TimeStamp=2025.09.13-04.05.13
GameplayInsights.LastCompileMethod=Unknown
AnimationBlueprintEditor.TimeStamp=2025.09.13-04.03.27
AnimationBlueprintEditor.LastCompileMethod=Unknown
GameplayInsightsEditor.TimeStamp=2025.09.13-04.05.13
GameplayInsightsEditor.LastCompileMethod=Unknown
RewindDebuggerRuntime.TimeStamp=2025.09.13-04.05.13
RewindDebuggerRuntime.LastCompileMethod=Unknown
RewindDebuggerVLogRuntime.TimeStamp=2025.09.13-04.05.13
RewindDebuggerVLogRuntime.LastCompileMethod=Unknown
MotionWarping.TimeStamp=2025.09.13-04.05.15
MotionWarping.LastCompileMethod=Unknown
RigLogicModule.TimeStamp=2025.09.13-04.05.15
RigLogicModule.LastCompileMethod=Unknown
RigLogicEditor.TimeStamp=2025.09.13-04.05.15
RigLogicEditor.LastCompileMethod=Unknown
TweeningUtils.TimeStamp=2025.09.13-04.05.16
TweeningUtils.LastCompileMethod=Unknown
TweeningUtilsEditor.TimeStamp=2025.09.13-04.05.16
TweeningUtilsEditor.LastCompileMethod=Unknown
SkeletalMeshModelingTools.TimeStamp=2025.09.13-04.05.16
SkeletalMeshModelingTools.LastCompileMethod=Unknown
SkeletalMeshEditor.TimeStamp=2025.09.13-04.03.31
SkeletalMeshEditor.LastCompileMethod=Unknown
GameplayCamerasUncookedOnly.TimeStamp=2025.09.13-04.05.16
GameplayCamerasUncookedOnly.LastCompileMethod=Unknown
AnimationSharingEd.TimeStamp=2025.09.13-04.05.17
AnimationSharingEd.LastCompileMethod=Unknown
OodleNetworkHandlerComponent.TimeStamp=2025.09.13-04.05.17
OodleNetworkHandlerComponent.LastCompileMethod=Unknown
DumpGPUServices.TimeStamp=2025.09.13-04.05.19
DumpGPUServices.LastCompileMethod=Unknown
N10XSourceCodeAccess.TimeStamp=2025.09.13-04.05.19
N10XSourceCodeAccess.LastCompileMethod=Unknown
NamingTokens.TimeStamp=2025.09.13-04.05.19
NamingTokens.LastCompileMethod=Unknown
NamingTokensUncookedOnly.TimeStamp=2025.09.13-04.05.19
NamingTokensUncookedOnly.LastCompileMethod=Unknown
PluginUtils.TimeStamp=2025.09.13-04.05.19
PluginUtils.LastCompileMethod=Unknown
ProjectLauncher.TimeStamp=2025.09.13-04.05.19
ProjectLauncher.LastCompileMethod=Unknown
CommonLaunchExtensions.TimeStamp=2025.09.13-04.05.19
CommonLaunchExtensions.LastCompileMethod=Unknown
RiderSourceCodeAccess.TimeStamp=2025.09.13-04.05.19
RiderSourceCodeAccess.LastCompileMethod=Unknown
UObjectPlugin.TimeStamp=2025.09.13-04.05.19
UObjectPlugin.LastCompileMethod=Unknown
VisualStudioSourceCodeAccess.TimeStamp=2025.09.13-04.05.19
VisualStudioSourceCodeAccess.LastCompileMethod=Unknown
VisualStudioCodeSourceCodeAccess.TimeStamp=2025.09.13-04.05.19
VisualStudioCodeSourceCodeAccess.LastCompileMethod=Unknown
BlueprintHeaderView.TimeStamp=2025.09.13-04.05.20
BlueprintHeaderView.LastCompileMethod=Unknown
ChangelistReview.TimeStamp=2025.09.13-04.05.20
ChangelistReview.LastCompileMethod=Unknown
AssetReferenceRestrictions.TimeStamp=2025.09.13-04.05.20
AssetReferenceRestrictions.LastCompileMethod=Unknown
ColorGradingEditor.TimeStamp=2025.09.13-04.05.20
ColorGradingEditor.LastCompileMethod=Unknown
CurveEditorTools.TimeStamp=2025.09.13-04.05.20
CurveEditorTools.LastCompileMethod=Unknown
ConsoleVariablesEditor.TimeStamp=2025.09.13-04.05.20
ConsoleVariablesEditor.LastCompileMethod=Unknown
ConsoleVariablesEditorRuntime.TimeStamp=2025.09.13-04.05.20
ConsoleVariablesEditorRuntime.LastCompileMethod=Unknown
CryptoKeys.TimeStamp=2025.09.13-04.05.20
CryptoKeys.LastCompileMethod=Unknown
CryptoKeysOpenSSL.TimeStamp=2025.09.13-04.05.20
CryptoKeysOpenSSL.LastCompileMethod=Unknown
EditorDebugTools.TimeStamp=2025.09.13-04.05.20
EditorDebugTools.LastCompileMethod=Unknown
EditorScriptingUtilities.TimeStamp=2025.09.13-04.05.20
EditorScriptingUtilities.LastCompileMethod=Unknown
MaterialAnalyzer.TimeStamp=2025.09.13-04.05.20
MaterialAnalyzer.LastCompileMethod=Unknown
ModelingToolsEditorMode.TimeStamp=2025.09.13-04.05.20
ModelingToolsEditorMode.LastCompileMethod=Unknown
MeshLODToolset.TimeStamp=2025.09.13-04.05.20
MeshLODToolset.LastCompileMethod=Unknown
PluginBrowser.TimeStamp=2025.09.13-04.05.20
PluginBrowser.LastCompileMethod=Unknown
SequencerAnimTools.TimeStamp=2025.09.13-04.05.20
SequencerAnimTools.LastCompileMethod=Unknown
StylusInput.TimeStamp=2025.09.13-04.05.20
StylusInput.LastCompileMethod=Unknown
StylusInputDebugWidget.TimeStamp=2025.09.13-04.05.20
StylusInputDebugWidget.LastCompileMethod=Unknown
SpeedTreeImporter.TimeStamp=2025.09.13-04.05.20
SpeedTreeImporter.LastCompileMethod=Unknown
UMGWidgetPreview.TimeStamp=2025.09.13-04.05.20
UMGWidgetPreview.LastCompileMethod=Unknown
UVEditor.TimeStamp=2025.09.13-04.05.20
UVEditor.LastCompileMethod=Unknown
UVEditorTools.TimeStamp=2025.09.13-04.05.20
UVEditorTools.LastCompileMethod=Unknown
UVEditorToolsEditorOnly.TimeStamp=2025.09.13-04.05.20
UVEditorToolsEditorOnly.LastCompileMethod=Unknown
WorldPartitionHLODUtilities.TimeStamp=2025.09.13-04.05.25
WorldPartitionHLODUtilities.LastCompileMethod=Unknown
VariantManager.TimeStamp=2025.09.13-04.05.26
VariantManager.LastCompileMethod=Unknown
VariantManagerContentEditor.TimeStamp=2025.09.13-04.05.26
VariantManagerContentEditor.LastCompileMethod=Unknown
AbilitySystemGameFeatureActions.TimeStamp=2025.09.13-04.05.26
AbilitySystemGameFeatureActions.LastCompileMethod=Unknown
AdvancedRenamer.TimeStamp=2025.09.13-04.05.26
AdvancedRenamer.LastCompileMethod=Unknown
AutomationUtils.TimeStamp=2025.09.13-04.05.29
AutomationUtils.LastCompileMethod=Unknown
AutomationUtilsEditor.TimeStamp=2025.09.13-04.05.29
AutomationUtilsEditor.LastCompileMethod=Unknown
BackChannel.TimeStamp=2025.09.13-04.05.29
BackChannel.LastCompileMethod=Unknown
FractureEditor.TimeStamp=2025.09.13-04.05.31
FractureEditor.LastCompileMethod=Unknown
ChaosNiagara.TimeStamp=2025.09.13-04.05.35
ChaosNiagara.LastCompileMethod=Unknown
ChaosUserDataPT.TimeStamp=2025.09.13-04.05.35
ChaosUserDataPT.LastCompileMethod=Unknown
ChaosSolverEditor.TimeStamp=2025.09.13-04.05.35
ChaosSolverEditor.LastCompileMethod=Unknown
DataflowAssetTools.TimeStamp=2025.09.13-04.05.35
DataflowAssetTools.LastCompileMethod=Unknown
DataflowEnginePlugin.TimeStamp=2025.09.13-04.05.35
DataflowEnginePlugin.LastCompileMethod=Unknown
DataflowEngine.TimeStamp=2025.09.13-04.03.28
DataflowEngine.LastCompileMethod=Unknown
DataflowSimulation.TimeStamp=2025.09.13-04.03.28
DataflowSimulation.LastCompileMethod=Unknown
DataflowNodes.TimeStamp=2025.09.13-04.05.35
DataflowNodes.LastCompileMethod=Unknown
TedsCore.TimeStamp=2025.09.13-04.05.35
TedsCore.LastCompileMethod=Unknown
TypedElementFramework.TimeStamp=2025.09.13-04.03.32
TypedElementFramework.LastCompileMethod=Unknown
MassEntityEditor.TimeStamp=2025.09.13-04.03.30
MassEntityEditor.LastCompileMethod=Unknown
MassEntityDebugger.TimeStamp=2025.09.13-04.03.30
MassEntityDebugger.LastCompileMethod=Unknown
TedsUI.TimeStamp=2025.09.13-04.05.35
TedsUI.LastCompileMethod=Unknown
TedsAlerts.TimeStamp=2025.09.13-04.05.35
TedsAlerts.LastCompileMethod=Unknown
TedsAssetData.TimeStamp=2025.09.13-04.05.35
TedsAssetData.LastCompileMethod=Unknown
TedsContentBrowser.TimeStamp=2025.09.13-04.05.35
TedsContentBrowser.LastCompileMethod=Unknown
TedsDebugger.TimeStamp=2025.09.13-04.05.35
TedsDebugger.LastCompileMethod=Unknown
TedsOutliner.TimeStamp=2025.09.13-04.05.35
TedsOutliner.LastCompileMethod=Unknown
TedsPropertyEditor.TimeStamp=2025.09.13-04.05.35
TedsPropertyEditor.LastCompileMethod=Unknown
TedsQueryStack.TimeStamp=2025.09.13-04.05.35
TedsQueryStack.LastCompileMethod=Unknown
TedsRevisionControl.TimeStamp=2025.09.13-04.05.35
TedsRevisionControl.LastCompileMethod=Unknown
TedsSettings.TimeStamp=2025.09.13-04.05.35
TedsSettings.LastCompileMethod=Unknown
TedsTableViewer.TimeStamp=2025.09.13-04.05.35
TedsTableViewer.LastCompileMethod=Unknown
TargetingSystem.TimeStamp=2025.09.13-04.05.35
TargetingSystem.LastCompileMethod=Unknown
GeometryCollectionEditor.TimeStamp=2025.09.13-04.05.36
GeometryCollectionEditor.LastCompileMethod=Unknown
GeometryCollectionTracks.TimeStamp=2025.09.13-04.05.36
GeometryCollectionTracks.LastCompileMethod=Unknown
GeometryCollectionSequencer.TimeStamp=2025.09.13-04.05.36
GeometryCollectionSequencer.LastCompileMethod=Unknown
GeometryCollectionEngine.TimeStamp=2025.09.13-04.03.29
GeometryCollectionEngine.LastCompileMethod=Unknown
GeometryCollectionNodes.TimeStamp=2025.09.13-04.05.36
GeometryCollectionNodes.LastCompileMethod=Unknown
GeometryCollectionDepNodes.TimeStamp=2025.09.13-04.05.36
GeometryCollectionDepNodes.LastCompileMethod=Unknown
GeometryFlowCore.TimeStamp=2025.09.13-04.05.36
GeometryFlowCore.LastCompileMethod=Unknown
GeometryFlowMeshProcessing.TimeStamp=2025.09.13-04.05.36
GeometryFlowMeshProcessing.LastCompileMethod=Unknown
GeometryFlowMeshProcessingEditor.TimeStamp=2025.09.13-04.05.36
GeometryFlowMeshProcessingEditor.LastCompileMethod=Unknown
GeometryDataflowNodes.TimeStamp=2025.09.13-04.05.36
GeometryDataflowNodes.LastCompileMethod=Unknown
LocalizableMessage.TimeStamp=2025.09.13-04.05.37
LocalizableMessage.LastCompileMethod=Unknown
LocalizableMessageBlueprint.TimeStamp=2025.09.13-04.05.37
LocalizableMessageBlueprint.LastCompileMethod=Unknown
Landmass.TimeStamp=2025.09.13-04.05.37
Landmass.LastCompileMethod=Unknown
LandmassEditor.TimeStamp=2025.09.13-04.05.37
LandmassEditor.LastCompileMethod=Unknown
MeshModelingToolsExp.TimeStamp=2025.09.13-04.05.38
MeshModelingToolsExp.LastCompileMethod=Unknown
MeshModelingToolsEditorOnlyExp.TimeStamp=2025.09.13-04.05.38
MeshModelingToolsEditorOnlyExp.LastCompileMethod=Unknown
GeometryProcessingAdapters.TimeStamp=2025.09.13-04.05.37
GeometryProcessingAdapters.LastCompileMethod=Unknown
ModelingEditorUI.TimeStamp=2025.09.13-04.05.38
ModelingEditorUI.LastCompileMethod=Unknown
ModelingUI.TimeStamp=2025.09.13-04.05.38
ModelingUI.LastCompileMethod=Unknown
ToolPresetAsset.TimeStamp=2025.09.13-04.05.54
ToolPresetAsset.LastCompileMethod=Unknown
ToolPresetEditor.TimeStamp=2025.09.13-04.05.54
ToolPresetEditor.LastCompileMethod=Unknown
Cascade.TimeStamp=2025.09.13-04.06.02
Cascade.LastCompileMethod=Unknown
NiagaraBlueprintNodes.TimeStamp=2025.09.13-04.06.02
NiagaraBlueprintNodes.LastCompileMethod=Unknown
NiagaraEditorWidgets.TimeStamp=2025.09.13-04.06.02
NiagaraEditorWidgets.LastCompileMethod=Unknown
AlembicImporter.TimeStamp=2025.09.13-04.06.10
AlembicImporter.LastCompileMethod=Unknown
AlembicLibrary.TimeStamp=2025.09.13-04.06.10
AlembicLibrary.LastCompileMethod=Unknown
GeometryCache.TimeStamp=2025.09.13-04.06.49
GeometryCache.LastCompileMethod=Unknown
GeometryCacheEd.TimeStamp=2025.09.13-04.06.49
GeometryCacheEd.LastCompileMethod=Unknown
InterchangeEditor.TimeStamp=2025.09.13-04.06.10
InterchangeEditor.LastCompileMethod=Unknown
InterchangeEditorPipelines.TimeStamp=2025.09.13-04.06.10
InterchangeEditorPipelines.LastCompileMethod=Unknown
InterchangeEditorUtilities.TimeStamp=2025.09.13-04.06.10
InterchangeEditorUtilities.LastCompileMethod=Unknown
GLTFCore.TimeStamp=2025.09.13-04.06.10
GLTFCore.LastCompileMethod=Unknown
InterchangeMessages.TimeStamp=2025.09.13-04.06.10
InterchangeMessages.LastCompileMethod=Unknown
InterchangeExport.TimeStamp=2025.09.13-04.06.10
InterchangeExport.LastCompileMethod=Unknown
InterchangeDispatcher.TimeStamp=2025.09.13-04.06.10
InterchangeDispatcher.LastCompileMethod=Unknown
InterchangeCommon.TimeStamp=2025.09.13-04.06.10
InterchangeCommon.LastCompileMethod=Unknown
InterchangeCommonParser.TimeStamp=2025.09.13-04.06.10
InterchangeCommonParser.LastCompileMethod=Unknown
InterchangeFbxParser.TimeStamp=2025.09.13-04.06.10
InterchangeFbxParser.LastCompileMethod=Unknown
MediaCompositing.TimeStamp=2025.09.13-04.06.13
MediaCompositing.LastCompileMethod=Unknown
ImgMedia.TimeStamp=2025.09.13-04.06.13
ImgMedia.LastCompileMethod=Unknown
MediaPlate.TimeStamp=2025.09.13-04.06.13
MediaPlate.LastCompileMethod=Unknown
MediaPlateEditor.TimeStamp=2025.09.13-04.06.13
MediaPlateEditor.LastCompileMethod=Unknown
MetaHumanSDKEditor.TimeStamp=2025.09.13-04.06.41
MetaHumanSDKEditor.LastCompileMethod=Unknown
MetaHumanSDKRuntime.TimeStamp=2025.09.13-04.06.41
MetaHumanSDKRuntime.LastCompileMethod=Unknown
MovieRenderPipelineCore.TimeStamp=2025.09.13-04.06.41
MovieRenderPipelineCore.LastCompileMethod=Unknown
ImageWriteQueue.TimeStamp=2025.09.13-04.03.29
ImageWriteQueue.LastCompileMethod=Unknown
MovieRenderPipelineSettings.TimeStamp=2025.09.13-04.06.41
MovieRenderPipelineSettings.LastCompileMethod=Unknown
MovieRenderPipelineRenderPasses.TimeStamp=2025.09.13-04.06.41
MovieRenderPipelineRenderPasses.LastCompileMethod=Unknown
MovieRenderPipelineEditor.TimeStamp=2025.09.13-04.06.41
MovieRenderPipelineEditor.LastCompileMethod=Unknown
MovieRenderPipelineMP4Encoder.TimeStamp=2025.09.13-04.06.41
MovieRenderPipelineMP4Encoder.LastCompileMethod=Unknown
UEOpenExrRTTI.TimeStamp=2025.09.13-04.06.41
UEOpenExrRTTI.LastCompileMethod=Unknown
MoviePipelineMaskRenderPass.TimeStamp=2025.09.13-04.06.41
MoviePipelineMaskRenderPass.LastCompileMethod=Unknown
SequencerScripting.TimeStamp=2025.09.13-04.06.41
SequencerScripting.LastCompileMethod=Unknown
SequencerScriptingEditor.TimeStamp=2025.09.13-04.06.41
SequencerScriptingEditor.LastCompileMethod=Unknown
TemplateSequence.TimeStamp=2025.09.13-04.06.41
TemplateSequence.LastCompileMethod=Unknown
NNEDenoiser.TimeStamp=2025.09.13-04.06.42
NNEDenoiser.LastCompileMethod=Unknown
ActorLayerUtilities.TimeStamp=2025.09.13-04.06.47
ActorLayerUtilities.LastCompileMethod=Unknown
ActorLayerUtilitiesEditor.TimeStamp=2025.09.13-04.06.47
ActorLayerUtilitiesEditor.LastCompileMethod=Unknown
AssetTags.TimeStamp=2025.09.13-04.06.48
AssetTags.LastCompileMethod=Unknown
ArchVisCharacter.TimeStamp=2025.09.13-04.06.47
ArchVisCharacter.LastCompileMethod=Unknown
AudioCapture.TimeStamp=2025.09.13-04.06.48
AudioCapture.LastCompileMethod=Unknown
AudioCaptureWasapi.TimeStamp=2025.09.13-04.03.27
AudioCaptureWasapi.LastCompileMethod=Unknown
AudioWidgetsEditor.TimeStamp=2025.09.13-04.06.48
AudioWidgetsEditor.LastCompileMethod=Unknown
ComputeFrameworkEditor.TimeStamp=2025.09.13-04.06.48
ComputeFrameworkEditor.LastCompileMethod=Unknown
GameFeaturesEditor.TimeStamp=2025.09.13-04.06.48
GameFeaturesEditor.LastCompileMethod=Unknown
GeometryAlgorithms.TimeStamp=2025.09.13-04.06.49
GeometryAlgorithms.LastCompileMethod=Unknown
DynamicMesh.TimeStamp=2025.09.13-04.06.49
DynamicMesh.LastCompileMethod=Unknown
MeshFileUtils.TimeStamp=2025.09.13-04.06.49
MeshFileUtils.LastCompileMethod=Unknown
GeometryCacheSequencer.TimeStamp=2025.09.13-04.06.49
GeometryCacheSequencer.LastCompileMethod=Unknown
GeometryCacheStreamer.TimeStamp=2025.09.13-04.06.49
GeometryCacheStreamer.LastCompileMethod=Unknown
GeometryCacheTracks.TimeStamp=2025.09.13-04.06.49
GeometryCacheTracks.LastCompileMethod=Unknown
HairStrandsDeformer.TimeStamp=2025.09.13-04.06.53
HairStrandsDeformer.LastCompileMethod=Unknown
HairStrandsRuntime.TimeStamp=2025.09.13-04.06.53
HairStrandsRuntime.LastCompileMethod=Unknown
HairStrandsEditor.TimeStamp=2025.09.13-04.06.53
HairStrandsEditor.LastCompileMethod=Unknown
HairCardGeneratorFramework.TimeStamp=2025.09.13-04.06.53
HairCardGeneratorFramework.LastCompileMethod=Unknown
HairStrandsDataflow.TimeStamp=2025.09.13-04.06.53
HairStrandsDataflow.LastCompileMethod=Unknown
HairStrandsSolver.TimeStamp=2025.09.13-04.06.53
HairStrandsSolver.LastCompileMethod=Unknown
InputDebugging.TimeStamp=2025.09.13-04.06.59
InputDebugging.LastCompileMethod=Unknown
InputDebuggingEditor.TimeStamp=2025.09.13-04.06.59
InputDebuggingEditor.LastCompileMethod=Unknown
MeshModelingTools.TimeStamp=2025.09.13-04.06.59
MeshModelingTools.LastCompileMethod=Unknown
MeshModelingToolsEditorOnly.TimeStamp=2025.09.13-04.06.59
MeshModelingToolsEditorOnly.LastCompileMethod=Unknown
ModelingComponents.TimeStamp=2025.09.13-04.06.59
ModelingComponents.LastCompileMethod=Unknown
GeometryFramework.TimeStamp=2025.09.13-04.03.29
GeometryFramework.LastCompileMethod=Unknown
ModelingComponentsEditorOnly.TimeStamp=2025.09.13-04.06.59
ModelingComponentsEditorOnly.LastCompileMethod=Unknown
ModelingOperators.TimeStamp=2025.09.13-04.06.59
ModelingOperators.LastCompileMethod=Unknown
ModelingOperatorsEditorOnly.TimeStamp=2025.09.13-04.06.59
ModelingOperatorsEditorOnly.LastCompileMethod=Unknown
SkeletalMeshModifiers.TimeStamp=2025.09.13-04.06.59
SkeletalMeshModifiers.LastCompileMethod=Unknown
ProceduralMeshComponentEditor.TimeStamp=2025.09.13-04.07.05
ProceduralMeshComponentEditor.LastCompileMethod=Unknown
PropertyBindingUtilsEditor.TimeStamp=2025.09.13-04.07.05
PropertyBindingUtilsEditor.LastCompileMethod=Unknown
StateTreeEditorModule.TimeStamp=2025.09.13-04.07.06
StateTreeEditorModule.LastCompileMethod=Unknown
SynthesisEditor.TimeStamp=2025.09.13-04.07.07
SynthesisEditor.LastCompileMethod=Unknown
WorldConditions.TimeStamp=2025.09.13-04.07.08
WorldConditions.LastCompileMethod=Unknown
WorldConditionsTestSuite.TimeStamp=2025.09.13-04.07.08
WorldConditionsTestSuite.LastCompileMethod=Unknown
InterchangeTests.TimeStamp=2025.09.13-04.07.09
InterchangeTests.LastCompileMethod=Unknown
InterchangeTestEditor.TimeStamp=2025.09.13-04.07.09
InterchangeTestEditor.LastCompileMethod=Unknown
CameraCalibrationCoreEditor.TimeStamp=2025.09.13-04.07.16
CameraCalibrationCoreEditor.LastCompileMethod=Unknown
TakeMovieScene.TimeStamp=2025.09.13-04.07.21
TakeMovieScene.LastCompileMethod=Unknown
TakeSequencer.TimeStamp=2025.09.13-04.07.21
TakeSequencer.LastCompileMethod=Unknown
ContentBrowserAssetDataSource.TimeStamp=2025.09.13-04.05.20
ContentBrowserAssetDataSource.LastCompileMethod=Unknown
CollectionManager.TimeStamp=2025.09.13-04.03.27
CollectionManager.LastCompileMethod=Unknown
ContentBrowserFileDataSource.TimeStamp=2025.09.13-04.05.20
ContentBrowserFileDataSource.LastCompileMethod=Unknown
PortableObjectFileDataSource.TimeStamp=2025.09.13-04.05.20
PortableObjectFileDataSource.LastCompileMethod=Unknown
ContentBrowserClassDataSource.TimeStamp=2025.09.13-04.05.20
ContentBrowserClassDataSource.LastCompileMethod=Unknown
XInputDevice.TimeStamp=2025.09.13-04.07.08
XInputDevice.LastCompileMethod=Unknown
LightMixer.TimeStamp=2025.09.13-04.05.20
LightMixer.LastCompileMethod=Unknown
ObjectMixerEditor.TimeStamp=2025.09.13-04.05.20
ObjectMixerEditor.LastCompileMethod=Unknown
ContextualAnimation.TimeStamp=2025.09.13-04.05.26
ContextualAnimation.LastCompileMethod=Unknown
ContextualAnimationEditor.TimeStamp=2025.09.13-04.05.26
ContextualAnimationEditor.LastCompileMethod=Unknown
BaseCharacterFXEditor.TimeStamp=2025.09.13-04.05.35
BaseCharacterFXEditor.LastCompileMethod=Unknown
ConcertSyncClient.TimeStamp=2025.09.13-04.05.18
ConcertSyncClient.LastCompileMethod=Unknown
ConcertSharedSlate.TimeStamp=2025.09.13-04.05.19
ConcertSharedSlate.LastCompileMethod=Unknown
Bridge.TimeStamp=2025.09.13-05.25.24
Bridge.LastCompileMethod=Unknown
MegascansPlugin.TimeStamp=2025.09.13-05.25.24
MegascansPlugin.LastCompileMethod=Unknown
CmdLinkServer.TimeStamp=2025.09.13-04.05.17
CmdLinkServer.LastCompileMethod=Unknown
Fab.TimeStamp=2025.09.13-05.22.58
Fab.LastCompileMethod=Unknown
DataflowEditor.TimeStamp=2025.09.13-04.05.35
DataflowEditor.LastCompileMethod=Unknown
LevelSequenceNavigatorBridge.TimeStamp=2025.09.13-04.05.37
LevelSequenceNavigatorBridge.LastCompileMethod=Unknown
AudioSynesthesiaEditor.TimeStamp=2025.09.13-04.06.48
AudioSynesthesiaEditor.LastCompileMethod=Unknown
TakesCore.TimeStamp=2025.09.13-04.07.21
TakesCore.LastCompileMethod=Unknown
TakeTrackRecorders.TimeStamp=2025.09.13-04.07.21
TakeTrackRecorders.LastCompileMethod=Unknown
TakeRecorderSources.TimeStamp=2025.09.13-04.07.21
TakeRecorderSources.LastCompileMethod=Unknown
CacheTrackRecorder.TimeStamp=2025.09.13-04.07.21
CacheTrackRecorder.LastCompileMethod=Unknown
TakeRecorderNamingTokens.TimeStamp=2025.09.13-04.07.21
TakeRecorderNamingTokens.LastCompileMethod=Unknown
ProfileVisualizer.TimeStamp=2025.09.13-04.03.31
ProfileVisualizer.LastCompileMethod=Unknown
TypedElementRuntime.TimeStamp=2025.09.13-04.03.32
TypedElementRuntime.LastCompileMethod=Unknown
LevelInstanceEditor.TimeStamp=2025.09.13-04.03.30
LevelInstanceEditor.LastCompileMethod=Unknown
ChaosVDRuntime.TimeStamp=2025.09.13-04.03.27
ChaosVDRuntime.LastCompileMethod=Unknown
AIModule.TimeStamp=2025.09.13-04.03.27
AIModule.LastCompileMethod=Unknown
NavigationSystem.TimeStamp=2025.09.13-04.03.31
NavigationSystem.LastCompileMethod=Unknown
AITestSuite.TimeStamp=2025.09.13-04.03.27
AITestSuite.LastCompileMethod=Unknown
MessagingRpc.TimeStamp=2025.09.13-04.03.30
MessagingRpc.LastCompileMethod=Unknown
PortalRpc.TimeStamp=2025.09.13-04.03.31
PortalRpc.LastCompileMethod=Unknown
PortalServices.TimeStamp=2025.09.13-04.03.31
PortalServices.LastCompileMethod=Unknown
AnalyticsET.TimeStamp=2025.09.13-04.03.27
AnalyticsET.LastCompileMethod=Unknown
LauncherPlatform.TimeStamp=2025.09.13-04.03.30
LauncherPlatform.LastCompileMethod=Unknown
AudioMixerXAudio2.TimeStamp=2025.09.13-04.03.27
AudioMixerXAudio2.LastCompileMethod=Unknown
AudioMixer.TimeStamp=2025.09.13-04.03.27
AudioMixer.LastCompileMethod=Unknown
StreamingPauseRendering.TimeStamp=2025.09.13-04.03.32
StreamingPauseRendering.LastCompileMethod=Unknown
MovieScene.TimeStamp=2025.09.13-04.03.30
MovieScene.LastCompileMethod=Unknown
MovieSceneTracks.TimeStamp=2025.09.13-04.03.31
MovieSceneTracks.LastCompileMethod=Unknown
CinematicCamera.TimeStamp=2025.09.13-04.03.27
CinematicCamera.LastCompileMethod=Unknown
SparseVolumeTexture.TimeStamp=2025.09.13-04.03.32
SparseVolumeTexture.LastCompileMethod=Unknown
Documentation.TimeStamp=2025.09.13-04.03.28
Documentation.LastCompileMethod=Unknown
OutputLog.TimeStamp=2025.09.13-04.03.31
OutputLog.LastCompileMethod=Unknown
SourceControlWindows.TimeStamp=2025.09.13-04.03.32
SourceControlWindows.LastCompileMethod=Unknown
SourceControlWindowExtender.TimeStamp=2025.09.13-04.03.32
SourceControlWindowExtender.LastCompileMethod=Unknown
UncontrolledChangelists.TimeStamp=2025.09.13-04.03.32
UncontrolledChangelists.LastCompileMethod=Unknown
ClassViewer.TimeStamp=2025.09.13-04.03.27
ClassViewer.LastCompileMethod=Unknown
StructViewer.TimeStamp=2025.09.13-04.03.32
StructViewer.LastCompileMethod=Unknown
GraphEditor.TimeStamp=2025.09.13-04.03.29
GraphEditor.LastCompileMethod=Unknown
Kismet.TimeStamp=2025.09.13-04.03.30
Kismet.LastCompileMethod=Unknown
KismetWidgets.TimeStamp=2025.09.13-04.03.30
KismetWidgets.LastCompileMethod=Unknown
Persona.TimeStamp=2025.09.13-04.03.31
Persona.LastCompileMethod=Unknown
AdvancedPreviewScene.TimeStamp=2025.09.13-04.03.27
AdvancedPreviewScene.LastCompileMethod=Unknown
PackagesDialog.TimeStamp=2025.09.13-04.03.31
PackagesDialog.LastCompileMethod=Unknown
DetailCustomizations.TimeStamp=2025.09.13-04.03.28
DetailCustomizations.LastCompileMethod=Unknown
ComponentVisualizers.TimeStamp=2025.09.13-04.03.27
ComponentVisualizers.LastCompileMethod=Unknown
Layers.TimeStamp=2025.09.13-04.03.30
Layers.LastCompileMethod=Unknown
AutomationWindow.TimeStamp=2025.09.13-04.03.27
AutomationWindow.LastCompileMethod=Unknown
AutomationController.TimeStamp=2025.09.13-04.03.27
AutomationController.LastCompileMethod=Unknown
DeviceManager.TimeStamp=2025.09.13-04.03.28
DeviceManager.LastCompileMethod=Unknown
ProfilerClient.TimeStamp=
ProfilerClient.LastCompileMethod=Unknown
SessionFrontend.TimeStamp=2025.09.13-04.03.31
SessionFrontend.LastCompileMethod=Unknown
LegacyProjectLauncher.TimeStamp=2025.09.13-04.03.30
LegacyProjectLauncher.LastCompileMethod=Unknown
SettingsEditor.TimeStamp=2025.09.13-04.03.31
SettingsEditor.LastCompileMethod=Unknown
EditorSettingsViewer.TimeStamp=2025.09.13-04.03.28
EditorSettingsViewer.LastCompileMethod=Unknown
InternationalizationSettings.TimeStamp=2025.09.13-04.03.29
InternationalizationSettings.LastCompileMethod=Unknown
ProjectSettingsViewer.TimeStamp=2025.09.13-04.03.31
ProjectSettingsViewer.LastCompileMethod=Unknown
ProjectTargetPlatformEditor.TimeStamp=2025.09.13-04.03.31
ProjectTargetPlatformEditor.LastCompileMethod=Unknown
Blutility.TimeStamp=2025.09.13-04.03.27
Blutility.LastCompileMethod=Unknown
XmlParser.TimeStamp=2025.09.13-04.03.33
XmlParser.LastCompileMethod=Unknown
UndoHistory.TimeStamp=2025.09.13-04.03.32
UndoHistory.LastCompileMethod=Unknown
DeviceProfileEditor.TimeStamp=2025.09.13-04.03.28
DeviceProfileEditor.LastCompileMethod=Unknown
HardwareTargeting.TimeStamp=2025.09.13-04.03.29
HardwareTargeting.LastCompileMethod=Unknown
LocalizationDashboard.TimeStamp=2025.09.13-04.03.30
LocalizationDashboard.LastCompileMethod=Unknown
LocalizationService.TimeStamp=2025.09.13-04.03.30
LocalizationService.LastCompileMethod=Unknown
MergeActors.TimeStamp=2025.09.13-04.03.30
MergeActors.LastCompileMethod=Unknown
InputBindingEditor.TimeStamp=2025.09.13-04.03.29
InputBindingEditor.LastCompileMethod=Unknown
EditorInteractiveToolsFramework.TimeStamp=2025.09.13-04.03.28
EditorInteractiveToolsFramework.LastCompileMethod=Unknown
InteractiveToolsFramework.TimeStamp=2025.09.13-04.03.29
InteractiveToolsFramework.LastCompileMethod=Unknown
StaticMeshEditor.TimeStamp=2025.09.13-04.03.32
StaticMeshEditor.LastCompileMethod=Unknown
EditorFramework.TimeStamp=2025.09.13-04.03.28
EditorFramework.LastCompileMethod=Unknown
EditorConfig.TimeStamp=2025.09.13-04.03.28
EditorConfig.LastCompileMethod=Unknown
DerivedDataEditor.TimeStamp=2025.09.13-04.03.28
DerivedDataEditor.LastCompileMethod=Unknown
ZenEditor.TimeStamp=2025.09.13-04.03.33
ZenEditor.LastCompileMethod=Unknown
CSVtoSVG.TimeStamp=2025.09.13-04.03.28
CSVtoSVG.LastCompileMethod=Unknown
VirtualizationEditor.TimeStamp=2025.09.13-04.03.33
VirtualizationEditor.LastCompileMethod=Unknown
AnimationSettings.TimeStamp=2025.09.13-04.03.27
AnimationSettings.LastCompileMethod=Unknown
GameplayDebuggerEditor.TimeStamp=2025.09.13-04.03.29
GameplayDebuggerEditor.LastCompileMethod=Unknown
RenderResourceViewer.TimeStamp=2025.09.13-04.03.31
RenderResourceViewer.LastCompileMethod=Unknown
UniversalObjectLocatorEditor.TimeStamp=2025.09.13-04.03.32
UniversalObjectLocatorEditor.LastCompileMethod=Unknown
StructUtilsEditor.TimeStamp=2025.09.13-04.03.32
StructUtilsEditor.LastCompileMethod=Unknown
StructUtilsTestSuite.TimeStamp=2025.09.13-04.03.32
StructUtilsTestSuite.LastCompileMethod=Unknown
SVGDistanceField.TimeStamp=2025.09.13-04.03.32
SVGDistanceField.LastCompileMethod=Unknown
DataHierarchyEditor.TimeStamp=2025.09.13-04.03.28
DataHierarchyEditor.LastCompileMethod=Unknown
AndroidRuntimeSettings.TimeStamp=2025.09.13-04.03.21
AndroidRuntimeSettings.LastCompileMethod=Unknown
IOSRuntimeSettings.TimeStamp=2025.09.13-04.03.23
IOSRuntimeSettings.LastCompileMethod=Unknown
MacPlatformEditor.TimeStamp=2025.09.13-04.03.30
MacPlatformEditor.LastCompileMethod=Unknown
WindowsPlatformEditor.TimeStamp=2025.09.13-04.03.33
WindowsPlatformEditor.LastCompileMethod=Unknown
AndroidPlatformEditor.TimeStamp=2025.09.13-04.03.21
AndroidPlatformEditor.LastCompileMethod=Unknown
AndroidDeviceDetection.TimeStamp=2025.09.13-04.03.21
AndroidDeviceDetection.LastCompileMethod=Unknown
PIEPreviewDeviceProfileSelector.TimeStamp=2025.09.13-04.03.31
PIEPreviewDeviceProfileSelector.LastCompileMethod=Unknown
IOSPlatformEditor.TimeStamp=2025.09.13-04.03.23
IOSPlatformEditor.LastCompileMethod=Unknown
LogVisualizer.TimeStamp=2025.09.13-04.03.30
LogVisualizer.LastCompileMethod=Unknown
WidgetRegistration.TimeStamp=2025.09.13-04.03.33
WidgetRegistration.LastCompileMethod=Unknown
ClothPainter.TimeStamp=2025.09.13-04.03.27
ClothPainter.LastCompileMethod=Unknown
ViewportInteraction.TimeStamp=2025.09.13-04.03.33
ViewportInteraction.LastCompileMethod=Unknown
EditorWidgets.TimeStamp=2025.09.13-04.03.28
EditorWidgets.LastCompileMethod=Unknown
ViewportSnapping.TimeStamp=2025.09.13-04.03.33
ViewportSnapping.LastCompileMethod=Unknown
PlacementMode.TimeStamp=2025.09.13-04.03.31
PlacementMode.LastCompileMethod=Unknown
MeshPaint.TimeStamp=2025.09.13-04.03.30
MeshPaint.LastCompileMethod=Unknown
SessionServices.TimeStamp=2025.09.13-04.03.31
SessionServices.LastCompileMethod=Unknown
SmartSnapping.TimeStamp=2025.09.13-04.05.07
SmartSnapping.LastCompileMethod=Unknown
ControlRigEditor.TimeStamp=2025.09.13-04.05.09
ControlRigEditor.LastCompileMethod=Unknown
RewindDebugger.TimeStamp=2025.09.13-04.05.13
RewindDebugger.LastCompileMethod=Unknown
RewindDebuggerVLog.TimeStamp=2025.09.13-04.05.13
RewindDebuggerVLog.LastCompileMethod=Unknown
OptimusEditor.TimeStamp=2025.09.13-04.05.13
OptimusEditor.LastCompileMethod=Unknown
IKRigEditor.TimeStamp=2025.09.13-04.05.14
IKRigEditor.LastCompileMethod=Unknown
CameraShakePreviewer.TimeStamp=2025.09.13-04.05.16
CameraShakePreviewer.LastCompileMethod=Unknown
GameplayCamerasEditor.TimeStamp=2025.09.13-04.05.16
GameplayCamerasEditor.LastCompileMethod=Unknown
EngineAssetDefinitions.TimeStamp=2025.09.13-04.05.20
EngineAssetDefinitions.LastCompileMethod=Unknown
GeometryMode.TimeStamp=2025.09.13-04.05.20
GeometryMode.LastCompileMethod=Unknown
BspMode.TimeStamp=2025.09.13-04.05.20
BspMode.LastCompileMethod=Unknown
TextureAlignMode.TimeStamp=2025.09.13-04.05.20
TextureAlignMode.LastCompileMethod=Unknown
CharacterAI.TimeStamp=2025.09.13-04.05.35
CharacterAI.LastCompileMethod=Unknown
FractureEngine.TimeStamp=2025.09.13-04.05.35
FractureEngine.LastCompileMethod=Unknown
PlanarCut.TimeStamp=2025.09.13-04.05.51
PlanarCut.LastCompileMethod=Unknown
WaterEditor.TimeStamp=2025.09.13-04.05.57
WaterEditor.LastCompileMethod=Unknown
AndroidMediaEditor.TimeStamp=2025.09.13-04.06.12
AndroidMediaEditor.LastCompileMethod=Unknown
AndroidMediaFactory.TimeStamp=2025.09.13-04.06.12
AndroidMediaFactory.LastCompileMethod=Unknown
AvfMediaEditor.TimeStamp=2025.09.13-04.06.12
AvfMediaEditor.LastCompileMethod=Unknown
AvfMediaFactory.TimeStamp=2025.09.13-04.06.12
AvfMediaFactory.LastCompileMethod=Unknown
MediaCompositingEditor.TimeStamp=2025.09.13-04.06.13
MediaCompositingEditor.LastCompileMethod=Unknown
SequenceRecorder.TimeStamp=2025.09.13-04.03.31
SequenceRecorder.LastCompileMethod=Unknown
ImgMediaEditor.TimeStamp=2025.09.13-04.06.13
ImgMediaEditor.LastCompileMethod=Unknown
ImgMediaFactory.TimeStamp=2025.09.13-04.06.13
ImgMediaFactory.LastCompileMethod=Unknown
OpenExrWrapper.TimeStamp=2025.09.13-04.06.13
OpenExrWrapper.LastCompileMethod=Unknown
MediaPlayerEditor.TimeStamp=2025.09.13-04.06.13
MediaPlayerEditor.LastCompileMethod=Unknown
WmfMediaEditor.TimeStamp=2025.09.13-04.06.15
WmfMediaEditor.LastCompileMethod=Unknown
WmfMediaFactory.TimeStamp=2025.09.13-04.06.15
WmfMediaFactory.LastCompileMethod=Unknown
WebMMedia.TimeStamp=2025.09.13-04.06.15
WebMMedia.LastCompileMethod=Unknown
WebMMediaEditor.TimeStamp=2025.09.13-04.06.15
WebMMediaEditor.LastCompileMethod=Unknown
WebMMediaFactory.TimeStamp=2025.09.13-04.06.15
WebMMediaFactory.LastCompileMethod=Unknown
ActorSequenceEditor.TimeStamp=2025.09.13-04.06.41
ActorSequenceEditor.LastCompileMethod=Unknown
LevelSequenceEditor.TimeStamp=2025.09.13-04.06.41
LevelSequenceEditor.LastCompileMethod=Unknown
TemplateSequenceEditor.TimeStamp=2025.09.13-04.06.41
TemplateSequenceEditor.LastCompileMethod=Unknown
AudioCaptureEditor.TimeStamp=2025.09.13-04.06.48
AudioCaptureEditor.LastCompileMethod=Unknown
HDRIBackdrop.TimeStamp=2025.09.13-04.06.56
HDRIBackdrop.LastCompileMethod=Unknown
RigVMEditor.TimeStamp=2025.09.13-04.07.06
RigVMEditor.LastCompileMethod=Unknown
ResonanceAudioEditor.TimeStamp=2025.09.13-04.07.05
ResonanceAudioEditor.LastCompileMethod=Unknown
SmartObjectsEditorModule.TimeStamp=2025.09.13-04.07.06
SmartObjectsEditorModule.LastCompileMethod=Unknown
WaveTableEditor.TimeStamp=2025.09.13-04.07.08
WaveTableEditor.LastCompileMethod=Unknown
WorldConditionsEditor.TimeStamp=2025.09.13-04.07.08
WorldConditionsEditor.LastCompileMethod=Unknown
ActorPickerMode.TimeStamp=2025.09.13-04.03.27
ActorPickerMode.LastCompileMethod=Unknown
SceneDepthPickerMode.TimeStamp=2025.09.13-04.03.31
SceneDepthPickerMode.LastCompileMethod=Unknown
LandscapeEditor.TimeStamp=2025.09.13-04.03.30
LandscapeEditor.LastCompileMethod=Unknown
FoliageEdit.TimeStamp=2025.09.13-04.03.29
FoliageEdit.LastCompileMethod=Unknown
VirtualTexturingEditor.TimeStamp=2025.09.13-04.03.33
VirtualTexturingEditor.LastCompileMethod=Unknown
AutomationWorker.TimeStamp=2025.09.13-04.03.27
AutomationWorker.LastCompileMethod=Unknown
SequenceRecorderSections.TimeStamp=2025.09.13-04.03.31
SequenceRecorderSections.LastCompileMethod=Unknown
StatsViewer.TimeStamp=2025.09.13-04.03.32
StatsViewer.LastCompileMethod=Unknown
GameProjectGeneration.TimeStamp=2025.09.13-04.03.29
GameProjectGeneration.LastCompileMethod=Unknown
UnsavedAssetsTracker.TimeStamp=2025.09.13-04.03.33
UnsavedAssetsTracker.LastCompileMethod=Unknown
StatusBar.TimeStamp=2025.09.13-04.03.32
StatusBar.LastCompileMethod=Unknown
SceneOutliner.TimeStamp=2025.09.13-04.03.31
SceneOutliner.LastCompileMethod=Unknown
SubobjectEditor.TimeStamp=2025.09.13-04.03.32
SubobjectEditor.LastCompileMethod=Unknown
WorldBrowser.TimeStamp=2025.09.13-04.03.33
WorldBrowser.LastCompileMethod=Unknown
DataLayerEditor.TimeStamp=2025.09.13-04.03.28
DataLayerEditor.LastCompileMethod=Unknown
AddContentDialog.TimeStamp=2025.09.13-04.03.27
AddContentDialog.LastCompileMethod=Unknown
WidgetCarousel.TimeStamp=2025.09.13-04.03.33
WidgetCarousel.LastCompileMethod=Unknown
HierarchicalLODOutliner.TimeStamp=2025.09.13-04.03.29
HierarchicalLODOutliner.LastCompileMethod=Unknown
AndroidDeviceProfileSelector.TimeStamp=
AndroidDeviceProfileSelector.LastCompileMethod=Unknown
Voice.TimeStamp=2025.09.13-04.03.33
Voice.LastCompileMethod=Unknown
MovieSceneCapture.TimeStamp=2025.09.13-04.03.30
MovieSceneCapture.LastCompileMethod=Unknown
WebBrowser.TimeStamp=2025.09.13-04.03.33
WebBrowser.LastCompileMethod=Unknown

[Python]
LastDirectory=
RecentsFiles=C:/Program Files/Epic Games/UE_5.6/Engine/Plugins/Animation/ControlRig/Content/Python/init_unreal.py
RecentsFiles=C:/Program Files/Epic Games/UE_5.6/Engine/Plugins/MovieScene/MovieRenderPipeline/Content/Python/init_unreal.py

